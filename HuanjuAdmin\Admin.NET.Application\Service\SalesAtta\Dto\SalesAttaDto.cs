﻿namespace Admin.NET.Application;

    /// <summary>
    /// 合同附件输出参数
    /// </summary>
    public class SalesAttaDto
    {
        /// <summary>
        /// 附件地址
        /// </summary>
        public string? Address { get; set; }
        
        /// <summary>
        /// 销售ID
        /// </summary>
        public long SalesID { get; set; }
        
        /// <summary>
        /// 附件名称
        /// </summary>
        public string? AttaName { get; set; }
        
    }
