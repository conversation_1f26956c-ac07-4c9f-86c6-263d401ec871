<template>
	<div class="sys-tenant-container">
		<el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.queryParams" ref="queryForm" :inline="true">
				<el-form-item label="租户名称" prop="name">
					<el-input placeholder="租户名称" clearable @keyup.enter="handleQuery" v-model="state.queryParams.name" />
				</el-form-item>
				<el-form-item label="联系电话" prop="phone">
					<el-input placeholder="联系电话" clearable @keyup.enter="handleQuery" v-model="state.queryParams.phone" />
				</el-form-item>
				<el-form-item label="管理员" prop="adminName">
					<el-input placeholder="管理员" clearable @keyup.enter="handleQuery" v-model="state.queryParams.adminName" />
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'sysTenant:page'"> 查询
						</el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="ele-Plus" @click="openAddTenant" v-auth="'sysTenant:add'"> 新增
					</el-button>
					<el-button type="primary" icon="ele-Plus" @click="UpdateOrder1" v-auth="'sysTenant:updateOrder'"> 更新序号
					</el-button>
					<el-button type="primary" icon="ele-Plus" @click="UpdateTenantRoles" v-auth="'sysTenant:updateRoles'"> 更新权限
					</el-button>
				</el-form-item>
			</el-form>
		</el-card>

		<el-card class="full-table" shadow="hover" style="margin-top: 8px">
			<el-table :data="state.tenantData" style="width: 100%" v-loading="state.loading" border>
				<el-table-column type="index" label="序号" width="55" align="center" fixed />
				<el-table-column prop="name" label="租户名称" width="160" show-overflow-tooltip />
				<el-table-column prop="adminName" label="管理员" width="70" show-overflow-tooltip />
				<el-table-column prop="phone" label="电话" width="120" show-overflow-tooltip />
				<!-- <el-table-column prop="host" label="主机" show-overflow-tooltip /> -->
				<!-- <el-table-column prop="email" label="邮箱" show-overflow-tooltip /> -->
				<el-table-column prop="tenantType" label="租户类型" width="90" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-tag v-if="scope.row.tenantType === 0"> ID隔离 </el-tag>
						<el-tag type="danger" v-else> 库隔离 </el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="appType" label="租户来源" width="90" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-tag v-if="scope.row.appType === 0"> 自建 </el-tag>
						<el-tag type="danger" v-else> 智云 </el-tag>
					</template>
				</el-table-column>
				<el-table-column label="状态" width="70" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-switch v-model="scope.row.status" :active-value="1" :inactive-value="2" size="small"
							@change="changeStatus(scope.row)" :disabled="scope.row.id == 123456780000000" />
					</template>
				</el-table-column>
				<el-table-column prop="dbType" label="数据库类型" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-tag v-if="scope.row.dbType === 0"> MySql </el-tag>
						<el-tag v-else-if="scope.row.dbType === 1"> SqlServer </el-tag>
						<el-tag v-if="scope.row.dbType === 2"> Sqlite </el-tag>
						<el-tag v-else-if="scope.row.dbType === 3"> Oracle </el-tag>
						<el-tag v-if="scope.row.dbType === 4"> PostgreSQL </el-tag>
						<el-tag v-else-if="scope.row.dbType === 5"> Dm </el-tag>
						<el-tag v-if="scope.row.dbType === 6"> Kdbndp </el-tag>
						<el-tag v-else-if="scope.row.dbType === 7"> Oscar </el-tag>
						<el-tag v-if="scope.row.dbType === 8"> MySqlConnector </el-tag>
						<el-tag v-else-if="scope.row.dbType === 9"> Access </el-tag>
						<el-tag v-if="scope.row.dbType === 10"> OpenGauss </el-tag>
						<el-tag v-else-if="scope.row.dbType === 11"> QuestDB </el-tag>
						<el-tag v-else-if="scope.row.dbType === 12"> HG </el-tag>
						<el-tag v-else-if="scope.row.dbType === 13"> ClickHouse </el-tag>
						<el-tag v-else-if="scope.row.dbType === 14"> GBase </el-tag>
						<el-tag v-else-if="scope.row.dbType === 900"> Custom </el-tag>
					</template>
				</el-table-column>
				<!-- <el-table-column prop="configId" label="数据库标识" show-overflow-tooltip /> -->
				<!-- <el-table-column prop="connection" label="数据库连接" width="300" show-overflow-tooltip /> -->
				<el-table-column prop="orderNo" label="排序" width="60" align="center" show-overflow-tooltip />
				<el-table-column prop="createTime" label="创建时间" width="160" align="center" show-overflow-tooltip />
				<el-table-column prop="saleUserName" label="销售人" width="70" align="center" show-overflow-tooltip />
				<el-table-column prop="newFlag" label="客户类型" width="70" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-tag type="success" v-if="scope.row.newFlag === undefined || scope.row.newFlag === 0"> 新客
						</el-tag>
						<el-tag type="danger" v-else> 续费 </el-tag>
					</template>
				</el-table-column>
				<el-table-column label="开始时间" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ formatDate(scope.row.startTime) }}
					</template>
				</el-table-column>
				<el-table-column label="时长" width="70" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ scope.row.duration }}年
					</template>
				</el-table-column>
				<el-table-column label="到期时间" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ formatDate(scope.row.endTime) }}
					</template>
				</el-table-column>
				<el-table-column prop="remark" label="备注" show-overflow-tooltip />
				<el-table-column label="操作" width="180" fixed="right" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-button icon="ele-Coin" size="small" text type="danger" @click="createTenant(scope.row)"
							v-auth="'sysTenant:createDb'" :disabled="scope.row.tenantType == 0"> 创建库 </el-button>
						<el-button icon="ele-Edit" size="small" text type="primary" @click="openEditTenant(scope.row)"
							v-auth="'sysTenant:update'"> 编辑 </el-button>
						<el-dropdown>
							<el-button icon="ele-MoreFilled" size="small" text type="primary" style="padding-left: 12px" />
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item icon="ele-OfficeBuilding" @click="openGrantMenu(scope.row)"
										:v-auth="'sysTenant:grantMenu'"> 授权菜单 </el-dropdown-item>
									<el-dropdown-item icon="ele-RefreshLeft" @click="resetTenantPwd(scope.row)"
										:v-auth="'sysTenant:resetPwd'"> 重置密码 </el-dropdown-item>
									<el-dropdown-item icon="ele-Delete" @click="delTenant(scope.row)"
										:v-auth="'sysTenant:delete'"> 删除租户 </el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination v-model:currentPage="state.tableParams.page" v-model:page-size="state.tableParams.pageSize"
				:total="state.tableParams.total" :page-sizes="[10, 20, 50, 100]" small background
				@size-change="handleSizeChange" @current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper" />
		</el-card>

		<EditTenant ref="editTenantRef" :title="state.editTenantTitle" @handleQuery="handleQuery" />
		<GrantMenu ref="grantMenuRef" />
	</div>
</template>

<script lang="ts" setup name="sysTenant">
import { onMounted, reactive, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import EditTenant from '/@/views/system/tenant/component/editTenant.vue';
import GrantMenu from '/@/views/system/tenant/component/grantMenu.vue';

import { getAPI } from '/@/utils/axios-utils';
import { SysTenantApi } from '/@/api-services/api';
import { TenantOutput } from '/@/api-services/models';
import { UpdateOrder} from '/@/api/main/pubOrder';
const editTenantRef = ref<InstanceType<typeof EditTenant>>();
const grantMenuRef = ref<InstanceType<typeof GrantMenu>>();
const state = reactive({
	loading: false,
	tenantData: [] as Array<TenantOutput>,
	queryParams: {
		name: undefined,
		phone: undefined,
		adminName: undefined,
	},
	tableParams: {
		page: 1,
		pageSize: 10,
		total: 0 as any,
	},
	editTenantTitle: '',
});

onMounted(async () => {
	handleQuery();
});

const formatDate = (dateString: string) => {
    if (!dateString) return '';  // Return an empty string if dateString is null or undefined

    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};


// 查询操作
const handleQuery = async () => {
	state.loading = true;
	let params = Object.assign(state.queryParams, state.tableParams);
	var res = await getAPI(SysTenantApi).apiSysTenantPagePost(params);
	state.tenantData = res.data.result?.items ?? [];
	state.tableParams.total = res.data.result?.total;
	state.loading = false;
};

// 重置操作
const resetQuery = () => {
	state.queryParams.name = undefined;
	state.queryParams.phone = undefined;
	handleQuery();
};

// 打开新增页面
const openAddTenant = () => {
	state.editTenantTitle = '添加租户';
	editTenantRef.value?.openDialog({ tenantType: 0 });
};
const UpdateOrder1= async () => {
	// var res = await UpdateOrder();
	// ElMessage.success('序号更新成功');
	ElMessageBox.confirm(`确定更新序号吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await UpdateOrder();
			handleQuery();
			ElMessage.success('序号更新成功');
		})
		.catch(() => { });
}

const UpdateTenantRoles= async () => {
	ElMessageBox.confirm(`确定更新权限吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
	.then(async () => {
			await getAPI(SysTenantApi).apiUpdateTenantRolesPost();
			handleQuery();
			ElMessage.success('更新成功');
		})
		.catch(() => { });
}

// 打开编辑页面
const openEditTenant = (row: any) => {
	state.editTenantTitle = '编辑租户';
	editTenantRef.value?.openDialog(row);
};

// 打开授权菜单页面
const openGrantMenu = async (row: any) => {
	grantMenuRef.value?.openDialog(row);
};

// 重置密码
const resetTenantPwd = async (row: any) => {
	ElMessageBox.confirm(`确定重置密码：【${row.name}】?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await getAPI(SysTenantApi).apiSysTenantResetPwdPost({ userId: row.userId });
			ElMessage.success('密码重置成功：mima123');
		})
		.catch(() => { });
};

// 删除
const delTenant = (row: any) => {
	ElMessageBox.confirm(`确定删除租户：【${row.name}】?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await getAPI(SysTenantApi).apiSysTenantDeletePost({ id: row.id });
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => { });
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	state.tableParams.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	state.tableParams.page = val;
	handleQuery();
};

// 创建租户库
const createTenant = (row: any) => {
	ElMessageBox.confirm(`确定创建/更新租户数据库：【${row.name}】?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await getAPI(SysTenantApi).apiSysTenantCreateDbPost({ id: row.id });
			ElMessage.success('创建/更新租户数据库成功');
		})
		.catch(() => { });
};

// 修改状态
const changeStatus = (row: any) => {
	getAPI(SysTenantApi)
		.apiSysTenantSetStatusPost({ id: row.id, status: row.status })
		.then(() => {
			ElMessage.success('租户状态设置成功');
		})
		.catch(() => {
			row.status = row.status == 1 ? 2 : 1;
		});
};
</script>
