﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 出库记录基础输入参数
/// </summary>
public class OutboundRecordBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long Id { get; set; }

    /// <summary>
    /// 出库明细Id
    /// </summary>
    public virtual long? WarehouseOutMxId { get; set; }

    /// <summary>
    /// 出库数量
    /// </summary>
    public virtual int? OutBoundCount { get; set; }

    /// <summary>
    /// 打印次数
    /// </summary>
    public virtual int? PrintCount { get; set; }

    /// <summary>
    /// 批次Id
    /// </summary>
    public virtual long? WarehouseBatchId { get; set; }

    /// <summary>
    /// 是否良品
    /// </summary>
    public virtual bool? GoodProduct { get; set; }

}

/// <summary>
/// 出入库记录分页查询输入参数
/// </summary>
public class OutboundRecordInput : BasePageInput
{
    /// <summary>
    /// 出入库类型
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    /// 打印状态
    /// </summary>
    public string PrintState { get; set; }

    /// <summary>
    /// 单号
    /// </summary>
    public string OrderNumber { get; set; }

    /// <summary>
    /// 客户Id
    /// </summary>
    public long? CustomId { get; set; }

    /// <summary>
    /// 供应商Id
    /// </summary>
    public long? SupplierId { get; set; }

    /// <summary>
    /// 仓库Id
    /// </summary>
    public long? WarehouseId { get; set; }

    /// <summary>
    /// 商品Id
    /// </summary>
    public long? GoodsId { get; set; }

    /// <summary>
    /// 批次号
    /// </summary>
    public string BatchNumber { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 操作人
    /// </summary>
    public long? CreateUserId { get; set; }

}


/// <summary>
/// 出入库记录输入参数
/// </summary>
public class OutInBoundInput
{
    /// <summary>
    /// 出入库类型
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    /// 单号
    /// </summary>
    public string OrderNumber { get; set; }
}

/// <summary>
/// 出库记录增加输入参数
/// </summary>
public class AddOutboundRecordInput : OutboundRecordBaseInput
{
}

/// <summary>
/// 出库记录删除输入参数
/// </summary>
public class DeleteOutboundRecordInput : BaseIdInput
{
}

/// <summary>
/// 出库记录更新输入参数
/// </summary>
public class UpdateOutboundRecordInput : OutboundRecordBaseInput
{
}

/// <summary>
/// 出库记录主键查询输入参数
/// </summary>
public class QueryByIdOutboundRecordInput : DeleteOutboundRecordInput
{

}
