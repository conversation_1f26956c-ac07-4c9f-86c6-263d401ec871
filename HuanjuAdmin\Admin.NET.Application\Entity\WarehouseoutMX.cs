﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 出库单明细
    /// </summary>
    [SugarTable("warehouseoutmx","出库单明细")]
    [Tenant("1300000000001")]
    public class WarehouseoutMX  : EntityTenant
    {
        /// <summary>
        /// 出库单
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.ManyToOne, nameof(OutId))]
        public Warehouseout warehouseout { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        [SugarColumn(ColumnDescription = "商品ID", Length = 50)]
        public long goodsId { get; set; }

        /// <summary>
        /// 商品
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(goodsId))]
        public Warehousegoods Warehousegoods { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(ColumnDescription = "单位", Length = 20)]
        public long? Unit { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(Unit))]
        public WarehouseGoodsUnit WarehouseGoodsUnit { get; set; }
        /// <summary>
        /// 是否缺货
        /// </summary>
        [SugarColumn(ColumnDescription = "是否缺货")]
        public int? Vacancy { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        [SugarColumn(ColumnDescription = "客户名称", Length = 50)]
        public string? CustomerName { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        [SugarColumn(ColumnDescription = "联系人", Length = 20)]
        public string? Contacts { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        [SugarColumn(ColumnDescription = "电话", Length = 20)]
        public string? Phone { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        [SugarColumn(ColumnDescription = "地址", Length = 100)]
        public string? Address { get; set; }
        /// <summary>
        /// 出库类型
        /// </summary>
        [SugarColumn(ColumnDescription = "出库类型", Length = 10)]
        public string? Outboundtype { get; set; }
        /// <summary>
        /// 出库时间
        /// </summary>
        [SugarColumn(ColumnDescription = "出库时间")]
        public DateTime? Deliverytime { get; set; }
        /// <summary>
        /// 审核状态
        /// </summary>
        [SugarColumn(ColumnDescription = "审核状态", Length = 10)]
        public string? Auditstatus { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 0)]
        public string? Notes { get; set; }
        ///// <summary>
        ///// 租户Id
        ///// </summary>
        //[SugarColumn(ColumnDescription = "租户Id")]
        //public long? TenantId { get; set; }
        /// <summary>
        /// 出库单ID
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "出库单ID")]
        public long OutId { get; set; }

        [SugarColumn(ColumnDescription = "是否良品")]
        public bool GoodProduct { get; set; }

        [SugarColumn(ColumnDescription = "出库数量")]
        /// <summary>
        /// 出库数量
        /// </summary>
        public int? OutCount { get; set; }

        [SugarColumn(ColumnDescription = "实际出库数量")]
        /// <summary>
        /// 实际出库数量
        /// </summary>
        public int? TrueOutCount { get; set; }

        /// <summary>
        /// 本次出库数量
        /// </summary>
        public int? ThisOutCount { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [SugarColumn(ColumnDescription = "单价")]
        public decimal Unitprice { get; set; }
        /// <summary>
        /// 合计
        /// </summary>
        [SugarColumn(ColumnDescription = "合计")]
        public decimal TotalAmt { get; set; }
    }
}