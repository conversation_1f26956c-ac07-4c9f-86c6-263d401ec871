﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Furion.FriendlyException;
using System.Linq;

namespace Admin.NET.Application;
/// <summary>
/// 个人审批明细服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WorkflowRecordService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<WorkflowRecord> _rep;
    public WorkflowRecordService(SqlSugarRepository<WorkflowRecord> rep)
    {
        _rep = rep;
    }

    /// <summary>
    /// 分页查询个人审批明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WorkflowRecordOutput>> Page(WorkflowRecordInput input)
    {
        var query = _rep.AsQueryable()
                    .WhereIF(input.OrderId > 0, u => u.OrderId == input.OrderId)
                    .WhereIF(input.StepNumber > 0, u => u.StepNumber == input.StepNumber)
                    .WhereIF(input.Approver > 0, u => u.Approver == input.Approver)
                    .WhereIF(input.Status > 0, u => u.Status == input.Status)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))

                    .Select<WorkflowRecordOutput>()
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加个人审批明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddWorkflowRecordInput input)
    {
        var entity = input.Adapt<WorkflowRecord>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除个人审批明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteWorkflowRecordInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.DeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新个人审批明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWorkflowRecordInput input)
    {
        var entity = input.Adapt<WorkflowRecord>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取个人审批明细
    /// </summary>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<List<WorkflowRecordOutput>> Detail(WorkflowOrderInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(u => u.OrderId == input.Id)
                    .Select(u => new WorkflowRecordOutput
                    {
                        Id = u.Id,
                        Avatar = u.SysUser.Avatar,
                        ApproveTime = u.ApproveTime,
                        StepNumber = u.StepNumber,
                        SysOrgName = u.SysUser.SysOrg.Name,
                        SysPosName = u.SysUser.SysPos.Name,
                        SysUserRealName = u.SysUser.RealName,
                        Status = u.Status,
                        Remark = u.Remark,
                    });

        var listRecord = await query.ToListAsync();
        var newList = new List<WorkflowRecordOutput>();

        var recordFirst = new WorkflowRecordOutput();
        recordFirst.Avatar = input.Avatar;
        recordFirst.SysUserRealName = input.SysUserRealName;
        recordFirst.ApproveTime = input.CreateTime;
        recordFirst.SysOrgName = input.SysOrgName;
        recordFirst.SysOrgName = input.SysPosName;
        recordFirst.Status = ApproveStatusEnum.InProgress;
        recordFirst.StepNumber = -99999;

        newList.Add(recordFirst);

        if (listRecord != null && listRecord.Count > 0)
        {
            foreach (var item in listRecord.OrderBy(x => x.StepNumber).ThenByDescending(x => x.Status))
            {
                if (newList.Exists(x => x.StepNumber == item.StepNumber))
                    continue;

                if (item.Status == ApproveStatusEnum.InProgress)
                {
                    newList.Add(item);
                    continue;
                }

                var sameSteps = listRecord.FindAll(x => x.Id != item.Id && x.StepNumber == item.StepNumber && item.Status == 0);
                foreach (var step in sameSteps)
                {
                    item.SysUserRealName += "/" + step.SysUserRealName;
                }
                newList.Add(item);
            }
        }

        return newList;
    }

    /// <summary>
    /// 获取个人审批明细列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WorkflowRecordOutput>> List([FromQuery] WorkflowRecordInput input)
    {
        return await _rep.AsQueryable().Select<WorkflowRecordOutput>().ToListAsync();
    }


    /// <summary>
    /// 获取待我审批的流程明细
    /// </summary>
    /// <param name="userID"></param>
    /// <param name="approveStatus"></param>
    /// <returns></returns>
    public async Task<List<WorkflowRecordOutput>> GetDaiWoSP(long userID, ApproveStatusEnum approveStatus)
    {
        return await _rep.AsQueryable()
            .Where(u => u.Approver == userID && u.Status == approveStatus)
            .Select<WorkflowRecordOutput>().ToListAsync();
    }


}

