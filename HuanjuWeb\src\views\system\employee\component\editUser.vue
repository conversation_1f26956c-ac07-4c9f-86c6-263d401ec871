<template>
	<div class="sys-user-container">
		<el-dialog v-model="state.isShowDialog" draggable :close-on-click-modal="false" width="769px">
			<template #header>
				<div style="color: #fff">
					<el-icon size="16" style="margin-right: 3px; display: inline; vertical-align: middle"> <ele-Edit />
					</el-icon>
					<span>{{ props.title }}</span>
				</div>
			</template>
			<el-tabs v-loading="state.loading" v-model="state.selectedTabName">
				<el-tab-pane label="基础信息">
					<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="100px">
						<el-row :gutter="35">
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
								<el-form-item label="真实姓名" prop="realName"
									:rules="[{ required: true, message: '真实姓名不能为空', trigger: 'blur' }]">
									<el-input v-model="state.ruleForm.realName" placeholder="真实姓名" clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
								<el-form-item label="手机号码" prop="phone"
									:rules="[{ required: true, message: '手机号码不能为空', trigger: 'blur' }]">
									<el-input v-model="state.ruleForm.phone" placeholder="手机号码" clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
								<el-form-item label="账号" prop="account"
									:rules="[{ required: true, message: '账号不能为空', trigger: 'blur' }]">
									<el-input v-model="state.ruleForm.account" placeholder="账号" clearable />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
								<el-form-item label="出生日期" prop="birthday">
									<el-date-picker v-model="state.ruleForm.birthday" type="date" placeholder="出生日期"
										format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%" />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
								<el-form-item label="合同状态" prop="contractStatus"
									:rules="[{ required: true, message: '合同状态不能为空', trigger: 'blur' }]">
									<el-select clearable filterable v-model="state.ruleForm.contractStatus"
										placeholder="请选择合同状态" >
										<el-option v-for="(item, index) in counterStore.contractList" :key="index"
											:value="item.value" :label="item.label" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
								<el-form-item label="入职时间" prop="entryTime">
									<el-date-picker v-model="state.ruleForm.entryTime" type="date" placeholder="入职时间"
										format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%" />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
								<el-form-item label="性别" prop="sex">
									<el-radio-group v-model="state.ruleForm.sex">
										<el-radio :label="1">男</el-radio>
										<el-radio :label="2">女</el-radio>
									</el-radio-group>
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
								<el-form-item label="合同时间" prop="contractTime">
									<el-date-picker v-model="state.ruleForm.contractTime" type="date" placeholder="合同时间"
										format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%" />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb5">
								<el-form-item label="排序" rop="orderNo"
									:rules="[{ required: true, message: '排序不能为空', trigger: 'blur' }]">
									<el-input-number v-model="state.ruleForm.orderNo" placeholder="排序" class="w100" />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
								<el-form-item label="试用期" prop="probationPeriod">
									<el-input v-model="state.ruleForm.probationPeriod" placeholder="试用期" clearable />
								</el-form-item>
							</el-col>
							<el-divider border-style="dashed" content-position="center">
								<div style="color: #b1b3b8">机构组织</div>
							</el-divider>

							<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
								<el-form-item label="职位" prop="posId"
									:rules="[{ required: true, message: '职位名称不能为空', trigger: 'blur' }]">
									<el-select v-model="state.ruleForm.posId" placeholder="职位" style="width: 100%">
										<el-option v-for="d in state.posData" :key="d.id" :label="d.name" :value="d.id" />
									</el-select>
								</el-form-item>
							</el-col> -->
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
								<el-form-item label="直属上级" prop="reportToId"
								>
									<el-cascader :options="props.orgData"
										:props="{ checkStrictly: true, emitPath: false, value: 'id', label: 'name', expandTrigger: 'hover' }"
										placeholder="直属上级" clearable class="w100" v-model="state.ruleForm.reportToId">
										<template #default="{ node, data }">
											<span  :class="{ 'disabled-node': data.posId != null }">{{ data.name }}</span>
											<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
										</template>
									</el-cascader>
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
								<el-form-item label="职位" prop="posId"
									:rules="[{ required: true, message: '职位不能为空', trigger: 'blur' }]">
									<el-cascader :options="props.orgData"
										:props="{ checkStrictly: true, emitPath: false, value: 'id', label: 'name', expandTrigger: 'hover' }"
										placeholder="职位" clearable class="w100" v-model="state.ruleForm.posId">
										<template #default="{ node, data }">
											<span  :class="{ 'disabled-node': data.posId === null }">{{ data.name }}</span>
											<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
										</template>
									</el-cascader>
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
								<el-form-item label="工号" prop="jobNum">
									<el-input v-model="state.ruleForm.jobNum" placeholder="工号" clearable />
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</el-tab-pane>
			</el-tabs>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel">取 消</el-button>
					<el-button type="primary" @click="submit">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup name="sysEditUser">

import useCounter from '/@/stores/counter'
import { onMounted, reactive, ref } from 'vue';

import { getAPI } from '/@/utils/axios-utils';
import { SysPosApi, SysRoleApi, SysUserApi, SysOrgApi } from '/@/api-services/api';
import { RoleOutput, SysOrg, SysPos, UpdateUserInput } from '/@/api-services/models';

const counterStore = useCounter()

const props = defineProps({
	title: String,
	orgData: Array<SysOrg>,
});
const emits = defineEmits(['handleQuery']);
const ruleFormRef = ref();
const state = reactive({
	loading: false,
	isShowDialog: false,
	selectedTabName: '0', // 选中的 tab 页
	ruleForm: {} as UpdateUserInput,
	posData: [] as Array<SysPos>, // 职位数据
	roleData: [] as Array<RoleOutput>, // 角色数据
	posList: [] as Array<SysPos>
});

onMounted(async () => {
	state.loading = true;
	var res = await getAPI(SysOrgApi).apiSysOrgListGet(0);
	state.posData = res.data.result ?? [];
	var res1 = await getAPI(SysRoleApi).apiSysRoleListGet();
	state.roleData = res1.data.result ?? [];
	state.loading = false;
    state.posList = state.posData;
	debugger;
	if (props.orgData) {
		props.orgData.forEach(node => {	
			if (node.posId === null) {
				node.disabled = true;
			} else {
				node.disabled = false;
			}
			if (node.children) {
				recursiveSetDisabled(node);
			}
         });
		//  state.posList.forEach(node => {
		// 	console.log(node);
		// 	if (node.posId === null) {
		// 		node.disabled = true;
		// 	} else {
		// 		node.disabled = false;
		// 	}
		// 	if (node.children) {
		// 		recursiveSetDisabled1(node);
		// 	}
        //  });
	}
});
const recursiveSetDisabled = (node:any) => {
	if (node.children) {
        node.children.forEach((childNode: any) => {
			if (childNode.posId === null) {
				childNode.disabled = true;
			} else {
				childNode.disabled = false;
			}
           recursiveSetDisabled(childNode);  // 递归遍历子节点
        });
	}
};
// const recursiveSetDisabled1 = (node:any) => {
// 	if (node.children) {
//         node.children.forEach((childNode: any) => {
// 			if (childNode.posId === null) {
// 				childNode.disabled = true;
// 			} else {
// 				childNode.disabled = false;
// 			}
//            recursiveSetDisabled1(childNode);  // 递归遍历子节点
//         });
// 	}
// };
// 打开弹窗
const openDialog = async (row: any) => {
	state.selectedTabName = '0'; // 重置为第一个 tab 页
	state.ruleForm = JSON.parse(JSON.stringify(row));
	debugger;
	if (JSON.stringify(row) !== '{}') {
		var resRole = await getAPI(SysUserApi).apiSysUserOwnRoleListUserIdGet(row.id);
		state.ruleForm.roleIdList = resRole.data.result;
		var resExtOrg = await getAPI(SysUserApi).apiSysUserOwnExtOrgListUserIdGet(row.id);
		state.ruleForm.extOrgIdList = resExtOrg.data.result;
		state.isShowDialog = true;
	} else state.isShowDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emits('handleQuery');
	state.isShowDialog = false;
};

// 取消
const cancel = () => {
	state.isShowDialog = false;
};

// 提交
const submit = () => {
	ruleFormRef.value.validate(async (valid: boolean) => {
		if (!valid) return;
		if (state.ruleForm.orgId == null) {
           delete state.ruleForm.orgId
		}
		if (state.ruleForm.id != undefined && state.ruleForm.id > 0) {
			await getAPI(SysUserApi).apiSysUserUpdatePost(state.ruleForm);
		} else {
			await getAPI(SysUserApi).apiSysUserAddPost(state.ruleForm);
		}
		closeDialog();
	});
};

// 增加附属机构行
const addExtOrgRow = () => {
	if (state.ruleForm.extOrgIdList == undefined) state.ruleForm.extOrgIdList = [];
	state.ruleForm.extOrgIdList?.push({});
};

// 删除附属机构行
const deleteExtOrgRow = (k: number) => {
	state.ruleForm.extOrgIdList?.splice(k, 1);
};

// 导出对象
defineExpose({ openDialog });
</script>
<style scoped>
.disabled-node {
  color: #000; /* 设置禁用节点的样式 */
}
</style>