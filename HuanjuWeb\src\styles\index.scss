/* 增强解决按钮点击后保持焦点样式的问题 */
.el-button:focus,
.el-button:focus-visible,
.el-button:active,
.el-button.is-focus,
.el-button.is-active {
  outline: none !important;
  box-shadow: none !important;
  border-color: var(--el-button-border-color) !important;
  background-color: var(--el-button-bg-color) !important;
  color: var(--el-button-text-color) !important;
}

/* 针对主要按钮 */
.el-button--primary:focus,
.el-button--primary:focus-visible,
.el-button--primary:active,
.el-button--primary.is-focus,
.el-button--primary.is-active {
  border-color: var(--el-button-primary-border-color) !important;
  background-color: var(--el-button-primary-bg-color) !important;
  color: var(--el-button-primary-text-color) !important;
}

/* 禁止按钮点击后的状态保持 */
.el-button:after {
  display: none !important;
} 