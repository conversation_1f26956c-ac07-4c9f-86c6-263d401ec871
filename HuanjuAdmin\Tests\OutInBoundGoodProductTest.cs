using Admin.NET.Application.Entity;
using Admin.NET.Application.Service.OutboundRecord.Dto;
using System;
using Xunit;

namespace Admin.NET.Application.Tests
{
    /// <summary>
    /// 出入库良品次品功能测试
    /// </summary>
    public class OutInBoundGoodProductTest
    {
        /// <summary>
        /// 测试OutInBoundOutput的ProductTypeDesc属性
        /// </summary>
        [Fact]
        public void Test_OutInBoundOutput_ProductTypeDesc()
        {
            // 测试良品
            var goodProductOutput = new OutInBoundOutput
            {
                GoodProduct = true
            };
            Assert.Equal("良品", goodProductOutput.ProductTypeDesc);

            // 测试次品
            var defectiveProductOutput = new OutInBoundOutput
            {
                GoodProduct = false
            };
            Assert.Equal("次品", defectiveProductOutput.ProductTypeDesc);

            // 测试未知
            var unknownProductOutput = new OutInBoundOutput
            {
                GoodProduct = null
            };
            Assert.Equal("未知", unknownProductOutput.ProductTypeDesc);
        }

        /// <summary>
        /// 测试WarehouseInrecordMX实体的默认值
        /// </summary>
        [Fact]
        public void Test_WarehouseInrecordMX_DefaultValues()
        {
            var entity = new WarehouseInrecordMX();
            
            // 测试GoodProduct默认值为true
            Assert.True(entity.GoodProduct);
        }

        /// <summary>
        /// 测试OutboundRecord实体的GoodProduct字段
        /// </summary>
        [Fact]
        public void Test_OutboundRecord_GoodProduct()
        {
            var outboundRecord = new OutboundRecord
            {
                GoodProduct = true,
                OutBoundCount = 10,
                Remark = "测试出库记录"
            };

            Assert.True(outboundRecord.GoodProduct);
            Assert.Equal(10, outboundRecord.OutBoundCount);
        }

        /// <summary>
        /// 测试InboundRecord实体的GoodProduct字段
        /// </summary>
        [Fact]
        public void Test_InboundRecord_GoodProduct()
        {
            var inboundRecord = new InboundRecord
            {
                GoodProduct = false,
                InBoundCount = 5,
                Remark = "测试入库记录"
            };

            Assert.False(inboundRecord.GoodProduct);
            Assert.Equal(5, inboundRecord.InBoundCount);
        }

        /// <summary>
        /// 测试红冲记录的备注生成
        /// </summary>
        [Fact]
        public void Test_RedInk_Remark_Generation()
        {
            long originalRecordId = 12345;
            bool isGoodProduct = true;
            
            string expectedRemark = $"红冲记录，原记录ID: {originalRecordId}，产品类型: {(isGoodProduct ? "良品" : "次品")}";
            string actualRemark = $"红冲记录，原记录ID: {originalRecordId}，产品类型: {(isGoodProduct ? "良品" : "次品")}";
            
            Assert.Equal(expectedRemark, actualRemark);
        }

        /// <summary>
        /// 测试库存计算逻辑
        /// </summary>
        [Fact]
        public void Test_Inventory_Calculation()
        {
            // 测试良品库存计算
            int goodProduct = 100;
            int shippedoutNum = 20;
            int compatible = goodProduct - shippedoutNum;
            
            Assert.Equal(80, compatible);
            Assert.False(compatible < 0); // 不缺货
        }

        /// <summary>
        /// 测试入库记录GoodProduct字段设置
        /// </summary>
        [Fact]
        public void Test_InboundRecord_GoodProduct_Setting()
        {
            // 测试良品入库记录
            var goodInboundRecord = new InboundRecord
            {
                GoodProduct = true,
                InBoundCount = 10,
                OrderNum = "RK001-1"
            };
            
            Assert.True(goodInboundRecord.GoodProduct);
            Assert.Equal("良品", goodInboundRecord.GoodProduct == true ? "良品" : "次品");

            // 测试次品入库记录
            var defectiveInboundRecord = new InboundRecord
            {
                GoodProduct = false,
                InBoundCount = 5,
                OrderNum = "RK001-2"
            };
            
            Assert.False(defectiveInboundRecord.GoodProduct);
            Assert.Equal("次品", defectiveInboundRecord.GoodProduct == true ? "良品" : "次品");
        }

        /// <summary>
        /// 测试出库记录GoodProduct字段设置
        /// </summary>
        [Fact]
        public void Test_OutboundRecord_GoodProduct_Setting()
        {
            // 测试良品出库记录
            var goodOutboundRecord = new OutboundRecord
            {
                GoodProduct = true,
                OutBoundCount = 8,
                OrderNum = "CK001-1"
            };
            
            Assert.True(goodOutboundRecord.GoodProduct);
            Assert.Equal("良品", goodOutboundRecord.GoodProduct == true ? "良品" : "次品");

            // 测试次品出库记录
            var defectiveOutboundRecord = new OutboundRecord
            {
                GoodProduct = false,
                OutBoundCount = 3,
                OrderNum = "CK001-2"
            };
            
            Assert.False(defectiveOutboundRecord.GoodProduct);
            Assert.Equal("次品", defectiveOutboundRecord.GoodProduct == true ? "良品" : "次品");
        }

        /// <summary>
        /// 测试WarehouseoutMXOutput字段名一致性
        /// </summary>
        [Fact]
        public void Test_WarehouseoutMXOutput_FieldName_Consistency()
        {
            var output = new WarehouseoutMXOutput
            {
                GoodProduct = true,
                OutCount = 10,
                TrueOutCount = 8
            };
            
            Assert.True(output.GoodProduct);
            
            // 验证字段名是GoodProduct而不是goodProduct
            var propertyInfo = typeof(WarehouseoutMXOutput).GetProperty("GoodProduct");
            Assert.NotNull(propertyInfo);
            Assert.Equal(typeof(bool), propertyInfo.PropertyType);
        }

        /// <summary>
        /// 测试批次库存计算
        /// </summary>
        [Fact]
        public void Test_Batch_Inventory_Calculation()
        {
            // 模拟批次数据
            int batchGoodProductNum = 50;
            int batchRejectNum = 10;
            int redInkCount = 5;

            // 测试良品批次红冲
            int newBatchGoodProduct = batchGoodProductNum + redInkCount;
            Assert.Equal(55, newBatchGoodProduct);

            // 测试次品批次红冲
            int newBatchReject = batchRejectNum - redInkCount;
            Assert.Equal(5, newBatchReject);

            // 测试批次是否为空
            bool isBatchEmpty = (newBatchGoodProduct <= 0 && newBatchReject <= 0);
            Assert.False(isBatchEmpty);
        }

        /// <summary>
        /// 测试安全库存预警逻辑
        /// </summary>
        [Fact]
        public void Test_Safety_Stock_Warning()
        {
            int goodProductStock = 15;
            int safetyStockLowNum = 20;
            int safetyStockTallNum = 100;

            // 测试库存不足预警
            int stockWarning = 0;
            if (safetyStockLowNum > 0 && goodProductStock < safetyStockLowNum)
                stockWarning = 1; // 库存不足

            if (safetyStockTallNum > 0 && goodProductStock > safetyStockTallNum)
                stockWarning = 2; // 库存超额

            Assert.Equal(1, stockWarning); // 应该是库存不足
        }

        /// <summary>
        /// 测试出库红冲时可配数计算
        /// </summary>
        [Fact]
        public void Test_Outbound_RedInk_Compatible_Calculation()
        {
            // 模拟红冲前的数据
            int originalGoodProduct = 100;  // 原始良品库存
            int originalShippedoutNum = 20; // 原始待出库数
            int originalCompatible = 80;    // 原始可配数 = 100 - 20
            
            int redInkCount = 10; // 红冲数量
            
            // 出库红冲后：增加库存，重新计算可配数
            int newGoodProduct = originalGoodProduct + redInkCount; // 110
            
            // 假设红冲后重新计算的待出库数减少了（因为实际出库数减少了）
            int newShippedoutNum = originalShippedoutNum - redInkCount; // 10
            
            // 重新计算可配数
            int newCompatible = newGoodProduct - newShippedoutNum; // 110 - 10 = 100
            
            Assert.Equal(110, newGoodProduct);
            Assert.Equal(10, newShippedoutNum);
            Assert.Equal(100, newCompatible);
            
            // 验证可配数正确增加
            Assert.True(newCompatible > originalCompatible);
        }

        /// <summary>
        /// 测试入库红冲时在途数计算
        /// </summary>
        [Fact]
        public void Test_Inbound_RedInk_IntransitNum_Calculation()
        {
            // 模拟红冲前的数据
            int originalGoodProduct = 100;  // 原始良品库存
            int originalIntransitNum = 30;  // 原始在途数
            int originalDocumentNum = 150;  // 单据总数量
            int originalRcvQty = 120;       // 已入库总数量（150-30=120）
            
            int redInkCount = 10; // 红冲数量
            
            // 入库红冲后：减少库存，增加在途数
            int newGoodProduct = originalGoodProduct - redInkCount; // 90
            
            // 红冲后实际入库数量减少
            int newRcvQty = originalRcvQty - redInkCount; // 110
            
            // 重新计算在途数 = 单据数量 - 已入库数量
            int newIntransitNum = originalDocumentNum - newRcvQty; // 150 - 110 = 40
            
            Assert.Equal(90, newGoodProduct);
            Assert.Equal(110, newRcvQty);
            Assert.Equal(40, newIntransitNum);
            
            // 验证在途数正确增加（因为已入库数量减少了）
            Assert.True(newIntransitNum > originalIntransitNum); // 40 > 30
            Assert.Equal(redInkCount, newIntransitNum - originalIntransitNum); // 增加的在途数等于红冲数量
        }

        /// <summary>
        /// 测试红冲时库存不足的验证
        /// </summary>
        [Fact]
        public void Test_RedInk_Insufficient_Inventory_Validation()
        {
            // 模拟库存不足的情况
            int currentGoodProduct = 5;  // 当前良品库存
            int redInkCount = 10;        // 红冲数量

            // 验证库存不足时应该抛出异常
            bool shouldThrowException = currentGoodProduct < redInkCount;
            Assert.True(shouldThrowException);
        }

        /// <summary>
        /// 测试红冲时缺货状态计算
        /// </summary>
        [Fact]
        public void Test_RedInk_StockOrNot_Calculation()
        {
            // 测试出库红冲后缺货状态的变化
            int originalGoodProduct = 50;
            int originalShippedoutNum = 60; // 待出库数大于库存，缺货
            bool originalStockOrNot = true; // 原来缺货
            
            int redInkCount = 20;
            
            // 红冲后
            int newGoodProduct = originalGoodProduct + redInkCount; // 70
            int newShippedoutNum = originalShippedoutNum - redInkCount; // 40
            int newCompatible = newGoodProduct - newShippedoutNum; // 30
            bool newStockOrNot = newCompatible < 0; // false，不再缺货
            
            Assert.Equal(70, newGoodProduct);
            Assert.Equal(40, newShippedoutNum);
            Assert.Equal(30, newCompatible);
            Assert.False(newStockOrNot);
            
            // 验证缺货状态从缺货变为不缺货
            Assert.True(originalStockOrNot && !newStockOrNot);
        }

        /// <summary>
        /// 测试入库红冲的完整逻辑验证
        /// </summary>
        [Fact]
        public void Test_Inbound_RedInk_Complete_Logic()
        {
            // 假设有一个商品的入库情况：
            // 单据1：DocumentNum=100, RcvQty=100 (完全入库)
            // 单据2：DocumentNum=50,  RcvQty=30  (部分入库)
            // 单据3：DocumentNum=20,  RcvQty=0   (待入库)
            
            // 红冲前的计算：
            int totalDocumentNum = 100 + 50 + 20; // 170
            int totalRcvQty = 100 + 30 + 0;       // 130
            int originalIntransitNum = totalDocumentNum - totalRcvQty; // 170 - 130 = 40
            
            // 现在对单据1进行红冲，红冲数量为20
            int redInkCount = 20;
            
            // 红冲后单据1的RcvQty变为：100 - 20 = 80
            int newTotalRcvQty = 80 + 30 + 0; // 110
            
            // 红冲后在途数重新计算：
            int newIntransitNum = totalDocumentNum - newTotalRcvQty; // 170 - 110 = 60
            
            // 验证结果
            Assert.Equal(170, totalDocumentNum); // 单据总数不变
            Assert.Equal(130, totalRcvQty);     // 原始已入库数量
            Assert.Equal(40, originalIntransitNum); // 原始在途数
            
            Assert.Equal(110, newTotalRcvQty);  // 红冲后已入库数量减少
            Assert.Equal(60, newIntransitNum);  // 红冲后在途数增加
            
            // 关键验证：在途数的增加量应该等于红冲数量
            int intransitIncrease = newIntransitNum - originalIntransitNum;
            Assert.Equal(redInkCount, intransitIncrease); // 60 - 40 = 20
            
            // 验证计算公式正确性
            Assert.Equal(totalDocumentNum - newTotalRcvQty, newIntransitNum);
        }
    }
}
