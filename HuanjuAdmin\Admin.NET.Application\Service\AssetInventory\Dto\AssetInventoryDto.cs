﻿namespace Admin.NET.Application;

/// <summary>
/// 固资库存输出参数
/// </summary>
public class AssetInventoryDto
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Specs { get; set; }

    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 借用数量
    /// </summary>
    public int BorrowCount { get; set; }

    /// <summary>
    /// 库存数量
    /// </summary>
    public int InventoryCount { get; set; }

    /// <summary>
    /// 初始总数量
    /// </summary>
    public int BaseTotalCount { get; set; }

    /// <summary>
    /// 初始借用数量
    /// </summary>
    public int BaseBorrowCount { get; set; }

    /// <summary>
    /// 初始库存数量
    /// </summary>
    public int BaseInventoryCount { get; set; }

    /// <summary>
    /// 需要归还
    /// </summary>
    public bool IsNdReturn { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

}
