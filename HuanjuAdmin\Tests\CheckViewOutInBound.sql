-- 检查出入库记录视图和相关表的GoodProduct字段
-- 用于诊断为什么GoodProduct字段显示不正确

-- 1. 检查View_OutInBound视图的结构
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'hjdb' 
AND TABLE_NAME = 'View_OutInBound'
ORDER BY ORDINAL_POSITION;

-- 2. 检查outboundrecord表的GoodProduct字段
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'hjdb' 
AND TABLE_NAME = 'outboundrecord' 
AND COLUMN_NAME = 'GoodProduct';

-- 3. 检查inboundrecord表的GoodProduct字段
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'hjdb' 
AND TABLE_NAME = 'inboundrecord' 
AND COLUMN_NAME = 'GoodProduct';

-- 4. 检查View_OutInBound视图是否存在
SELECT TABLE_NAME, TABLE_TYPE 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'hjdb' 
AND TABLE_NAME = 'View_OutInBound';

-- 5. 查看最近的出入库记录数据
SELECT Id, Type, OrderNum, GoodProduct, CreateTime 
FROM View_OutInBound 
ORDER BY CreateTime DESC 
LIMIT 10;

-- 6. 检查出库记录表中的最新数据
SELECT Id, OrderNum, GoodProduct, CreateTime 
FROM outboundrecord 
ORDER BY CreateTime DESC 
LIMIT 5;

-- 7. 检查入库记录表中的最新数据
SELECT Id, OrderNum, GoodProduct, CreateTime 
FROM inboundrecord 
ORDER BY CreateTime DESC 
LIMIT 5; 