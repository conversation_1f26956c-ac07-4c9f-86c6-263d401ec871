﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.CgibinUserInfoBatchGetRequest.Types;

namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 流程审批记录表
    /// </summary>
    [SugarTable("workflowrecord","流程审批记录表")]
    [Tenant("1300000000001")]
    public class WorkflowRecord  : EntityBaseId
    {
        /// <summary>
        /// 审批单ID
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "审批单ID")]
        public long OrderId { get; set; }
        /// <summary>
        /// 步骤编号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "步骤编号")]
        public int StepNumber { get; set; }
        /// <summary>
        /// 审批人
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "审批人")]
        public long Approver { get; set; }
        /// <summary>
        /// 审批时间
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "审批时间")]
        public DateTime? ApproveTime { get; set; }
        /// <summary>
        /// 用户
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(Approver))]
        public SysUser SysUser { get; set; }
        /// <summary>
        /// 审批状态
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "审批状态")]
        public ApproveStatusEnum Status { get; set; }
        /// <summary>
        /// 审批意见
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "审批意见")]
        public string Remark { get; set; }
    }
}