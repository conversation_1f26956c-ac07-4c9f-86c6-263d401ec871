-- 出入库明细良品次品区分功能数据库迁移脚本
-- 执行日期: 2024年
-- MySQL版本

-- 1. 为入库明细表添加良品次品字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'warehouseinrecordmx' 
     AND COLUMN_NAME = 'GoodProduct') = 0,
    'ALTER TABLE `warehouseinrecordmx` ADD COLUMN `GoodProduct` TINYINT(1) NOT NULL DEFAULT 1 COMMENT ''是否良品''',
    'SELECT ''Column GoodProduct already exists in warehouseinrecordmx'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 实际入库数量字段使用现有的RcvQty字段，无需添加TrueInCount
-- RcvQty字段已存在，代表实际入库数量

-- 3. 为出库记录表添加良品次品字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'outboundrecord' 
     AND COLUMN_NAME = 'GoodProduct') = 0,
    'ALTER TABLE `outboundrecord` ADD COLUMN `GoodProduct` TINYINT(1) NULL COMMENT ''是否良品''',
    'SELECT ''Column GoodProduct already exists in outboundrecord'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 为入库记录表添加良品次品字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'inboundrecord' 
     AND COLUMN_NAME = 'GoodProduct') = 0,
    'ALTER TABLE `inboundrecord` ADD COLUMN `GoodProduct` TINYINT(1) NULL COMMENT ''是否良品''',
    'SELECT ''Column GoodProduct already exists in inboundrecord'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 更新现有数据，根据出库明细表的GoodProduct字段更新出库记录
UPDATE outboundrecord 
SET GoodProduct = (
    SELECT mx.GoodProduct 
    FROM warehouseoutmx mx 
    WHERE mx.Id = outboundrecord.WarehouseOutMxId
    LIMIT 1
)
WHERE outboundrecord.GoodProduct IS NULL 
AND outboundrecord.WarehouseOutMxId IS NOT NULL;

-- 6. 更新现有数据，根据入库明细表的GoodProduct字段更新入库记录
UPDATE inboundrecord 
SET GoodProduct = (
    SELECT mx.GoodProduct 
    FROM warehouseinrecordmx mx 
    WHERE mx.Id = inboundrecord.WarehouseIncordMxId
    LIMIT 1
)
WHERE inboundrecord.GoodProduct IS NULL 
AND inboundrecord.WarehouseIncordMxId IS NOT NULL;

-- 7. 实际入库数量使用RcvQty字段，无需初始化TrueInCount
-- RcvQty字段已包含实际入库数量信息

-- 8. 更新出入库记录视图（如果存在）
-- 检查视图是否存在并删除
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'View_OutInBound') > 0,
    'DROP VIEW `View_OutInBound`',
    'SELECT ''View View_OutInBound does not exist'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 重新创建视图（包含良品次品字段）
-- 注意：这里的视图定义需要根据实际的业务逻辑来调整
CREATE VIEW `View_OutInBound` AS
SELECT 
    -- 出库记录
    o.Id,
    '0' as Type, -- 0表示出库
    o.Id as ParentId,
    o.OrderNum,
    o.OutBoundCount as OutInCount,
    o.PrintCount,
    o.WarehouseBatchId,
    wb.Batchnumber as BatchNumber,
    wo.OutOrder as OrderNumber,
    wo.CustomId,
    NULL as SupplierId,
    pc.Name as Company,
    pc.Phone,
    wh.Id as WarehouseId,
    wh.Name as WarehouseName,
    wg.Id as GoodsId,
    wg.Name as GoodsName,
    wg.BarCode,
    wg.Code,
    wg.Brand,
    wg.Specs,
    wu.Id as UnitId,
    wu.Name as UnitName,
    cu.RealName as CreateUserName,
    uu.RealName as UpdateUserName,
    o.CreateTime,
    o.UpdateTime,
    o.GoodProduct -- 添加良品次品字段
FROM outboundrecord o
LEFT JOIN warehouseoutmx omx ON o.WarehouseOutMxId = omx.Id
LEFT JOIN warehouseout wo ON omx.OutId = wo.Id
LEFT JOIN warehousebatch wb ON o.WarehouseBatchId = wb.Id
LEFT JOIN pubcustom pc ON wo.CustomId = pc.Id
LEFT JOIN warehouse wh ON wo.WarehouseId = wh.Id
LEFT JOIN warehousegoods wg ON omx.goodsId = wg.Id
LEFT JOIN warehousegoodsunit wu ON omx.Unit = wu.Id
LEFT JOIN sysuser cu ON o.CreateUserId = cu.Id
LEFT JOIN sysuser uu ON o.UpdateUserId = uu.Id

UNION ALL

SELECT 
    -- 入库记录
    i.Id,
    '1' as Type, -- 1表示入库
    i.Id as ParentId,
    i.OrderNum,
    i.InBoundCount as OutInCount,
    i.PrintCount,
    i.WarehouseBatchId,
    wb.Batchnumber as BatchNumber,
    wi.OrderNumber,
    NULL as CustomId,
    wi.SupplierId,
    ps.Name as Company,
    ps.Phone,
    wh.Id as WarehouseId,
    wh.Name as WarehouseName,
    wg.Id as GoodsId,
    wg.Name as GoodsName,
    wg.BarCode,
    wg.Code,
    wg.Brand,
    wg.Specs,
    wu.Id as UnitId,
    wu.Name as UnitName,
    cui.RealName as CreateUserName,
    uui.RealName as UpdateUserName,
    i.CreateTime,
    i.UpdateTime,
    i.GoodProduct -- 添加良品次品字段
FROM inboundrecord i
LEFT JOIN warehouseinrecordmx imx ON i.WarehouseIncordMxId = imx.Id
LEFT JOIN warehouseinrecord wi ON imx.InrecordId = wi.Id
LEFT JOIN warehousebatch wb ON i.WarehouseBatchId = wb.Id
LEFT JOIN pubsupplier ps ON wi.SupplierId = ps.Id
LEFT JOIN warehouse wh ON wi.Warehouseid = wh.Id
LEFT JOIN warehousegoods wg ON imx.GoodsId = wg.Id
LEFT JOIN warehousegoodsunit wu ON imx.Unit = wu.Id
LEFT JOIN sysuser cui ON i.CreateUserId = cui.Id
LEFT JOIN sysuser uui ON i.UpdateUserId = uui.Id;

SELECT '出入库明细良品次品区分功能数据库迁移完成！' as message;
