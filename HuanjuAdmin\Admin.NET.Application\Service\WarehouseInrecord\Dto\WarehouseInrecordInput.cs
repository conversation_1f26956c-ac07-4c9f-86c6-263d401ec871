﻿using Admin.NET.Application.Entity;
using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 入库单基础输入参数
/// </summary>
public class WarehouseInrecordBaseInput
{
    /// <summary>
    /// 入库单号
    /// </summary>
    public virtual string OrderNumber { get; set; }

    /// <summary>
    /// 采购单号
    /// </summary>
    public virtual string? PurchaseNumber { get; set; }

    /// <summary>
    /// 供应商ID
    /// </summary>
    public virtual long? SupplierId { get; set; }

    /// <summary>
    /// 入库类型
    /// </summary>
    public virtual RcvTypeEnum InhouseType { get; set; }

    /// <summary>
    /// 入库状态
    /// </summary>
    public virtual RcvStatusEnum InhouseStatus { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    /// <summary>
    /// 仓库ID
    /// </summary>
    public virtual string? Warehouseid { get; set; }

    /// <summary>
    /// 商品信息
    /// </summary>
    public virtual string? GoodsInfo { get; set; }

    /// <summary>
    /// 总金额
    /// </summary>
    public virtual decimal? TotalAmt { get; set; }

    /// <summary>
    /// 优惠金额
    /// </summary>
    public virtual decimal? DiscountAmt { get; set; }

    /// <summary>
    /// 实际金额
    /// </summary>
    public virtual decimal? ActualAmt { get; set; }

}

/// <summary>
/// 入库单分页查询输入参数
/// </summary>
public class WarehouseInrecordInput : BasePageInput
{
    /// <summary>
    /// 入库单号
    /// </summary>
    public string OrderNumber { get; set; }

    /// <summary>
    /// 采购单号
    /// </summary>
    public string? PurchaseNumber { get; set; }

    /// <summary>
    /// 入库类型
    /// </summary>
    public RcvTypeEnum InhouseType { get; set; }

    /// <summary>
    /// 入库状态
    /// </summary>
    public string? InhouseStatus { get; set; }
    /// <summary>
    /// 仓库ID
    /// </summary>
    public string? Warehouseid { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string? TradeName { get; set; }
}

/// <summary>
/// 入库单增加输入参数
/// </summary>
public class AddWarehouseInrecordInput : WarehouseInrecordBaseInput
{
    public long Id { get; set; }
}
/// <summary>
/// 入库单增加输入参数
/// </summary>
public class AddWarehouseInrecordMx 
{
    public AddWarehouseInrecordInput addWarehouseInrecordInput { get; set; }
    public List<AddWarehouseInrecordMXInput> listMx { get; set; }
}

/// <summary>
/// 入库单删除输入参数
/// </summary>
public class DeleteWarehouseInrecordInput : BaseIdInput
{
}

/// <summary>
/// 入库单更新输入参数
/// </summary>
public class UpdateWarehouseInrecordInput : WarehouseInrecordBaseInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public long Id { get; set; }

}

/// <summary>
/// 入库单主键查询输入参数
/// </summary>
public class QueryByIdWarehouseInrecordInput : DeleteWarehouseInrecordInput
{

}
