<template>
    <el-collapse-item name="outputList">
        <template #title>
            <div style="width: 100%; display: flex; justify-content: space-between; align-items: center;" >
                <el-text size="large">产出列表</el-text>
            </div>
        </template>

        <editable-table
            ref="tableRef"
            :table-data="props.tableData"
            @update:table-data="handleUpdate"
            :columns="tableColumns"
            :loading="loading"
            :show-pagination="false"
            :required-fields="['actQuantity']"
            validation-message="实际数量不能为空"
            style="height: calc(30vh - var(--el-collapse-header-height));"
        >
            <template #unitPrice-edit="{ row }">
                <el-input-number v-model="row.unitPrice" :min="0" :precision="2" size="small" style="width: 100%" controls-position="right"/>
            </template>
            <template #actQuantity-edit="{ row }">
                <el-input-number v-model="row.actQuantity" :min="0" :precision="2" size="small" style="width: 100%" controls-position="right"/>
            </template>
        </editable-table>
    </el-collapse-item>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import EditableTable from '/@/components/editableTable/index.vue';

const props = defineProps({
    tableData: {
        type: Array,
        required: true,
        default: () => []
    },
    activeItems: {
        type: Array,
        required: true,
        default: () => []
    }
});

const emit = defineEmits(['update:tableData']);

const loading = ref(false);
const tableRef = ref();

// 表格列配置
const tableColumns = [
    { type: 'index', label: '序号' },
    { prop: 'warehousegoods.name', label: '产品名称', showOverflowTooltip: true },
    { prop: 'warehousegoods.code', label: '产品编码', showOverflowTooltip: true },
    { prop: 'warehousegoods.specs', label: '规格型号', showOverflowTooltip: true },
    { prop: 'warehousegoods.warehouseGoodsUnit.name', label: '单位', showOverflowTooltip: true },
    { prop: 'unitPrice', label: '单价', showOverflowTooltip: true,slot: 'unitPrice',editable: true,width: 110 },
    { prop: 'totalPrice', label: '总价', showOverflowTooltip: true },
    { prop: 'baseQuantity', label: '基准数量', showOverflowTooltip: true },
    { 
        prop: 'actQuantity', 
        label: '实际数量', 
        slot: 'actQuantity',
        editable: true,
        width: 110,
        showOverflowTooltip: true 
    },
    { type: 'operation', label: '操作', width: 140, fixed: 'right' }
];


// 检查表格有效性
const valid = () => {
    return tableRef.value.valid();
};

// 重置编辑状态
const resetEditStatus = () => {
    if (tableRef.value) {
        tableRef.value.resetEditStatus();
    }
};

interface ProductItem {
    actQuantity: number;
    baseQuantity: number;
    [key: string]: any;
}

const handleUpdate = (val: ProductItem[], index: number) => {
    // 检查是否是编辑完成后的状态（tableRef不在编辑状态）
    if (tableRef.value) {
        // 找出修改后的行（与原始数据对比）
        const updatedData = val;

        // 找到修改过的行
        let changedRowIndex = index;
        let ratio = 1;

        // 如果找到了修改过的行，根据比例更新其他行
        if (changedRowIndex !== -1) {

            
            // 创建一个新数组来存储更新后的数据
            const newData = [...updatedData];

            

            newData[changedRowIndex].totalPrice = newData[changedRowIndex].actQuantity * newData[changedRowIndex].unitPrice;

            // 计算比例：实际数量/基准数量
            if (updatedData[changedRowIndex].baseQuantity && updatedData[changedRowIndex].baseQuantity !== 0) {
                ratio = updatedData[changedRowIndex].actQuantity / updatedData[changedRowIndex].baseQuantity;
            }

            // 更新其他行的实际数量
            for (let i = 0; i < newData.length; i++) {
                if (i !== changedRowIndex && newData[i].baseQuantity) {
                    newData[i].actQuantity = Number((newData[i].baseQuantity * ratio).toFixed(2));
                    newData[i].totalPrice = newData[i].actQuantity * newData[i].unitPrice;
                }
            }

            // 发送更新后的数据
            emit('update:tableData', newData);
            return;
        }
    }

    // 如果不是编辑完成后的状态或没有找到修改的行，直接更新数据
    emit('update:tableData', val);
}

// 暴露方法
defineExpose({
    loading,
    valid,
    resetEditStatus
});
</script>

<style scoped>
/* 确保编辑中的输入框宽度合适 */
.el-input, .el-select, .el-input-number {
    width: 100%;
}

/* 隐藏分页组件 */
:deep(.output-table .el-pagination) {
    display: none !important;
}
</style>