﻿<template>
	<!-- 出库单上 -->
	<div class="warehouseout-container">
		<el-card class="query-form">
			<el-form :model="queryParams" ref="queryForm" :inline="true">
				<el-form-item label="出库单号：">
					<el-input v-model="queryParams.outOrder" clearable="" placeholder="请输入出库单号" />
				</el-form-item>
				<el-form-item label="商品：">
					<el-input v-model="queryParams.goodsName" clearable="" placeholder="请输入商品名称" />
				</el-form-item>
				<el-form-item label="客户：">
					<el-input v-model="queryParams.customerName" clearable="" placeholder="请输入客户名称" />
				</el-form-item>
				<el-form-item label="出库状态：">
					<el-select v-model="queryParams.outBoundStatus" placeholder="请选择出库状态" clearable>
						<el-option v-for="item in outStatusList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="上级单号：">
					<el-input v-model="queryParams.superiorNum" clearable="" placeholder="请输入上级单号" />
				</el-form-item>
				<!-- <el-form-item label="客户：">
          <el-select v-model="queryParams.customId" clearable="" placeholder="请选择客户">
            <el-option v-for="item in getPagePubcustomList" :key="item.id" :value="item.id" :label="item.name" />
          </el-select>
        </el-form-item> -->
			</el-form>
			<div style="margin-top: -10px">
				<el-button-group>
					<el-button type="primary" icon="ele-Search" @click="(event) => { handleQuery(); event.target.blur(); }" v-auth="'warehouseout:page'"> 查询 </el-button>
					<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
				</el-button-group>
				<!-- <el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'warehouseout:page'"> 查询 </el-button> -->
				<el-button type="primary" icon="ele-Plus" @click="openAddWarehouseout" v-auth="'warehouseout:add'" style="margin-left: 20px"> 新增</el-button>
				<el-button type="primary" @click="(event) => handleButtonWithBlur(event, Submits)" icon="ele-Finished"> 提交</el-button>
				<el-button type="primary" @click="Withdraws" icon="ele-Back"> 撤回</el-button>
				<el-button type="primary" icon="ele-Finished" @click="GetOutbound"> 中止 </el-button>
			</div>
		</el-card>
		<splitpanes class="default-theme" horizontal style="height: 80vh" @resized="handleTz">
			<pane :size="topSize" ref="topCardRef">
				<el-card shadow="hover" :body-style="{ paddingBottom: '0' }" style="height: 100%" class="topCard">
					<el-table
						ref="inTable"
						:data="tableData"
						v-loading="loading"
						:row-class-name="rowClassName"
						tooltip-effect="light"
						row-key="id"
						border=""
						class="toptable"
						@row-click="handleRowClick"
						highlight-current-row
						:height="tableHeight"
						@selection-change="handleSelectionChange"
					>
						<el-table-column type="selection" width="40" align="center" />
						<el-table-column type="index" label="序号" width="45" align="center" />
						<el-table-column prop="outOrder" label="出库单号" width="100" show-overflow-tooltip="" />
						<el-table-column prop="goodsInfo" label="商品信息" width="170" show-overflow-tooltip="" />
						<el-table-column prop="totalAmt" label="总金额" width="90" show-overflow-tooltip="" />
						<el-table-column prop="discountAmt" label="优惠金额" width="90" show-overflow-tooltip="" />
						<el-table-column prop="actualAmt" label="实际金额" width="90" show-overflow-tooltip="" />
						<el-table-column prop="superiorNum" label="上级单号" width="100" show-overflow-tooltip=""> </el-table-column>
						<el-table-column prop="customerName" label="客户" show-overflow-tooltip="" />
						<el-table-column prop="warehouseId" label="仓库" show-overflow-tooltip="">
							<template #default="scope">
								{{ filterSlots(scope.row.warehouseId, counterStore.warehouseList) }}
							</template>
						</el-table-column>
						<el-table-column prop="outboundtype" label="出库类型" width="90" show-overflow-tooltip>
							<template #default="scope">
								<el-tag :type="scope.row.outboundtype === 1 ? 'success' : 'danger'">{{ filterSlots(scope.row.outboundtype, counterStore.outboundStatusList) }}</el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="outboundstatus" label="出库状态" width="90" show-overflow-tooltip="">
							<template #default="scope">
								<el-tag type="danger" v-if="scope.row.outboundstatus === 0">待提交</el-tag>
								<el-tag type="danger" v-if="scope.row.outboundstatus === 1">待出库</el-tag>
								<el-tag type="danger" v-if="scope.row.outboundstatus === 2">部分出库</el-tag>
								<el-tag type="success" v-if="scope.row.outboundstatus === 3">已出库</el-tag>
								<el-tag type="success" v-if="scope.row.outboundstatus === 4">已中止</el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="stockOrNot" label="是否缺货" width="65" show-overflow-tooltip="">
							<template #default="scope">
								<el-tag type="danger" v-if="scope.row.stockOrNot">缺</el-tag>
								<el-tag v-else>否</el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="createUserName" label="创建人" show-overflow-tooltip="" />
						<el-table-column prop="createTime" label="创建时间" show-overflow-tooltip="" />
						<el-table-column prop="remark" label="备注" show-overflow-tooltip="" />
						<!-- <el-table-column label="修改记录" width="65" align="center" fixed="right" show-overflow-tooltip>
							<template #default="scope">
								<ModifyRecord :data="scope.row" />
							</template>
						</el-table-column> -->
						<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip="" v-if="auth('warehouseout:edit') || auth('warehouseout:delete')">
							<template #default="scope">
								<el-button 
									icon="ele-Edit" 
									size="small" 
									text="" 
									type="primary" 
									@click="(event) => { 
										event.stopPropagation(); 
										try {
											const isSelected = selectedRows.value.some(item => item.id === scope.row.id);
											if (!isSelected) {
												programmaticSelection.value = true;
												inTable.value.toggleRowSelection(scope.row, true);
											}
										} catch (err) {
											console.error('选中行出错:', err);
										}
										openEditWarehouseout(scope.row);
									}" 
									v-auth="'warehouseout:edit'" 
									:disabled="scope.row.outboundstatus != 0">
									编辑
								</el-button>
								<el-button 
									icon="ele-Delete" 
									size="small" 
									text="" 
									type="primary" 
									@click="(event) => { 
										event.stopPropagation(); 
										try {
											const isSelected = selectedRows.value.some(item => item.id === scope.row.id);
											if (!isSelected) {
												programmaticSelection.value = true;
												inTable.value.toggleRowSelection(scope.row, true);
											}
										} catch (err) {
											console.error('选中行出错:', err);
										}
										delWarehouseout(scope.row);
									}" 
									v-auth="'warehouseout:delete'" 
									:disabled="scope.row.outboundstatus != 0">
									删除
								</el-button>
							</template>
						</el-table-column>
					</el-table>
					<el-pagination
						v-model:currentPage="tableParams.page"
						v-model:page-size="tableParams.pageSize"
						:total="tableParams.total"
						:page-sizes="[10, 20, 50, 100]"
						small=""
						background=""
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
						layout="total, sizes, prev, pager, next, jumper"
					/>
					<editDialog ref="editDialogRef" :title="editWarehouseoutTitle" @reloadTable="handleQuery" />
				</el-card>
			</pane>
			<pane :size="BomSize" ref="bomCardRef">
				<warehouseoutMX
					class="ckd-detail"
					:orderDetail="orderDetail"
					:outOrder="outOrder"
					:outBtnStatus="outBtnStatus"
					ref="order"
					:bomHeight="bomHeight"
					:detailsData="currentDetailsData"
					@reloadTable="handleQuery('current', tabls)"
				/>
			</pane>
		</splitpanes>
	</div>
</template>

<script lang="ts" setup="" name="warehouseout">
import { reactive, ref, getCurrentInstance, onMounted, created } from 'vue';
import useCounter from '/@/stores/counter';
import { ElMessageBox, ElMessage } from 'element-plus';
import { auth } from '/@/utils/authFunction';
import warehouseoutMX from '/@/views/main/warehouseoutMX/index.vue';
import { pageWarehouseoutMX } from '/@/api/main/warehouseoutMX';

import editDialog from '/@/views/main/warehouseout/component/editDialog.vue';
import { pageWarehouseout, deleteWarehouseout, Suspend, Outbound, Submit, Withdraw } from '/@/api/main/warehouseout';
import { Splitpanes, Pane } from 'splitpanes';
import 'splitpanes/dist/splitpanes.css';
import { create } from 'domain';
import { Pubcustom } from '/@/api/main/pubcustom';
// import ModifyRecord from '/@/components/table/modifyRecord.vue';
import { useRoute } from 'vue-router';
import { cachedRequest } from '/@/utils/request-cache';

const route = useRoute();

//父级传递来的函数，用于回调
const editDialogRef = ref();
const editDiaNumRef = ref();
const ckdMX = ref();
const loading = ref(false);
const tableData = ref<any>([]);
const queryParams = ref<{
	outOrder?: string;
	goodsName?: string;
	customerName?: string;
	superiorNum?: string;
	outBoundStatus?: number | null;
}>({
	outOrder: '',
	goodsName: '',
	customerName: '',
	superiorNum: '',
	outBoundStatus: null,
});

let initialized = false;

//加初始化方法
const initializeFilters = () => {
	const statusFromRoute = route.query.outBoundStatus;
	if (statusFromRoute) {
		// 确保将字符串转换为数字数组
		queryParams.value.outBoundStatus = Array.isArray(statusFromRoute) 
			? statusFromRoute.map(Number) 
			: [Number(statusFromRoute)];
	} else {
		// 确保初始化为空数组
		queryParams.value.outBoundStatus = [];
	}
	handleQuery();
	initialized = true;
};

// 在组件挂载时初始化
onMounted(() => {
	initializeFilters();
	
	// 检查表格引用是否正确
	setTimeout(() => {
		if (inTable.value) {
			console.log("表格引用正确");
		} else {
			console.error("表格引用不存在，请检查ref设置");
		}
	}, 500);
});

const tabls = ref<any>({});
const tableParams = ref({
	page: 1,
	pageSize: 10,
	total: 0,
});
const outStatusList = ref([
	{
		label: '待提交',
		value: 0,
	},
	{
		label: '待出库',
		value: 1,
	},
	{
		label: '部分出库',
		value: 2,
	},
	{
		label: '已出库',
		value: 3,
	},
	{
		label: '已中止',
		value: 4,
	},
]); //出库状态列表
const counterStore = useCounter();
const filterSlots = (prop: any, list: any) => {
	if (!list.length) {
		return prop;
	}
	return list.find((item: { value: any }) => item.value === prop)?.label || prop;
};
const topSize = ref('58%');
const BomSize = ref('42%');
const tableHeight = ref('340');
const bomHeight = ref('220');
const pagesInstance = getCurrentInstance();
const topCardRef = ref(null);
const bomCardRef = ref(null);
const withdrawBtnStatus = ref(true); //撤回按钮控制可用
const submitBtnStatus = ref(true); //提交按钮控制可用
const editWarehouseoutTitle = ref('');
const editWarehouseOutNumTitle = ref('出库数量');
const orderDetail = ref('');
const outOrder = ref('');
const outBtnStatus = ref(0); // 出库状态：0-待提交，1-待出库，2-部分出库，3-已出库，4-已中止
const getPagePubcustomList = ref([]); // 客户列表
const selectedRows = ref<any[]>([]);

// 添加一个标记，用于识别程序触发的选择
const programmaticSelection = ref(false);

// 添加防抖函数和批量处理逻辑
const debounceTimer = ref(null);
const pendingDetailQueries = ref(new Set());

// 防抖的明细查询函数
const debouncedDetailQuery = (rowIds: number[]) => {
	// 清除之前的定时器
	if (debounceTimer.value) {
		clearTimeout(debounceTimer.value);
	}
	
	// 添加到待查询队列
	rowIds.forEach(id => pendingDetailQueries.value.add(id));
	
	// 设置新的定时器，300ms后执行批量查询
	debounceTimer.value = setTimeout(async () => {
		const idsToQuery = Array.from(pendingDetailQueries.value);
		pendingDetailQueries.value.clear();
		
		if (idsToQuery.length > 0) {
			await batchQueryDetails(idsToQuery);
		}
	}, 300);
};

// 批量查询明细数据
const batchQueryDetails = async (rowIds: number[]) => {
	try {
		const rows = tableData.value.filter(row => rowIds.includes(row.id));
		
		// 分批处理，每批最多5个请求
		const batchSize = 5;
		for (let i = 0; i < rows.length; i += batchSize) {
			const batch = rows.slice(i, i + batchSize);
			
			// 并发执行当前批次的查询
			const promises = batch.map(async (row) => {
				try {
					let params = {
						OutId: row.id,
						page: 1,
						pageSize: 1000
					};
					
					// 使用缓存请求
					const res = await cachedRequest(
						'warehouseoutMX/page',
						params,
						() => pageWarehouseoutMX(params),
						2 * 60 * 1000 // 2分钟缓存
					);
					
					const details = res.data.result?.items ?? [];
					
					return {
						rowId: row.id,
						outOrder: row.outOrder,
						details: details.map(item => ({
							...item,
							outOrder: row.outOrder,
							outId: row.id
						}))
					};
				} catch (error) {
					console.error(`查询明细失败 - 出库单ID: ${row.id}`, error);
					return { rowId: row.id, outOrder: row.outOrder, details: [] };
				}
			});
			
			const batchResults = await Promise.all(promises);
			
			// 更新明细数据
			batchResults.forEach(result => {
				if (result.details.length > 0) {
					// 检查是否已存在相同ID的明细数据，避免重复添加
					const existingDetailIds = currentDetailsData.value.map(item => item.id);
					const newDetails = result.details.filter(item => !existingDetailIds.includes(item.id));
					
					if (newDetails.length > 0) {
						currentDetailsData.value = [...currentDetailsData.value, ...newDetails];
						console.log(`批量查询: 为出库单 ${result.outOrder} 添加 ${newDetails.length} 条明细数据`);
					}
				}
			});
			
			// 批次间添加小延迟，避免过于频繁的请求
			if (i + batchSize < rows.length) {
				await new Promise(resolve => setTimeout(resolve, 100));
			}
		}
		
		console.log(`批量查询完成，当前明细表总数据: ${currentDetailsData.value.length} 条`);
	} catch (error) {
		console.error('批量查询明细数据失败:', error);
		ElMessage.error('查询明细数据失败，请重试');
	}
};

// 修改选择变化处理函数
const handleSelectionChange = (selection: any[]) => {
	// 如果是程序触发的选择，不执行取消选择的逻辑
	if (programmaticSelection.value) {
		programmaticSelection.value = false;
		selectedRows.value = selection;
		
		// 找出新增的选中行ID
		const previousSelectedIds = selectedRows.value.map(row => row.id);
		const currentSelectedIds = selection.map(row => row.id);
		const newlySelectedIds = currentSelectedIds.filter(id => !previousSelectedIds.includes(id));
		
		// 使用防抖批量查询
		if (newlySelectedIds.length > 0) {
			debouncedDetailQuery(newlySelectedIds);
		}
		return;
	}
	
	// 正常的选择变化处理逻辑
	// 保存之前选中的行ID
	const previousSelectedIds = selectedRows.value.map(row => row.id);
	
	// 更新选中行
	selectedRows.value = selection;
	
	// 找出新增的选中行ID
	const currentSelectedIds = selection.map(row => row.id);
	const newlySelectedIds = currentSelectedIds.filter(id => !previousSelectedIds.includes(id));
	
	// 找出被取消选中的行ID
	const deselectedIds = previousSelectedIds.filter(id => !currentSelectedIds.includes(id));
	
	console.log(`选择变化: 新增${newlySelectedIds.length}行, 取消${deselectedIds.length}行`);
	
	// 使用防抖批量查询新增的行
	if (newlySelectedIds.length > 0) {
		debouncedDetailQuery(newlySelectedIds);
	}
	
	// 处理取消选中的行
	if (deselectedIds.length > 0) {
		// 从明细中移除这些行的明细数据
		currentDetailsData.value = currentDetailsData.value.filter(
			item => !deselectedIds.includes(item.outId)
		);
		console.log(`取消勾选: 移除对应明细数据后剩余 ${currentDetailsData.value.length} 条`);
	}
};

const rowClassName = (row: TableItem) => {
	console.log(row.row);
	return row.row.isSelected ? 'current-row' : '';
};
// 拖动改变表格高度
const handleTz = (value) => {
	topSize.value = value[0].size;
	BomSize.value = value[1].size;
	const height = window.innerHeight - 200;
	console.log('浏览器高度', height);
	tableHeight.value = (parseFloat(pagesInstance.refs.topCardRef.style.height) / 100) * height - 80;
	console.log(tableHeight.value);
	bomHeight.value = (parseFloat(pagesInstance?.refs.bomCardRef.style.height) / 100) * height - 110;
	console.log(tableHeight.value, bomHeight.value);
};

const inTable = ref();
// 修改查询函数，确保清空明细
const handleQuery = async (type?: string, row?: any) => {
	loading.value = true;
	tableData.value = [];
	
	// 清空明细数据
	if (type !== 'current' && type !== 'preserve') {
		currentDetailsData.value = [];
		if (inTable.value) {
			inTable.value.clearSelection();
		}
	}
	
	// 确保outBoundStatus存在且是数组
	if (!queryParams.value.outBoundStatus) {
		queryParams.value.outBoundStatus = [];
	}
	
	// 创建一个新对象作为参数，避免引用原对象导致的问题
	let params = {
		page: tableParams.value.page,
		pageSize: tableParams.value.pageSize,
		// 条件 - 只传输必要的字段
		outOrder: queryParams.value.outOrder || '',
		goodsName: queryParams.value.goodsName || '',
		customerName: queryParams.value.customerName || '',
		superiorNum: queryParams.value.superiorNum || '',
		// 关键改变: 将数组转换为逗号分隔的字符串
		outBoundStatusString: Array.isArray(queryParams.value.outBoundStatus) && queryParams.value.outBoundStatus.length > 0 
			? queryParams.value.outBoundStatus.join(',') 
			: ''
	};
	
	// 删除会导致问题的数组字段
	delete params.outBoundStatus;
	
	console.log("查询参数:", params);
	
	try {
		var res = await pageWarehouseout(params);
		tableData.value = res.data.result?.items ?? [];
		tableParams.value.total = res.data.result?.total;
	} catch (error) {
		console.error("查询出错:", error);
		ElMessage.error('查询出错，请检查参数');
	}
	loading.value = false;
	
	// 处理不同类型的查询结果
	if (type === 'default') {
		// 对于默认情况，选中第一行
		if (tableData.value.length > 0) {
			inTable.value.setCurrentRow(tableData.value[0]);
			handleRowClick(tableData.value[0]);
		} else {
			tabls.value = {};
			orderDetail.value = '';
			outOrder.value = '';
		}
	} else if (type === 'current' || type === 'preserve') {
		// 对于编辑、出库操作或保留当前行的操作，选中指定行
		if (row) {
			const targetRow = tableData.value.find((r) => r.id === row.id);
			if (targetRow) {
				inTable.value.setCurrentRow(targetRow);
				handleRowClick(targetRow);
			}
		}
	}
	// 对于 'noselect' 类型，不选中任何行，也不触发任何行点击事件
};

// 查看一下pageWarehouseout的实现细节
console.log("pageWarehouseout函数:", pageWarehouseout.toString());

// 修改查询出库单明细数据的函数 - 保持原有函数用于单行查询
const ckdDetailQuery = async (row: any) => {
	let params = {
		OutId: row.id,
		page: 1,
		pageSize: 1000 // 确保获取所有数据
	};
	
	// 使用缓存请求
	const res = await cachedRequest(
		'warehouseoutMX/page',
		params,
		() => pageWarehouseoutMX(params),
		2 * 60 * 1000 // 2分钟缓存
	);
	
	ckdMX.value = res.data.result?.items ?? [];
	
	// 确保每条数据都有出库单号和出库单ID
	ckdMX.value = ckdMX.value.map(item => ({
		...item,
		outOrder: row.outOrder,
		outId: row.id
	}));
	
	console.log(`查询到明细数据 ${ckdMX.value.length} 条`);
};

// 修改提交函数
const Submits = async () => {
	if (selectedRows.value.length == 0) {
		ElMessage.warning('请先选中要提交的记录');
		return;
	}
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		if (item.outboundstatus == 0) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.outOrder + ' 状态不正确，无法提交');
			return;
		}
	}
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要提交吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await Submit(listPurchaseIds.value);
			
			// 清空明细表
			currentDetailsData.value = [];
			
			// 完全重新加载数据 - 但不自动选中任何行
			await handleQuery('noselect');
			
			ElMessage.success('提交成功');
		})
		.catch(() => {});
};

// 修改撤回函数
const Withdraws = async () => {
	if (selectedRows.value.length == 0) {
		ElMessage.warning('请先选中要撤回的记录');
		return;
	}
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		if (item.outboundstatus == 1 || item.outboundstatus == 2) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.outOrder + ' 状态不正确，无法撤回');
			return;
		}
	}
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要撤回吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await Withdraw(listPurchaseIds.value);
			
			// 清空明细表
			currentDetailsData.value = [];
			
			// 完成后重新查询但不自动选中任何行
			await handleQuery('noselect');
			
			ElMessage.success('撤回成功');
		})
		.catch(() => {});
};

// 修改中止函数
const GetOutbound = (row: any) => {
	if (selectedRows.value.length == 0) {
		ElMessage.warning('请先选中要中止的记录');
		return;
	}
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		if (item.outboundstatus == 2) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.outOrder + ' 状态不正确，无法中止');
			return;
		}
	}
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要中止吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await Suspend(listPurchaseIds.value);
			
			// 清空明细表
			currentDetailsData.value = [];
			
			// 完成后重新查询但不自动选中任何行
			await handleQuery('noselect');
			
			ElMessage.success('已中止');
		})
		.catch(() => {});
};

// 重置查询条件，确保正确初始化所有字段
const resetQuery = () => {
	// 重置为一个全新对象，但保留outBoundStatus为空数组
	queryParams.value = {
		outOrder: '',
		goodsName: '',
		customerName: '',
		superiorNum: '',
		outBoundStatus: null, // 初始为null
	};
	handleQuery();
};

// 打开新增页面
const openAddWarehouseout = () => {
	editWarehouseoutTitle.value = '添加商品出库';
	editDialogRef.value.openDialog({}, []);
};

// 打开编辑页面
const openEditWarehouseout = async (row: any) => {
	await ckdDetailQuery(row);
	editWarehouseoutTitle.value = '编辑商品出库';
	editDialogRef.value.openDialog(row, ckdMX.value);
};

// 删除
const delWarehouseout = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await deleteWarehouseout(row);
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	tableParams.value.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	tableParams.value.page = val;
	handleQuery();
};

// 为所有按钮创建一个通用处理函数
const handleButtonWithBlur = (event, callback) => {
	callback();
	// 确保按钮失去焦点
	if (event.target) {
		event.target.blur();
	}
};

// 客户列表
const khList = async () => {
	let list = await Pubcustom();
	getPagePubcustomList.value = list.data.result ?? [];
};
khList();

// 添加一个新的响应式引用，用于存储明细数据
const currentDetailsData = ref<any[]>([]);

// 修复行点击函数
const handleRowClick = async (row: any) => {
	console.log("行点击事件触发:", row.outOrder);
	
	// 保存当前行数据
	tabls.value = row;
	orderDetail.value = row.id;
	outOrder.value = row.outOrder;
	
	// 切换当前行的选中状态
	const isCurrentlySelected = selectedRows.value.some(item => item.id === row.id);
	console.log("当前行选中状态:", isCurrentlySelected);
	
	// 确保inTable引用存在再调用方法
	if (inTable.value) {
		console.log("切换行选中状态");
		inTable.value.toggleRowSelection(row, !isCurrentlySelected);
	} else {
		console.error("表格引用不存在!");
	}
	
	// 查询当前行的明细数据
	await ckdDetailQuery(row);
	
	if (!isCurrentlySelected) {
		// 检查是否已存在相同ID的明细数据，避免重复添加
		const existingDetailIds = currentDetailsData.value.map(item => item.id);
		
		// 只添加不存在的明细
		const newDetails = ckdMX.value.filter(item => !existingDetailIds.includes(item.id))
			.map(item => ({
				...item,
				outOrder: row.outOrder,
				outId: row.id
			}));
		
		console.log(`添加 ${newDetails.length} 条新明细数据`);
		
		// 将过滤后的明细添加到已有明细中
		currentDetailsData.value = [...currentDetailsData.value, ...newDetails];
	} else {
		// 如果取消选中，从明细中移除该行的明细数据
		currentDetailsData.value = currentDetailsData.value.filter(
			item => item.outId !== row.id
		);
	}
	
	console.log(`当前明细表总数据: ${currentDetailsData.value.length} 条`);
	
	// 设置出库状态为当前行的状态值
	outBtnStatus.value = row.outboundstatus;
};

handleQuery();
</script>
<style lang="scss" scoped>
.topCard {
	margin-top: 8px;
	height: 100%;
}

.toptable {
	width: 100%;
}

:deep(.splitpanes.default-theme .splitpanes__splitter) {
	background: #f0f0f0;
}

@media (max-height: 920px) {
	.default-theme {
		height: calc(80vh - 50px) !important;
	}
}

@media (max-height: 768px) {
	.default-theme {
		height: calc(80vh - 100px) !important;
	}
}

@media (max-height: 640px) {
	.default-theme {
		height: calc(80vh - 120px) !important;
	}
}
</style>
