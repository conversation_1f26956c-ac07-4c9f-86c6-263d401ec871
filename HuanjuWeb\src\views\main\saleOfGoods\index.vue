﻿<template>
  <div class="saleOfGoods-container">
    <!-- <el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
      <el-form :model="queryParams" ref="queryForm" :inline="true"> -->
    <!--         <el-form-item label="商品名称">
          <el-input v-model="queryParams.tradeName" clearable="" placeholder="请输入商品名称"/>
          
        </el-form-item>
        <el-form-item label="合同数量">
          <el-input-number v-model="queryParams.contractNum"  clearable="" placeholder="请输入合同数量"/>
          
        </el-form-item> -->
    <!--         <el-form-item label="销售单号">
          <el-input v-model="queryParams.salesOrder" clearable="" placeholder="请输入销售单号"/>
          
        </el-form-item> -->
    <!-- <el-form-item> -->
    <!--           <el-button-group>
            <el-button type="primary"  icon="ele-Search" @click="handleQuery" v-auth="'saleOfGoods:page'"> 查询 </el-button>
            <el-button icon="ele-Refresh" @click="() => queryParams = {}"> 重置 </el-button>
            
          </el-button-group> -->
    <!--           
        </el-form-item> -->
    <!-- <el-form-item> -->
    <!-- <el-button type="primary" icon="ele-Plus" @click="openAddSaleOfGoods" v-auth="'saleOfGoods:add'"> 新增 </el-button> -->

    <!-- </el-form-item>
        
      </el-form> -->
    <!-- </el-card> -->
    <el-card class="full-table" shadow="hover" style="margin-top: 8px">
      <el-table :data="tableData" style="width: 100%" v-loading="loading" tooltip-effect="light" row-key="id" border=""
        :height="bomHeight">
        <el-table-column type="index" label="序号" width="55" align="center" fixed="" />
        <el-table-column prop="tradeName" label="商品名称" show-overflow-tooltip="" />
        <el-table-column prop="productCode" label="商品编码" show-overflow-tooltip="" />
        <el-table-column prop="brandName" label="品牌" show-overflow-tooltip="" />
        <el-table-column prop="specsName" label="规格" show-overflow-tooltip="" />
        <el-table-column prop="unitName" label="单位" show-overflow-tooltip="" />
        <el-table-column prop="puchPrice" label="单价" show-overflow-tooltip="" />
        <el-table-column prop="puchQty" label="数量" show-overflow-tooltip="" />
        <el-table-column prop="puchAmt" label="销售金额" show-overflow-tooltip="" />
        <!-- <el-table-column prop="contractNum" label="合同数量" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="salesOrder" label="销售单号" fixed="" show-overflow-tooltip="" /> -->
        <!-- <el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip=""
          v-if="auth('saleOfGoods:edit') || auth('saleOfGoods:delete')">
          <template #default="scope">
            <el-button icon="ele-Edit" size="small" text="" type="primary" @click="openEditSaleOfGoods(scope.row)"
              v-auth="'saleOfGoods:edit'"> 编辑 </el-button>
            <el-button icon="ele-Delete" size="small" text="" type="primary" @click="delSaleOfGoods(scope.row)"
              v-auth="'saleOfGoods:delete'"> 删除 </el-button>
          </template>
        </el-table-column> -->
      </el-table>
      <el-pagination v-model:currentPage="tableParams.page" v-model:page-size="tableParams.pageSize"
        :total="tableParams.total" :page-sizes="[10, 20, 50, 100]" small="" background="" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" layout="total, sizes, prev, pager, next, jumper" />
      <editDialog ref="editDialogRef" :title="editSaleOfGoodsTitle" :orderId="props.outOrderId"
        @reloadTable="handleQuery" />
    </el-card>
  </div>
</template>

<script lang="ts"  name="saleOfGoods" setup>
import { ref, onMounted } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { auth } from '/@/utils/authFunction';
//import { formatDate } from '/@/utils/formatTime';

import editDialog from '/@/views/main/saleOfGoods/component/editDialog.vue'
import { pageSaleOfGoods, deleteSaleOfGoods } from '/@/api/main/saleOfGoods';
//父级传递来的参数
const props = defineProps<{
  bomHeight: String,
  outOrderId: String
}>();
const tabList = ref([
  {
    name: '履约计划'
  },
  {
    name: '商品明细'
  },
  {
    name: '合同附件'
  }
]);
// 查询操作
const handleQuery = async (Id: any) => {
  //debugger;
  queryParams.value.salesOrder = Id;
  loading.value = true;
  var res = await pageSaleOfGoods(Object.assign(queryParams.value, tableParams.value));
  tableData.value = res.data.result?.items ?? [];
  tableParams.value.total = res.data.result?.total;
  loading.value = false;
};

//listPurchaseIds
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const editDialogRef = ref();
const loading = ref(false);
const tableData = ref<any>
  ([]);
const queryParams = ref<any>
  ({});
const tableParams = ref({
  page: 1,
  pageSize: 10,
  total: 0,
});
const editSaleOfGoodsTitle = ref("");






// 打开新增页面
const openAddSaleOfGoods = () => {
  editSaleOfGoodsTitle.value = '添加商品明细';
  editDialogRef.value.openDialog({});
};

// 打开编辑页面
const openEditSaleOfGoods = (row: any) => {
  editSaleOfGoodsTitle.value = '编辑商品明细';
  editDialogRef.value.openDialog(row);
};

// 删除
const delSaleOfGoods = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      await deleteSaleOfGoods(row);
      //  handleQueryss();
      ElMessage.success("删除成功");
    })
    .catch(() => { });
};

// 改变页面容量
const handleSizeChange = (val: number) => {
  tableParams.value.pageSize = val;
  //   handleQueryss();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
  tableParams.value.page = val;
  //  handleQueryss();
};


// handleQueryss();
// 页面加载时
onMounted(async () => {
});
//将属性或者函数暴露给父组件
defineExpose({ handleQuery });
</script>


