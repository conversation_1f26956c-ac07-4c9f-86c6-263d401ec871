﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 唯一码库基础输入参数
    /// </summary>
    public class WarehouseuniquecodeBaseInput
    {
        /// <summary>
        /// 序号
        /// </summary>
        public virtual long Order { get; set; }
        
        /// <summary>
        /// 商品名称
        /// </summary>
        public virtual string? TradeName { get; set; }
        
        /// <summary>
        /// 商品编码
        /// </summary>
        public virtual string? TradeCode { get; set; }
        
        /// <summary>
        /// 唯一码
        /// </summary>
        public virtual string? UniqueCode { get; set; }
        
        /// <summary>
        /// 供应商
        /// </summary>
        public virtual string? Supplier { get; set; }
        
        /// <summary>
        /// 供应商ID
        /// </summary>
        public virtual string? SupplierID { get; set; }
        
        /// <summary>
        /// 商品属性
        /// </summary>
        public virtual string? Attributes { get; set; }
        
    }

    /// <summary>
    /// 唯一码库分页查询输入参数
    /// </summary>
    public class WarehouseuniquecodeInput : BasePageInput
    {
        /// <summary>
        /// 商品名称
        /// </summary>
        public string? TradeName { get; set; }
        
        /// <summary>
        /// 唯一码
        /// </summary>
        public string? UniqueCode { get; set; }
        
        /// <summary>
        /// 供应商
        /// </summary>
        public string? Supplier { get; set; }
        
    }

    /// <summary>
    /// 唯一码库增加输入参数
    /// </summary>
    public class AddWarehouseuniquecodeInput : WarehouseuniquecodeBaseInput
    {
    }

    /// <summary>
    /// 唯一码库删除输入参数
    /// </summary>
    public class DeleteWarehouseuniquecodeInput : BaseIdInput
    {
    }

    /// <summary>
    /// 唯一码库更新输入参数
    /// </summary>
    public class UpdateWarehouseuniquecodeInput : WarehouseuniquecodeBaseInput
    {
    }

    /// <summary>
    /// 唯一码库主键查询输入参数
    /// </summary>
    public class QueryByIdWarehouseuniquecodeInput : DeleteWarehouseuniquecodeInput
    {

    }
