﻿<template>
	<div class="paymentOrder-container">
		<el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="queryParams" ref="queryForm" :inline="true">
				<el-form-item label="单位名称">
					<el-input v-model="queryParams.unitName" clearable="" placeholder="请输入单位名称" />
				</el-form-item>
				<el-form-item label="创建人">
					<el-input v-model="queryParams.createUserName" clearable="" placeholder="请输入创建人" />
				</el-form-item>
				<!--         <el-form-item label="发票状态">
          <el-input-number v-model="queryParams.invoiceStatus" clearable="" placeholder="请输入发票状态" />

        </el-form-item> -->

				<el-form-item label="发票状态">
					<el-select clearable v-model="queryParams.invoiceStatus" placeholder="请选择发票状态">
						<el-option v-for="(item, index) in invoiceStatus" :key="index" :value="item.value" :label="item.label"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="付款状态">
					<el-select v-model="queryParams.paymentStatus" multiple placeholder="请选择付款状态">
						<el-option v-for="(item, index) in paymentStatus" :key="index" :value="item.value" :label="item.label"></el-option>
					</el-select>
				</el-form-item>
				<div style="margin-top: -10px; margin-bottom: 10px">
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'paymentOrder:page'"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
					<el-button type="primary" icon="ele-Plus" @click="openAddPaymentOrder" v-auth="'paymentOrder:add'" style="margin-left: 10px"> 新增 </el-button>
					<el-button type="primary" icon="ele-Plus" @click="commitButtonClick"> 提交 </el-button>
					<el-button type="primary" icon="ele-Back" @click="withdraws"> 撤回 </el-button>
					<el-button type="primary" icon="ele-Finished" @click="suspend"> 中止 </el-button>
				</div>
				<!-- <el-form-item>
          <el-button type="primary" icon="ele-Plus" @click="copyPaymentOrder" v-auth="'paymentOrder:add'"> 复制 </el-button>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="ele-Plus" @click="exportPaymentOrder" v-auth="'paymentOrder:add'"> 导出
          </el-button>
        </el-form-item> -->
			</el-form>
		</el-card>
		<el-card class="full-table" shadow="hover" style="margin-top: 8px">
			<el-table
				:data="tableData"
				:row-class-name="rowClassName"
				style="width: 100%"
				v-loading="loading"
				highlight-current-row
				tooltip-effect="light"
				row-key="id"
				border=""
				@row-click="handleRowClick"
				@selection-change="handleSelectionChange"
			>
				<el-table-column type="selection" width="55" align="center" />
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="paymentNo" label="付款单号" width="100" show-overflow-tooltip="" />
				<el-table-column prop="unitName" label="单位名称" width="120" show-overflow-tooltip="" />
				<!-- <el-table-column prop="contractNum" label="合同编号" show-overflow-tooltip="" /> -->
				<el-table-column prop="purchaseNumber" label="上级单号" width="100" show-overflow-tooltip="" />
				<el-table-column prop="abstract" label="摘要" show-overflow-tooltip="" />
				<el-table-column prop="documentAmount" label="单据金额" show-overflow-tooltip="" />
				<el-table-column prop="amountPaid" label="已付金额" show-overflow-tooltip="" />
				<el-table-column prop="amountToBePaid" label="待付金额" show-overflow-tooltip="">
					<template #default="scope">
						<text>{{ scope.row.documentAmount - scope.row.amountPaid }}</text>
					</template>
				</el-table-column>
				<el-table-column prop="paymentStatus" label="付款状态" show-overflow-tooltip="">
					<template #default="scope">
						<el-tag type="danger" v-if="scope.row.paymentStatus === 0">待提交</el-tag>
						<el-tag type="danger" v-if="scope.row.paymentStatus === 1">待付款</el-tag>
						<el-tag type="danger" v-if="scope.row.paymentStatus === 2">部分付款</el-tag>
						<el-tag type="success" v-if="scope.row.paymentStatus === 3">已付款</el-tag>
						<el-tag type="success" v-if="scope.row.paymentStatus === 4">已中止</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="invoiceNo" label="发票号码" show-overflow-tooltip="" />
				<el-table-column prop="receivedAmount" label="已收票金额" show-overflow-tooltip="" />

				<el-table-column prop="invoiceStatus" label="发票状态" show-overflow-tooltip="">
					<template #default="scope">
						<el-tag type="danger" v-if="scope.row.invoiceStatus === 0">无票</el-tag>
						<el-tag type="danger" v-if="scope.row.invoiceStatus === 1">待开</el-tag>
						<el-tag type="danger" v-if="scope.row.invoiceStatus === 2">已开</el-tag>
						<el-tag type="danger" v-if="scope.row.invoiceStatus === 3">申开</el-tag>
					</template>
				</el-table-column>
				<!-- <el-table-column prop="amountReceived" label="已收金额" show-overflow-tooltip="" />
        <el-table-column prop="invoicedAmount" label="已开票金额"  show-overflow-tooltip="" /> -->
				<!-- <el-table-column prop="incomeType" label="收入类型" show-overflow-tooltip="" /> -->
				<!--         <el-table-column prop="incomeCategory" label="支出名目" fixed="" show-overflow-tooltip="" /> -->
				<!-- <el-table-column prop="incomeCategory" label="收入名目" show-overflow-tooltip="">
          <template #default="scope">
            <el-tag type="danger" v-if="scope.row.incomeCategory === 0">销售收入</el-tag>
            <el-tag type="danger" v-if="scope.row.incomeCategory === 1">非销售收入</el-tag>
          </template>
        </el-table-column> -->
				<!--         <el-table-column prop="paymentStatus" label="付款状态" fixed="" show-overflow-tooltip="" /> -->

				<!--         <el-table-column prop="trading" label="交易方式" fixed="" show-overflow-tooltip="" /> -->
				<el-table-column prop="tradingName" label="交易账户" show-overflow-tooltip="" />
				<!-- <template #default="scope">
            <el-tag type="danger" v-if="scope.row.trading === 0">现金</el-tag>
            <el-tag type="danger" v-if="scope.row.trading === 1">对公</el-tag>
            <el-tag type="danger" v-if="scope.row.trading === 2">支付宝</el-tag>
            <el-tag type="danger" v-if="scope.row.trading === 3">微信</el-tag>
          </template>
        </el-table-column> -->
				<el-table-column prop="expenditureCategory" label="科目名称" show-overflow-tooltip="">
					<!-- <template #default="scope">
						<el-tag type="danger" v-if="scope.row.expenditureCategory === 0">营业成本</el-tag>
						<el-tag type="danger" v-if="scope.row.expenditureCategory === 1">采购成本</el-tag>
						<el-tag type="danger" v-if="scope.row.expenditureCategory === 2">销售成本</el-tag>
						<el-tag type="danger" v-if="scope.row.expenditureCategory === 3">交付成本</el-tag>
						<el-tag type="danger" v-if="scope.row.expenditureCategory === 4">财务成本</el-tag>
					</template> -->
				</el-table-column>
				<!-- <el-table-column prop="levelPubjects" label="二级科目" show-overflow-tooltip="" /> -->
				<el-table-column prop="secondaryAccount" label="科目代码" show-overflow-tooltip="">
					<!-- <template #default="scope">
						<el-tag type="danger" v-if="scope.row.secondaryAccount === 0">销售收入</el-tag>
						<el-tag type="danger" v-if="scope.row.secondaryAccount === 1">非销售收入</el-tag>
					</template> -->
				</el-table-column>
				<!--         <el-table-column prop="invoiceStatus" label="发票状态" fixed="" show-overflow-tooltip="" /> -->
				<el-table-column prop="createUserName" label="创建人" show-overflow-tooltip="" />
				<el-table-column prop="createTime" label="创建时间" show-overflow-tooltip="" />
				<el-table-column prop="notes" label="备注" show-overflow-tooltip="" />
				<!-- 修改操作列 -->
				<el-table-column label="操作" width="245" align="center" fixed="right" show-overflow-tooltip="" v-if="auth('paymentOrder:edit') || auth('paymentOrder:delete')">
					<template #default="scope">
						<!-- 基础操作按钮 -->
						<el-button icon="ele-Edit" size="small" text type="primary" @click="openEditPaymentOrder(scope.row)" v-auth="'paymentOrder:edit'" :disabled="scope.row.paymentStatus !== 0">
							编辑
						</el-button>

						<!-- 收款按钮 -->
						<el-button icon="ele-Money" size="small" text type="success" @click="paymentOrder(scope.row)" :disabled="!(scope.row.paymentStatus === 1 || scope.row.paymentStatus === 2)">
							付款
						</el-button>

						<!-- 收票按钮 -->
						<el-button icon="ele-Finished" size="small" text type="warning" @click="INVOICEOrder(scope.row)" :disabled="!(scope.row.paymentStatus === 2 || scope.row.paymentStatus === 3)">
							收票
						</el-button>

						<!-- 删除按钮 -->
						<el-button icon="ele-Delete" size="small" text type="danger" @click="delPaymentOrder(scope.row)" v-auth="'paymentOrder:delete'" :disabled="scope.row.paymentStatus !== 0"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="tableParams.page"
				v-model:page-size="tableParams.pageSize"
				:total="tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				small=""
				background=""
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
			<editDialog ref="editDialogRef" :title="editPaymentOrderTitle" @reloadTable="handleQuery" />
			<PaidinAmount ref="PaidinAmountRef" :title="editPaymentOrderTitle" @reloadTable="handleQuery" />
			<TicketAmount ref="TicketAmountRef" :title="editTicketAmountTitle" @reloadTable="handleQuery" />
		</el-card>
	</div>
</template>

<script lang="ts" setup="" name="paymentOrder">
import { ref, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { auth } from '/@/utils/authFunction';
//import { formatDate } from '/@/utils/formatTime';

import editDialog from '/@/views/main/paymentOrder/component/editDialog.vue';
import PaidinAmount from '/@/views/main/paymentOrder/component/PaidinAmount.vue';
import TicketAmount from '/@/views/main/paymentOrder/component/ticketAmount.vue';
import { pagePaymentOrder, deletePaymentOrder, Import, Submit, Retract, Suspend } from '/@/api/main/paymentOrder';
import { useRoute } from 'vue-router';

const editDialogRef = ref();
const PaidinAmountRef = ref();
const TicketAmountRef = ref();
const tableRow = ref({});
const submitBtnStatus = ref(true); //提交按钮
const loading = ref(false);
const paymentButton = ref(true);
const tableData = ref<any>([]);
// 查询参数定义
interface QueryParams {
	paymentStatus: number[];
	[key: string]: any;
}

const queryParams = ref<QueryParams>({
	paymentStatus: [], // 初始化为空数组
	// 其他查询参数...
});

const tableParams = ref({
	page: 1,
	pageSize: 20,
	total: 0,
});
const editPaymentOrderTitle = ref('');
const editTicketAmountTitle = ref('');
const invoiceStatus = [
	{
		label: '无票',
		value: 0,
	},
	{
		label: '待开',
		value: 1,
	},
	{
		label: '已开',
		value: 2,
	},
];
const paymentStatus = [
	{
		label: '待提交',
		value: 0,
	},
	{
		label: '待付款',
		value: 1,
	},
	{
		label: '部分付款',
		value: 2,
	},
	{
		label: '已付款',
		value: 3,
	},
	{
		label: '已中止',
		value: 4,
	},
];
const selectedRows = ref<any>([]); // 保存选中的行数据
const rowClassName = (row: TableItem) => {
	console.log(row.row);
	return row.row.isSelected ? 'current-row' : '';
};
const handleSelectionChange = (selection: TableItem[]) => {
	selectedRows.value = selection;
	if (selection.length != 0) {
		selectedRows.value = selection;
		// 待付款和部分付款可以付款
		if ((selectedRows.value[0].paymentStatus == 1 || selectedRows.value[0].paymentStatus == 2) && selectedRows.value.length == 1) {
			paymentButton.value = false;
		} else {
			paymentButton.value = true;
		}
		// 部分付款和已付款可以收票
		if ((selectedRows.value[0].paymentStatus == 2 || selectedRows.value[0].paymentStatus == 3) && selectedRows.value.length == 1) {
			spBtnStatus.value = false;
		} else {
			spBtnStatus.value = true;
		}
	} else {
		paymentButton.value = true;
		spBtnStatus.value = true;
	}
	for (const row of tableData.value) {
		row.isSelected = false;
	}
	for (const selectedRow of selection) {
		const foundRow = tableData.value.find((row) => row.id === selectedRow.id);
		if (foundRow) {
			foundRow.isSelected = true;
		}
	}
};
// 查询操作
const handleQuery = async () => {
	try {
		loading.value = true;
		const params = {
			...queryParams.value,
			paymentStatus: queryParams.value.paymentStatus.length > 0 
				? queryParams.value.paymentStatus.join(',') 
				: undefined
		};
		
		const res = await pagePaymentOrder(Object.assign(params, tableParams.value));
		tableData.value = res.data.result?.items ?? [];
		tableParams.value.total = res.data.result?.total;
	} catch (error) {
		console.error('查询失败:', error);
	} finally {
		loading.value = false;
	}
};

// 处理路由参数
const route = useRoute();
const initialized = ref(false);

// 初始化过滤条件
const initializeFilters = () => {
	if (initialized.value) return;
 
	const statusFromRoute = route.query.paymentStatus;
	if (statusFromRoute) {
		// 处理路由参数，可能是字符串或字符串数组
		if (Array.isArray(statusFromRoute)) {
			queryParams.value.paymentStatus = statusFromRoute.map(Number);
		} else if (typeof statusFromRoute === 'string') {
			queryParams.value.paymentStatus = statusFromRoute.split(',').map(Number);
		}
	}
	handleQuery();
	initialized.value = true;
};

onMounted(() => {
	initializeFilters();
});

// 重置查询条件
const resetQuery = () => {
	queryParams.value = {};
	handleQuery();
};
// 打开新增页面
const openAddPaymentOrder = () => {
	editPaymentOrderTitle.value = '添加付款单';
	editDialogRef.value.openDialog({});
};

// 打开新增页面
const copyPaymentOrder = () => {
	editPaymentOrderTitle.value = '添加付款单';
	editDialogRef.value.openDialog({});
};
// 打开新增页面
const exportPaymentOrder = async () => {
	var res = await Import(Object.assign(queryParams.value, tableParams.value));
	console.log('导出列表：', res.data);
	let href = window.URL.createObjectURL(new Blob([res.data]));
	let link = document.createElement('a');
	link.style.display = 'none';
	link.href = href;
	link.setAttribute('download', '付款单' + '.xlsx');
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link); // 下载完成移除元素
	window.URL.revokeObjectURL(href); // 释放掉blob对象
};

// 打开新增页面
const paymentOrder = (row: any) => {
	if (!(row.paymentStatus === 1 || row.paymentStatus === 2)) {
		ElMessage.warning('当前状态不能付款');
		return;
	}
	editPaymentOrderTitle.value = '确认金额';
	PaidinAmountRef.value.openDialog(row);
};

// 打开新增页面
const INVOICEOrder = (row: any) => {
	if (!(row.paymentStatus === 2 || row.paymentStatus === 3)) {
		ElMessage.warning('当前状态不能收票');
		return;
	}
	editTicketAmountTitle.value = '本次发票金额';
	TicketAmountRef.value.openDialog(row);
};
// 打开编辑页面
const openEditPaymentOrder = (row: any) => {
	editPaymentOrderTitle.value = '编辑付款单';
	editDialogRef.value.openDialog(row);
};

// 删除
const delPaymentOrder = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await deletePaymentOrder(row);
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	tableParams.value.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	tableParams.value.page = val;
	handleQuery();
};
// handleQuery();
// 控制撤回弹窗是否可用
const btnStatus = ref(true);
const spBtnStatus = ref(true);
const fkBtnStatus = ref(true);
const handleRowClick = async (row: any) => {
	tableRow.value = row;
	if (row.paymentStatus === 0) {
		submitBtnStatus.value = false;
	} else {
		submitBtnStatus.value = true;
	}
	if (row.paymentStatus === 1) {
		btnStatus.value = false;
	} else {
		btnStatus.value = true;
	}
};
// 提交
const commitButtonClick = () => {
	// 待提交可以提交
	if (selectedRows.value.length == 0) {
		ElMessage.warning('请先选中要提交的记录');
		return;
	}
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		if (item.paymentStatus == 0 || item.paymentStatus == -1) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.paymentNo + ' 状态不正确，无法提交');
			return;
		}
	}
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要提交吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await Submit(listPurchaseIds.value);
			handleQuery();
			ElMessage.success('提交成功');
		})
		.catch(() => {});
};
// 撤回
const withdraws = () => {
	// 待付款可以撤回
	if (selectedRows.value.length == 0) {
		ElMessage.warning('请先选中要撤回的记录');
		return;
	}
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		if (item.paymentStatus == 1) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.paymentNo + ' 状态不正确，无法撤回');
			return;
		}
	}
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要撤回吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await Retract(listPurchaseIds.value);
			handleQuery();
			ElMessage.success('撤回成功');
		})
		.catch(() => {});
};
const suspend = () => {
	if (selectedRows.value.length == 0) {
		ElMessage.warning('请先选中要中止的记录');
		return;
	}
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		if (item.paymentStatus == 2) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.paymentNo + ' 状态不正确，无法中止');
			return;
		}
	}
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要中止吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await Suspend(listPurchaseIds.value);
			handleQuery();
			ElMessage.success('已中止');
		})
		.catch(() => {});
};
// handleQuery();
</script>
<style scoped>
</style>

