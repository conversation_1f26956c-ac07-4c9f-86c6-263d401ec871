﻿using Admin.NET.Application;
using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Application.Service;
using Admin.NET.Core.Service;
using AngleSharp.Dom;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Magicodes.ExporterAndImporter.Core.Extension;
using NPOI.SS.Formula.Functions;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using static ICSharpCode.SharpZipLib.Zip.ExtendedUnixData;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.CgibinUserInfoBatchGetRequest.Types;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.ChannelsECWarehouseGetResponse.Types;

namespace Admin.NET.Application;
/// <summary>
/// 商品出库服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WarehouseoutService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<Warehousegoods> _repgoods;
    private readonly SqlSugarRepository<Warehouseout> _rep;

    private readonly SqlSugarRepository<WarehouseoutMX> _repmx;

    private readonly SqlSugarRepository<WarehouseStore> _repSale;
    private readonly SqlSugarRepository<warehousebatch> _repbatch;
    private readonly SqlSugarRepository<OutboundRecord> _repOutBound;
    UserManager _userManager;
    private readonly SqlSugarRepository<SalesperFormanceplan> _rep2;
    private readonly SqlSugarRepository<Salescontract> _rep3;
    private readonly SqlSugarRepository<Entity.Warehouse> _wareHouse;

    private readonly SqlSugarRepository<WarehouseBatchUpdateQueue> _repWarehouseBatchUpdateQueue;
    public WarehouseoutService(SqlSugarRepository<Warehouseout> rep, UserManager userManager, SqlSugarRepository<WarehouseoutMX> repmx, SqlSugarRepository<WarehouseStore> repSale, SqlSugarRepository<Warehousegoods> repgoods, SqlSugarRepository<warehousebatch> repbatch, SqlSugarRepository<OutboundRecord> repOutBound, SqlSugarRepository<SalesperFormanceplan> rep2, SqlSugarRepository<Salescontract> rep3, SqlSugarRepository<WarehouseBatchUpdateQueue> repWarehouseBatchUpdateQueue, SqlSugarRepository<Entity.Warehouse> wareHouse)
    {
        _repgoods = repgoods;
        _rep = rep;
        _userManager = userManager;
        _repmx = repmx;
        _repSale = repSale;
        _repbatch = repbatch;
        _repOutBound = repOutBound;
        _rep2 = rep2;
        _rep3 = rep3;
        _repWarehouseBatchUpdateQueue = repWarehouseBatchUpdateQueue;
        _wareHouse = wareHouse;
    }

    /// <summary>
    /// 分页查询商品出库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WarehouseoutOutput>> Page(WarehouseoutInput input)
    {
        var checkStatus = !string.IsNullOrWhiteSpace(input.outBoundStatus) ? input.outBoundStatus.Split(',') : null;
        var query = _rep.AsQueryable()
                    .LeftJoin<SysUser>((u, user) => u.CreateUserId == user.Id)
                    .Where(u => u.TenantId == _userManager.TenantId)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.OutOrder), u => u.OutOrder.Contains(input.OutOrder.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.GoodsName), u => u.GoodsInfo.Contains(input.GoodsName))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.CustomerName), u => u.Pubcustoms.Name.Contains(input.CustomerName))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.TrackingNumber), u => u.TrackingNumber.Contains(input.TrackingNumber.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.SuperiorNum), u => u.SuperiorNum.Contains(input.SuperiorNum.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
                    .WhereIF(checkStatus != null, u => checkStatus.Contains(u.OutboundStatus.ToString(), true))
                    .Where(u => u.IsDelete == false).Distinct()
                    .Select<WarehouseoutOutput>(((u, user) => new WarehouseoutOutput
                    {
                        Id = u.Id,
                        CustomerName = u.Pubcustoms.Name,
                        OutCount = u.OutCount,
                        TrueOutCount = u.TrueOutCount,
                        TrackingNumber = u.TrackingNumber,
                        Remark = u.Remark,
                        Outboundstatus = u.OutboundStatus,
                        OutOrder = u.OutOrder,
                        WarehouseId = u.WarehouseId,
                        CustomId = u.CustomId,
                        SuperiorNum = u.SuperiorNum,
                        Outboundtype = u.Outboundtype,
                        CreateTime = u.CreateTime,
                        CreateUserName = user.RealName,
                        GoodsInfo = u.GoodsInfo,
                        TotalAmt = u.TotalAmt,
                        DiscountAmt = u.DiscountAmt,
                        ActualAmt = u.ActualAmt,
                    }));

        query = query.OrderBuilder(input);
        var sql = query.ToSqlString();
        var list = await query.ToPagedListAsync(input.Page, input.PageSize);

        foreach (var item in list.Items)
        {
            var outMx = _repmx.AsQueryable().Where(x => x.OutId == item.Id).ToListAsync().Result;
            foreach (var mx in outMx)
            {
                var stockOrNot = _repSale.AsQueryable()
                    .Where(s => s.WarehouseId == item.WarehouseId && s.TradeID == mx.goodsId && s.StockOrNot == true).CountAsync().Result;
                if (stockOrNot > 0)
                {
                    item.StockOrNot = true;
                    break;
                }
            }
        }
        return list;
    }

    /// <summary>
    /// 增加商品出库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task<long> Add(AddWarehouseoutMx input)
    {
        var entity = input.addWarehouseoutInput.Adapt<Warehouseout>();
        var listMx = input.listMx.Adapt<List<WarehouseoutMX>>();

        if (entity.Id > 0)

        {
            await _rep.UpdateAsync(entity);
        }
        else
        {
            //var orderNumber = "CK2132131";
            var orderNumber = await App.GetRequiredService<PubOrderService>().GetNewOrder("CK");
            if (orderNumber.IsNullOrEmpty())
            {
                throw Oops.Oh(ErrorCodeEnum.GY1001);
            }

            entity.OutOrder = orderNumber;
            var addSuccess = await _rep.AsInsertable(entity).ExecuteCommandIdentityIntoEntityAsync();
            if (addSuccess)
            {
                if (listMx != null)
                    listMx.ForEach(u => u.OutId = entity.Id);
            }
        }

        if (listMx != null)
        {
            await App.GetRequiredService<WarehouseoutMXService>().AddOrUpdate(listMx);
        }
        return entity.Id;
    }


    /// <summary>
    /// 删除商品出库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteWarehouseoutInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新商品出库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWarehouseoutInput input)
    {
        var entity = input.Adapt<Warehouseout>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        await UpdateStatus(entity.SuperiorNum, entity.OutboundStatus);
    }

    /// <summary>
    /// 获取商品出库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<Warehouseout> Get([FromQuery] QueryByIdWarehouseoutInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 中止出库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Suspend")]
    public async Task Suspend(List<long> listInputs)
    {
        var listRp = await _rep.AsQueryable().Where(u => listInputs.Contains(u.Id)).ToListAsync();
        foreach (var rp in listRp)
        {
            if (rp.OutboundStatus != 2)
            {
                throw Oops.Oh("状态为部分出库才能执行中止操作");
            }

            var listStore = _repSale.AsQueryable()
                .InnerJoin<WarehouseoutMX>((u, mx) => u.WarehouseId == rp.WarehouseId && u.TradeID == mx.goodsId && mx.OutId == rp.Id && mx.GoodProduct && (mx.OutCount ?? 0) != (mx.TrueOutCount ?? 0))
                .Select((u, mx) => new WarehouseStore
                {
                    Id = u.Id,
                    ShippedoutNum = (u.ShippedoutNum ?? 0) - ((mx.OutCount ?? 0) - (mx.TrueOutCount ?? 0)),
                    Compatible = u.GoodProduct - ((u.ShippedoutNum ?? 0) - ((mx.OutCount ?? 0) - (mx.TrueOutCount ?? 0))),
                    StockOrNot = (u.GoodProduct - ((u.ShippedoutNum ?? 0) - ((mx.OutCount ?? 0) - (mx.TrueOutCount ?? 0)))) < 0

                }).ToListAsync().Result;

            if (listStore != null && listStore.Count > 0)
            {
                await _repSale.AsUpdateable(listStore).UpdateColumns(x => new { x.ShippedoutNum, x.Compatible, x.StockOrNot }).ExecuteCommandAsync();
            }
            rp.OutboundStatus = 4;
            await _rep.UpdateAsync(rp);
            await UpdateStatus(rp.SuperiorNum, rp.OutboundStatus);
        }
    }

    /// <summary>
    /// 提交修改状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Submit")]
    public async Task Submit(List<long> listInputs)
    {
        var listRp = await _rep.AsQueryable().Where(u => listInputs.Contains(u.Id)).ToListAsync();
        foreach (var rp in listRp)
        {
            if (rp.OutboundStatus != 0)
                throw Oops.Oh("状态为待提交才能执行提交操作");

            var listWareHouseIds = listRp.Select(x => x.WarehouseId).Distinct();

            var listWareHouse = _wareHouse.AsQueryable().Where(x => listWareHouseIds.Contains(x.Id) && x.IsDelete == false);

            if (listWareHouse.Count() != listWareHouseIds.Count())
                throw Oops.Oh("提交的表单中有不存在或已停用的仓库");

            var listWarehouseOutMX = await _repmx.AsQueryable()
                .InnerJoin<Warehouseout>((u, wo) => u.OutId == wo.Id && wo.IsDelete == false)
                .InnerJoin<Entity.Warehouse>((u, wo, w) => wo.WarehouseId == w.Id)
                .Where(u => u.IsDelete == false && u.OutId == rp.Id && u.GoodProduct)
                .Select((u, wo, w) => new WarehouseoutMXOutput
                {
                    Id = u.Id,
                    OutCount = u.OutCount,
                    Tradename = u.Warehousegoods.Name,
                    TrueOutCount = (int)u.TrueOutCount,
                    GoodsId = u.goodsId,
                    GoodProduct = u.GoodProduct,
                    WarehouseId = u.warehouseout.WarehouseId,
                    WarehouseName = w.Name,
                }).ToListAsync();

            var listWareHouseStore = await _repSale.AsQueryable()
                        .Where(u => u.WarehouseId == rp.WarehouseId && listWarehouseOutMX.Select(x => x.GoodsId).Contains(u.TradeID))
                        .Select(u => new WarehouseStoreOutput
                        {
                            Id = u.Id,
                            WarehouseId = u.WarehouseId,
                            TradeID = u.TradeID,
                            GoodProduct = u.GoodProduct,
                            Compatible = u.Compatible ?? 0,
                            ShippedoutNum = u.ShippedoutNum ?? 0,
                            StockOrNot = u.StockOrNot ?? false
                        }).ToListAsync();

            foreach (var item in listWarehouseOutMX)
            {
                var store = listWareHouseStore.Where(x => x.WarehouseId == item.WarehouseId && x.TradeID == item.GoodsId).FirstOrDefault();
                if (store != null)
                {
                    store.ShippedoutNum += item.OutCount ?? 0;
                    store.Compatible = store.GoodProduct.ToInt(0) - store.ShippedoutNum.ToInt(0);
                    store.StockOrNot = store.Compatible.ToInt(0) < 0;
                }
                else
                {
                    throw new Exception($"仓库【{item.WarehouseName}】没有商品【{item.Tradename}】的库存");
                }
            }

            var list = listWareHouseStore.Adapt<List<WarehouseStore>>();
            await _repSale.AsUpdateable(list).UpdateColumns(x => new { x.ShippedoutNum, x.Compatible, x.StockOrNot }).ExecuteCommandAsync();

            rp.OutboundStatus = 1;
            await _rep.UpdateAsync(rp);
            await UpdateStatus(rp.SuperiorNum, rp.OutboundStatus);
        }
    }

    /// <summary>
    /// 撤回
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Withdraw")]
    public async Task Withdraw(List<long> listInputs)
    {
        var listRp = await _rep.AsQueryable().Where(u => listInputs.Contains(u.Id)).ToListAsync();
        foreach (var rp in listRp)
        {
            if (rp.OutboundStatus != 1)
            {
                throw Oops.Oh("状态为已提交才能执行撤回操作");
            }
            var listWarehouseOutMX = await _repmx.AsQueryable()
                        .InnerJoin<Warehouseout>((u, warehouseOut) => u.OutId == warehouseOut.Id && warehouseOut.IsDelete == false)
                        .Where(u => u.IsDelete == false && u.OutId == rp.Id && u.GoodProduct)
                        .Select(u => new WarehouseoutMXOutput
                        {
                            Id = u.Id,
                            OutCount = u.OutCount,
                            Tradename = u.Warehousegoods.Name,
                            TrueOutCount = (int)u.TrueOutCount,
                            GoodsId = u.goodsId,
                            GoodProduct = u.GoodProduct,
                            WarehouseId = u.warehouseout.WarehouseId
                        }).ToListAsync();

            var listWareHouseStore = await _repSale.AsQueryable()
                        .Where(u => u.WarehouseId == rp.WarehouseId && listWarehouseOutMX.Select(x => x.GoodsId).Contains(u.TradeID))
                        .Select(u => new WarehouseStoreOutput
                        {
                            Id = u.Id,
                            WarehouseId = u.WarehouseId,
                            TradeID = u.TradeID,
                            GoodProduct = u.GoodProduct,
                            Compatible = u.Compatible ?? 0,
                            ShippedoutNum = u.ShippedoutNum ?? 0,
                            StockOrNot = u.StockOrNot ?? false
                        }).ToListAsync();

            foreach (var item in listWarehouseOutMX)
            {
                var store = listWareHouseStore.Where(x => x.WarehouseId == item.WarehouseId && x.TradeID == item.GoodsId).FirstOrDefault();
                if (store != null)
                {
                    store.ShippedoutNum -= item.OutCount ?? 0;
                }

                store.Compatible = store.GoodProduct.ToInt(0) - store.ShippedoutNum.ToInt(0);
                store.StockOrNot = store.Compatible.ToInt(0) < 0;
            }

            var list = listWareHouseStore.Adapt<List<WarehouseStore>>();
            await _repSale.AsUpdateable(list).UpdateColumns(x => new { x.ShippedoutNum, x.Compatible, x.StockOrNot }).ExecuteCommandAsync();
            rp.OutboundStatus = 0;
            await _rep.UpdateAsync(rp);
            await UpdateStatus(rp.SuperiorNum, rp.OutboundStatus);
        }
    }

    /// <summary>
    /// 获取商品出库列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WarehouseoutOutput>> List([FromQuery] WarehouseoutInput input)
    {
        return await _rep.AsQueryable().Select<WarehouseoutOutput>().ToListAsync();
    }

    /// <summary>
    /// 商品出库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Outbound")]
    public async Task Outbound([FromBody] List<WarehouseoutMX> input)
    {
        var outRecord = await _rep.AsQueryable()
                   .Where(u => u.TenantId == _userManager.TenantId)
                   .Where(u => u.Id == input[0].OutId).FirstAsync();

        string orderNum = outRecord.OutOrder + "-1";

        var outBound = _repOutBound.AsQueryable()
                .LeftJoin<WarehouseoutMX>((u, mx) => u.WarehouseOutMxId == mx.Id)
                .LeftJoin<Warehouseout>((u, mx, i) => mx.OutId == i.Id)
                .Where((u, mx, i) => i.Id == outRecord.Id)
                .OrderByDescending((u, mx, i) => u.CreateTime).FirstAsync()?.Result;

        if (outBound != null && !outBound.OrderNum.IsNullOrWhiteSpace())
        {
            if (outBound.OrderNum.Contains('-'))
            {
                int currentNum = 1;
                if (int.TryParse(outBound.OrderNum.Split('-')[1], out currentNum))
                {
                    orderNum = outRecord.OutOrder + "-" + (currentNum + 1).ToString();
                }
            }
        }

        foreach (var item in input)
        {
            var querysale = _repSale.AsQueryable().First(x => x.TradeID == item.goodsId && x.WarehouseId == outRecord.WarehouseId);
            if (querysale != null)
            {
                //var st = await _repgoods.AsQueryable().Where(u => u.Id == item.goodsId).FirstAsync();
                var st = await _repgoods.GetByIdAsync(item.goodsId);

                //不允许负库存
                if (st.vacancy != true)
                {
                    if (querysale.GoodProduct < item.ThisOutCount)
                    {
                        throw new Exception($"商品[{st.Name}]库存不足");
                    }
                }

                //var querymx = await _repmx.AsQueryable().Where(u => u.Id == item.Id).FirstAsync();
                var querymx = await _repmx.GetByIdAsync(item.Id);
                querymx.TrueOutCount = Convert.ToInt32(querymx.TrueOutCount) + Convert.ToInt32(item.ThisOutCount);

                await _repmx.UpdateAsync(querymx);
                if (item.GoodProduct)
                {
                    querysale.GoodProduct = querysale.GoodProduct.ToInt(0) - item.ThisOutCount.ToInt(0);
                    querysale.Quantity = querysale.Quantity.ToInt(0) - item.ThisOutCount.ToInt(0);
                    querysale.ShippedoutNum = querysale.ShippedoutNum.ToInt(0) - item.ThisOutCount.ToInt(0);

                    if (querysale.SafetyStockLowNum.ToInt(0) > 0 || querysale.SafetyStockTallNum.ToInt(0) > 0)
                    {
                        querysale.StockWarning = 0;
                        if (querysale.SafetyStockLowNum.ToInt(0) > 0 && querysale.GoodProduct.ToInt(0) < querysale.SafetyStockLowNum.ToInt(0))
                            //如果库存小于安全库存下限，则库存预警为1，否则为0 库存不足
                            querysale.StockWarning = 1;

                        if (querysale.SafetyStockTallNum.ToInt(0) > 0 && querysale.GoodProduct.ToInt(0) > querysale.SafetyStockTallNum.ToInt(0))
                            //如果库存大于安全库存上限，则库存预警为2，否则为0 库存超额
                            querysale.StockWarning = 2;
                    }
                    else
                    {
                        querysale.StockWarning = -1;
                    }
                }
                else
                {
                    querysale.Reject = querysale.Reject.ToInt(0) - item.ThisOutCount.ToInt(0);
                    querysale.Quantity = querysale.Quantity.ToInt(0) - item.ThisOutCount.ToInt(0);
                }

                if (st?.isbatch == true)
                {
                    //处理批次
                    var batchs = _repbatch.AsQueryable().Where(u => u.InventoryId == querysale.Id)
                        .WhereIF(item.GoodProduct, u => u.GoodProductNum > 0)
                        .WhereIF(!item.GoodProduct, u => u.RejectNum > 0);
                    var ckNum = item.ThisOutCount.ToInt(0);
                    foreach (var batch in batchs.OrderBy(u => u.ExpirationTime).ToList())
                    {
                        var thisBatchOut = ckNum;
                        if (item.GoodProduct)
                        {
                            if (ckNum > batch.GoodProductNum)
                            {
                                thisBatchOut = batch.GoodProductNum.ToInt(0);
                                batch.GoodProductNum = 0;
                            }
                            else
                            {
                                batch.GoodProductNum = batch.GoodProductNum.ToInt(0) - ckNum;
                            }
                        }
                        else
                        {
                            if (ckNum > batch.RejectNum)
                            {
                                thisBatchOut = batch.RejectNum.ToInt(0);
                                batch.RejectNum = 0;
                            }
                            else
                            {
                                batch.RejectNum = batch.RejectNum.ToInt(0) - ckNum;
                            }
                        }

                        ckNum -= thisBatchOut;

                        if (batch.GoodProductNum <= 0)
                        {
                            batch.ShelflifeStatus = -1;
                            await _repWarehouseBatchUpdateQueue.DeleteAsync(x => x.BatchId == batch.Id);
                        }

                        await _repbatch.UpdateAsync(batch);
                        var outboundRecord = new OutboundRecord();
                        outboundRecord.OrderNum = orderNum;
                        outboundRecord.WarehouseOutMxId = querymx.Id;
                        outboundRecord.OutBoundCount = thisBatchOut;
                        outboundRecord.WarehouseBatchId = batch.Id;
                        outboundRecord.GoodProduct = item.GoodProduct;
                        await _repOutBound.InsertAsync(outboundRecord);
                        if (ckNum == 0) break;
                    }

                    //取批次商品的保质期预警的最大值
                    querysale.ExpiredWarning = (batchs.Where(x => x.GoodProductNum > 0).Max(x => x.ShelflifeStatus)) ?? -1;
                }
                else
                {
                    var outboundRecord = new OutboundRecord();
                    outboundRecord.OrderNum = orderNum;
                    outboundRecord.WarehouseOutMxId = querymx.Id;
                    outboundRecord.OutBoundCount = item.ThisOutCount;
                    outboundRecord.WarehouseBatchId = null;
                    outboundRecord.GoodProduct = item.GoodProduct;
                    await _repOutBound.InsertAsync(outboundRecord);
                }
            }

            await _repSale.UpdateAsync(querysale);
        }

        var outMX = await _repmx.GetListAsync(x => x.OutId == input[0].OutId);
        if (outMX.Sum(x => x.TrueOutCount) != outMX.Sum(x => x.OutCount))
        {
            outRecord.OutboundStatus = 2;
        }
        else
        {
            outRecord.OutboundStatus = 3;
        }
        await _rep.UpdateAsync(outRecord);
        await UpdateStatus(outRecord.SuperiorNum, outRecord.OutboundStatus);
    }
    public async Task UpdateStatus(string salesOrder, int plan)
    {
        UpdateSalesperFormanceplanInput input1 = new UpdateSalesperFormanceplanInput();
        Func_UpdateStatus func = new Func_UpdateStatus(_rep2, _rep3);
        input1.SalesOrder = salesOrder;
        if (!string.IsNullOrEmpty(input1.SalesOrder))
        {
            var query = _rep3.AsQueryable().Where(u => u.SalesOrder == input1.SalesOrder).Select<Salescontract>().FirstAsync().Result;
            var entity = await _rep2.AsQueryable().Where(u => u.SalesOrder == query.Id.ToString() && u.Type == Enum.SalesTypeEnmu.SendOut).Select<SalesperFormanceplan>().FirstAsync();
            entity.Plan = plan;
            await _rep2.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
            await func.UpdateStatus(input1);
        }
    }
}

