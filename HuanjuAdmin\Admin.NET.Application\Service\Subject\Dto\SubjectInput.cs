﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 科目设置基础输入参数
    /// </summary>
    public class SubjectBaseInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public virtual long Id { get; set; }
        
        /// <summary>
        /// 科目编码
        /// </summary>
        public virtual string? SubjectCode { get; set; }
        
        /// <summary>
        /// 科目名称
        /// </summary>
        public virtual string? SubjectName { get; set; }

        /// <summary>
        /// 科目类型
        /// </summary>
        public string? SubjectType { get; set; }
        /// <summary>
        /// 余额方向
        /// </summary>
        public string? SubjectDerict { get; set; }
}

    /// <summary>
    /// 科目设置分页查询输入参数
    /// </summary>
    public class SubjectInput : BasePageInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 科目编码
        /// </summary>
        public string? SubjectCode { get; set; }
        
        /// <summary>
        /// 科目名称
        /// </summary>
        public string? SubjectName { get; set; }

        /// <summary>
        /// 科目类型
        /// </summary>
        public string? SubjectType { get; set; }
        /// <summary>
        /// 余额方向
        /// </summary>
        public string? SubjectDerict { get; set; }
}

    /// <summary>
    /// 科目设置增加输入参数
    /// </summary>
    public class AddSubjectInput : SubjectBaseInput
    {
    }

    /// <summary>
    /// 科目设置删除输入参数
    /// </summary>
    public class DeleteSubjectInput : BaseIdInput
    {
    }

    /// <summary>
    /// 科目设置更新输入参数
    /// </summary>
    public class UpdateSubjectInput : SubjectBaseInput
    {
    }

    /// <summary>
    /// 科目设置主键查询输入参数
    /// </summary>
    public class QueryByIdSubjectInput : DeleteSubjectInput
    {

    }
