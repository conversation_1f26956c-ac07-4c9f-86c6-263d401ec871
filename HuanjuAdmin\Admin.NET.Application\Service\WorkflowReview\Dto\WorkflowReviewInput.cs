﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 流程审批基础输入参数
    /// </summary>
    public class WorkflowReviewBaseInput
    {
        /// <summary>
        /// 流程名称
        /// </summary>
        public virtual string ProcessName { get; set; }
        
        /// <summary>
        /// 流程地址
        /// </summary>
        public virtual string ProcessAddress { get; set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public virtual StatusEnum Status { get; set; }
        
    }

    /// <summary>
    /// 流程审批分页查询输入参数
    /// </summary>
    public class WorkflowReviewInput : BasePageInput
    {
        /// <summary>
        /// 流程名称
        /// </summary>
        public string ProcessName { get; set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public StatusEnum Status { get; set; }
        
    }

    /// <summary>
    /// 流程审批增加输入参数
    /// </summary>
    public class AddWorkflowReviewInput : WorkflowReviewBaseInput
    {
    }

    /// <summary>
    /// 流程审批删除输入参数
    /// </summary>
    public class DeleteWorkflowReviewInput : BaseIdInput
    {
    }

    /// <summary>
    /// 流程审批更新输入参数
    /// </summary>
    public class UpdateWorkflowReviewInput : WorkflowReviewBaseInput
    {
        /// <summary>
        /// Id
        /// </summary>
        [Required(ErrorMessage = "Id不能为空")]
        public long Id { get; set; }
        
    }

    /// <summary>
    /// 流程审批主键查询输入参数
    /// </summary>
    public class QueryByIdWorkflowReviewInput : DeleteWorkflowReviewInput
    {

    }
