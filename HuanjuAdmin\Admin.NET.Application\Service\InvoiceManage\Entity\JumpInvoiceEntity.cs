﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.InvoiceManage.Entity;

/// <summary>
/// 商品信息
/// </summary>
public class JumpInvoiceGoods
{
    /// <summary>
    /// 商品名称
    /// </summary>
    [JsonProperty("spmc")]
    public string ProductName { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    [JsonProperty("spbm")]
    public string ProductCode { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [JsonProperty("dw")]
    public string Unit { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    [JsonProperty("ggxh")]
    public string Specification { get; set; }

    /// <summary>
    /// 商品单价
    /// </summary>
    [JsonProperty("spdj")]
    public string UnitPrice { get; set; }

    /// <summary>
    /// 商品数量
    /// </summary>
    [JsonProperty("spsl")]
    public string Quantity { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    [JsonProperty("je")]
    public string Amount { get; set; }

    /// <summary>
    /// 税率
    /// </summary>
    [JsonProperty("sl")]
    public string TaxRate { get; set; }

    /// <summary>
    /// 税额
    /// </summary>
    [JsonProperty("se")]
    public string TaxAmount { get; set; }
}

/// <summary>
/// 开具发票请求
/// </summary>
public class CreateInvoiceRequest
{
    /// <summary>
    /// 单据号
    /// </summary>
    [JsonProperty("djh")]
    public string DocumentNumber { get; set; }

    /// <summary>
    /// 纳税人识别号
    /// </summary>
    [JsonProperty("nsrsbh")]
    public string TaxpayerNumber { get; set; }

    /// <summary>
    /// 发票类型代码
    /// </summary>
    [JsonProperty("fplxdm")]
    public string InvoiceTypeCode { get; set; }

    /// <summary>
    /// 购方税号
    /// </summary>
    [JsonProperty("ghdwdm")]
    public string BuyerTaxNumber { get; set; }

    /// <summary>
    /// 购方名称
    /// </summary>
    [JsonProperty("ghdwmc")]
    public string BuyerName { get; set; }

    /// <summary>
    /// 购方地址电话
    /// </summary>
    [JsonProperty("ghdwdzdh")]
    public string BuyerAddressPhone { get; set; }

    /// <summary>
    /// 购方银行账号
    /// </summary>
    [JsonProperty("ghdwyhzh")]
    public string BuyerBankAccount { get; set; }

    /// <summary>
    /// 商品列表
    /// </summary>
    [JsonProperty("invoiceGoodsList")]
    public List<JumpInvoiceGoods> InvoiceGoodsList { get; set; }
}

/// <summary>
/// API响应基类
/// </summary>
/// <typeparam name="T">响应数据类型</typeparam>
public class JumpApiResponse<T>
{
    /// <summary>
    /// 接口返回code码
    /// </summary>
    [JsonProperty("code")]
    public int Code { get; set; }

    /// <summary>
    /// 接口返回信息
    /// </summary>
    [JsonProperty("message")]
    public string Message { get; set; }

    /// <summary>
    /// 返回数据
    /// </summary>
    [JsonProperty("data")]
    public T Data { get; set; }

    /// <summary>
    /// 总数
    /// </summary>
    [JsonProperty("total")]
    public int Total { get; set; }
}

/// <summary>
/// 开具发票响应数据
/// </summary>
public class CreateInvoiceResponse
{
    /// <summary>
    /// 单据号
    /// </summary>
    [JsonProperty("djh")]
    public string DocumentNumber { get; set; }
}

/// <summary>
/// 发票类型枚举
/// </summary>
public static class InvoiceType
{
    public const string RailwayElectronicTicket = "51";           // 数电发票（铁路电子客票）
    public const string AirTransportTicket = "61";                // 数电发票（航空运输电子客票行程单）
    public const string SpecialVATInvoice = "81";                 // 数电发票（增值税专用发票）
    public const string GeneralInvoice = "82";                    // 数电发票（普通发票）
    public const string SecondHandCarInvoice = "84";              // 数电票（二手车销售统一发票）
    public const string PaperSpecialVATInvoice = "85";           // 数电纸质发票（增值税专用发票）
    public const string PaperGeneralInvoice = "86";              // 数电纸质发票（普通发票）
    public const string PaperVehicleInvoice = "87";              // 数电纸质发票（机动车发票）
    public const string PaperSecondHandCarInvoice = "88";        // 数电纸质发票（二手车发票）
}