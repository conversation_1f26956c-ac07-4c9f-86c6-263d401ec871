﻿<template>
	<div class="warehouseStore-container">
		<el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="queryParams" ref="queryForm" :inline="true">
				<el-form-item label="仓库" prop="warehouseId">
					<el-select clearable filterable v-model="queryParams.warehouse" placeholder="请选择仓库">
						<el-option v-for="(item, index) in warehouseDropdownList" :key="index" :value="item.value" :label="item.label" />
					</el-select>
				</el-form-item>
				<el-form-item label="商品名称">
					<el-input v-model="queryParams.tradeName" clearable="" placeholder="请输入商品名称" />
				</el-form-item>
				<el-form-item label="是否缺货">
					<el-select v-model="queryParams.stockOrNot" placeholder="是否缺货" clearable>
						<el-option label="缺货" :value="true" />
						<el-option label="不缺货" :value="false" />
					</el-select>
				</el-form-item>
				<el-form-item label="保质期">
					<el-select v-model="queryParams.expiryStatus" placeholder="保质期" clearable multiple>
						<el-option label="未设置" :value="-1" />
						<el-option label="正常" :value="0" />
						<el-option label="临期" :value="1" />
						<el-option label="过期" :value="2" />
					</el-select>
				</el-form-item>
				<el-form-item label="库存">
					<el-select v-model="queryParams.stockStatus" placeholder="库存" clearable multiple>
						<el-option label="未设置" :value="-1" />
						<el-option label="正常" :value="0" />
						<el-option label="不足" :value="1" />
						<el-option label="过剩" :value="2" />
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'warehouseStore:page'"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
					<el-button-group>
						<el-button icon="ele-Plus" @click="AddwarehousePurchase()"> 生成采购单 </el-button>
						<el-button icon="ele-Edit" @click="Importstore()"> 导出 </el-button>
						<el-button icon="ele-Coin" @click="recalculateStore()"> 库存重算 </el-button>
					</el-button-group>
				</el-form-item>

				<!--         <el-form-item>
          <el-button type="primary" icon="ele-Plus" @click="openAddWarehouseStore" v-auth="'warehouseStore:add'"> 新增
          </el-button>

        </el-form-item> -->
				<!--     
            <el-button icon="ele-Edit" size="small" text="" type="primary" @click="openEditWarehouseStore(scope.row)" v-auth="'warehouseStore:edit'"> 安全库存设置 </el-button>
            <el-button icon="ele-Delete" size="small" text="" type="primary" @click="delWarehouseStore(scope.row)" v-auth="'warehouseStore:delete'"> 属性转换 </el-button>
 -->
			</el-form>
		</el-card>
		<el-card class="full-table" shadow="hover" style="margin-top: 8px">
			<!--  <el-table :data="tableData" style="width: 100%" v-loading="loading" tooltip-effect="light" row-key="id" border=""> -->

			<el-table
				:data="tableData"
				style="width: 100%; height: 150px"
				v-loading="loading"
				tooltip-effect="light"
				row-key="id"
				border=""
				@row-click="handleRowClick"
				highlight-current-row
				v-model:selection="selectedRows"
				@selection-change="(selection: Array<any>) => selectedRows = selection"
				stripe
			>
				<el-table-column type="selection" width="40" />

				<el-table-column type="index" label="序号" width="55" align="center" fixed="" />
				<!--         <el-table-column prop="number" label="序号" show-overflow-tooltip="" /> -->
				<el-table-column prop="warehouse" label="仓库" show-overflow-tooltip="" fixed="" />
				<el-table-column prop="id" v-if="false" label="仓库id" show-overflow-tooltip="" />

				<el-table-column prop="tradeID" v-if="false" label="商品ID" show-overflow-tooltip="" />
				<el-table-column prop="tradeName" label="商品名称" show-overflow-tooltip="" fixed="" />
				<el-table-column prop="productCode" label="商品编码" show-overflow-tooltip="" fixed="" />
				<el-table-column prop="brand" label="品牌" show-overflow-tooltip="" />
				<el-table-column prop="specifications" label="规格" show-overflow-tooltip="" />

				<el-table-column prop="unitName" label="单位" show-overflow-tooltip="" />
				<el-table-column prop="stockOrNot" label="是否缺货" show-overflow-tooltip="">
					<template #default="scope">
						<el-tag type="danger" v-if="scope.row.compatible < 0"> 缺 </el-tag>
						<el-tag v-else> 否 </el-tag>
					</template>
				</el-table-column>

				<el-table-column prop="expiredWarning" label="保质期" show-overflow-tooltip="">
					<template #default="scope">
						<el-tag type="success" v-if="scope.row.expiredWarning == -1">未设置</el-tag>
						<el-tag type="success" v-if="scope.row.expiredWarning == 0">正常</el-tag>
						<el-tag type="danger" v-else-if="scope.row.expiredWarning == 1">临期</el-tag>
						<el-tag type="danger" v-else-if="scope.row.expiredWarning == 2">过期</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="stockWarning" label="库存" show-overflow-tooltip="">
					<template #default="scope">
						<el-tag type="success" v-if="scope.row.stockWarning == -1">未设置</el-tag>
						<el-tag type="success" v-if="scope.row.stockWarning == 0">正常</el-tag>
						<el-tag type="danger" v-else-if="scope.row.stockWarning == 1">不足</el-tag>
						<el-tag type="danger" v-else-if="scope.row.stockWarning == 2">过剩</el-tag>
					</template>
				</el-table-column>
				<!-- <el-table-column prop="quantity" label="库存数量" show-overflow-tooltip="" /> -->
				<el-table-column prop="goodProduct" label="良品数量" show-overflow-tooltip="" />
				<el-table-column prop="reject" label="次品数量" show-overflow-tooltip="" />
				<el-table-column prop="marketable" label="可销售数量" show-overflow-tooltip="" />
				<el-table-column prop="SalesOccupancy" label="销售占用数" show-overflow-tooltip="" />

				<el-table-column prop="compatible" label="可配数" show-overflow-tooltip="" />
				<el-table-column prop="shippedoutNum" label="待出库数" show-overflow-tooltip="" />
				<el-table-column prop="intransitNum" label="在途数" show-overflow-tooltip="" />
				<el-table-column prop="isUniqueCode" label="是否唯一码" show-overflow-tooltip="">
					<template #default="scope">
						<el-tag v-if="scope.row.isUniqueCode"> 是 </el-tag>
						<el-tag type="danger" v-else> 否 </el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="safetyStockTallNum" label="最高库存数" show-overflow-tooltip="" />
				<el-table-column prop="safetyStockLowNum" label="最低库存数" show-overflow-tooltip="" />
				<el-table-column prop="purchaseUnitPrice" label="当前采购单价" width="98" show-overflow-tooltip="" />
				<el-table-column prop="currentCost" label="当前成本" show-overflow-tooltip="" />
				<el-table-column prop="barCode" label="商品条码" show-overflow-tooltip="" />
				<el-table-column label="是否批次" width="80" align="center" fixed="right" show-overflow-tooltip="">
					<template #default="scope">
						<!-- <el-button icon="ele-add" size="small" text="" type="primary" @click="batch(scope.row)" -->
						<el-button size="small" text="" type="primary" @click="batch(scope.row)" v-auth="'warehouse:edit'" v-if="scope.row.isbatch"> 详情 </el-button>
						<el-tag type="danger" v-else>否</el-tag>
					</template>
				</el-table-column>

				<el-table-column label="操作" width="180" align="center" fixed="right" show-overflow-tooltip="" v-if="auth('warehouse:edit') || auth('warehouse:delete')">
					<template #default="scope">
						<div>
							<el-button icon="ele-Edit" size="small" text="" type="primary" @click="openeditSafetyStockDialog(scope.row)" v-auth="'warehouse:edit'"> 安全库存 </el-button>
							<el-button icon="ele-Edit" size="small" text="" type="primary" @click="openEditWarehouseStore(scope.row)" v-auth="'warehouse:delete'"> 属性转换 </el-button>
						</div>
					</template>
				</el-table-column>
				<el-form-item label="选中的采购单号">
					<el-input v-model="tableDataRow.orderNumber" disabled />
				</el-form-item>
			</el-table>
			<el-pagination
				v-model:currentPage="tableParams.page"
				v-model:page-size="tableParams.pageSize"
				:total="tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				small=""
				background=""
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
			<editDialog ref="editDialogRef" :title="editWarehouseStoreTitle" @reloadTable="handleQuery" />
			<Mydialog ref="eMydialogRef" :title="editWarehouseStoreTitle" @reloadTable="handleQuery" />
			<editSafetyStockDialog ref="eSafetyStockDialogRef" :title="editWarehouseStoreTitle" @reloadTable="handleQuery" />
			<batchDialog ref="batchRef" :title="editWarehouseStoreTitle" @reloadTable="handleQuery" />
		</el-card>
	</div>
</template>

<script lang="ts" setup="" name="WarehouseStoreManagement">
import { ref, watch, onMounted, nextTick, onBeforeUnmount } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { auth } from '/@/utils/authFunction';
//import { formatDate } from '/@/utils/formatTime';
import editSafetyStockDialog from '/@/views/main/warehouseStore/component/editSafetyStockDialog.vue';
import batchDialog from '/@/views/main/warehousebatch/component/editDialog.vue';
import Mydialog from '/@/views/main/warehousePurchase/component/editDialog.vue';
import editDialog from '/@/views/main/warehouseStore/component/editDialog.vue';
import { pageWarehouseStore, deleteWarehouseStore, ImportWarehouseStore, recalculateInventory } from '/@/api/main/warehouseStore';
import { getWarehouseDropdown } from '/@/api/main/warehousePurchase';
// import { getPubSupplierDropdown } from '/@/api/main/warehousePurchaseMX';
import { PubSupplierDropdown } from '/@/api/main/pubSupplier';
import { useRoute } from 'vue-router';
import { debounce } from 'lodash-es';

const route = useRoute();

const eMydialogRef = ref();
const editDialogRef = ref();
const eSafetyStockDialogRef = ref();
const batchRef = ref();
const loading = ref(false);
const tableData = ref<any>([]);
const queryParams = ref<any>({
	warehouse: undefined,
	tradeName: undefined,
	stockOrNot: undefined,
	expiryStatus: [],
	stockStatus: [],
});
const tableParams = ref({
	page: 1,
	pageSize: 20,
	total: 0,
});
const editWarehouseStoreTitle = ref('');
let tableDataRow = ref<any>([]);
const selectedRows = ref<any[]>([]);
const warehouseDropdownList = ref<any>([]);

// 查询操作
const handleQuery = async () => {
	loading.value = true;
	try {
		const params = {
			...queryParams.value,
			...tableParams.value
		};
		const res = await pageWarehouseStore(params);
		tableData.value = res.data.result?.items ?? [];
		tableParams.value.total = res.data.result?.total;
	} finally {
		loading.value = false;
	}
};

// 添加一个处理URL参数的函数
const handleUrlParams = () => {
	const stockStatus = route.query.stockStatus as string;
	if (!stockStatus) {
		handleQuery();
		return;
	}

	// 重置查询条件
	queryParams.value = {
		warehouse: undefined,
		tradeName: undefined,
		stockOrNot: undefined,
		expiryStatus: [],
		stockStatus: [],
	};

	// 根据不同状态设置对应的筛选条件
	switch (stockStatus) {
		case 'stockOut':
			queryParams.value.stockOrNot = true;
			break;
		case 'expiry':
			queryParams.value.expiryStatus = [1, 2];
			break;
		case 'warning':
			queryParams.value.stockStatus = [1, 2];
			break;
	}

	handleQuery();
};

// 添加路由参数监听
const handleUrlParamsDebounced = debounce(() => {
	if (initialized.value) {
		handleUrlParams();
	}
}, 300);

watch(
	() => route.query,
	(newQuery, oldQuery) => {
		if (newQuery.stockStatus !== oldQuery.stockStatus) {
			handleUrlParamsDebounced();
		}
	}
);

//库存重算
const recalculateStore = async () => {
	loading.value = true;
	await recalculateInventory(Object.assign(queryParams.value, tableParams.value));
	loading.value = false;
};

// 重置查询条件
const resetQuery = () => {
	queryParams.value = {
		warehouse: undefined,
		tradeName: undefined,
		stockOrNot: undefined,
		expiryStatus: [],
		stockStatus: [],
	};
	handleQuery();
};
const handleRowClick = async (row: any) => {
	//debugger;
	tableDataRow.value = row;
	await refershDataMX();
};

const getWarehouseDropdownList = async () => {
	let list = await getWarehouseDropdown();

	warehouseDropdownList.value = list.data.result ?? [];
};

let initialized = ref(false);

const isFirstLoad = ref(true);

onMounted(async () => {
	if (isFirstLoad.value) {
		await getWarehouseDropdownList();
		isFirstLoad.value = false;
		handleInitialQuery();
	}
});

const handleInitialQuery = () => {
	if (route.query.stockStatus) {
		handleUrlParams();
	} else {
		handleQuery();
	}
};

watch(
	() => route.query.stockStatus,
	(newStatus, oldStatus) => {
		if (!isFirstLoad.value && newStatus !== oldStatus) {
			handleUrlParamsDebounced();
		}
	}
);

const refershDataMX = async () => {};
const Importstore = async () => {
	//loading.value = true;
	var res = await ImportWarehouseStore(Object.assign(queryParams.value, tableParams.value));
	console.log('导出列表：', res.data);

	/*   let blob = new Blob([res.data.result], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    let downloadElement = document.createElement('a') // 获取table表格
    let href = window.URL.createObjectURL(blob); //创建下载的链接
    downloadElement.href = href;
    downloadElement.download = "库存";//res.data.result.extras; //下载后文件名
    document.body.appendChild(downloadElement);
  
    // 方法一：跳链接下载(推荐这种方法，可以解决下载之后的表格打不开的问题)
    let getUrlData = `C:\Users\<USER>\Downloads` // 拼接的跳转路径
    window.location.href = getUrlData  */

	// 方法二：直接正常下载(下载的文件可能会出现打不开的情况)
	/*    downloadElement.click(); //点击下载
     document.body.removeChild(downloadElement); //下载完成移除元素
     window.URL.revokeObjectURL(href); //释放blob对象 */
	//}

	/*   const xlsx = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    let blob = new Blob([res.data.result], { type: xlsx })
    let downloadElement = document.createElement('a');
    let filename="库存"+new Date+".xlsx";
    downloadElement.download=filename;
    downloadElement.href=window.URL.createObjectURL(blob);
    downloadElement.click();
    downloadElement.remove();
    loading.value = false; */

	/*   let blob = new Blob([res.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=utf-8'
    })
    // 获取文件名，根据自己需要的分割
    let fileName ="库存.xlsx"; //res.headers['content-disposition'].split(';')[1].split("=")[1]
    let a = document.createElement('a')
    let url = window.URL.createObjectURL(blob)
    a.href = url
    a.download = fileName
    document.body.appendChild(a)
    a.style.display = 'none'
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url) */

	let href = window.URL.createObjectURL(new Blob([res.data]));
	const now = new Date();
	var year = now.getFullYear(); // 获取四位年份
	var month = (now.getMonth() + 1).toString().padStart(2, '0'); // 获取两位月份，不足两位前面补0
	var day = now.getDate().toString().padStart(2, '0'); // 获取两位日期，不足两位前面补0

	var fileName = '库存' + year + month + day;
	let link = document.createElement('a');
	link.style.display = 'none';
	link.href = href;
	link.setAttribute('download', fileName + '.xlsx');
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link); // 下载完成移除元素
	window.URL.revokeObjectURL(href); // 释放掉blob对象
};
const pubSupplierDropdownList = ref<any>([]);
const getPubSupplierDropdownList = async () => {
	let list = await PubSupplierDropdown();
	pubSupplierDropdownList.value = list.data.result ?? [];
};
getPubSupplierDropdownList();

const AddwarehousePurchase = () => {
	editWarehouseStoreTitle.value = '添加采购单';
	var listPurchaseIds = ref<string[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		// 直接给 item 添加 id 属性并设置为 null
		item.id = null;
		listPurchaseIds.value.push(item);
	}
	if (listPurchaseIds.value.length == 0) {
		ElMessage.warning('请先选中要提交的记录');
		return;
	}

	eMydialogRef.value.openDialog(listPurchaseIds.value[0], listPurchaseIds.value, '库存');
};

// 打开新增页面
const openAddWarehouseStore = () => {
	editWarehouseStoreTitle.value = '添加库存查询';

	editDialogRef.value.openDialog({});
};

// 打开编辑页面
const openEditWarehouseStore = (row: any) => {
	editWarehouseStoreTitle.value = '属性转换';
	editDialogRef.value.openDialog(row);
};

// 打开编辑页面
const openeditSafetyStockDialog = (row: any) => {
	editWarehouseStoreTitle.value = '安全库存设置';
	eSafetyStockDialogRef.value.openDialog(row);
};

// 打开编辑页面
const batch = (row: any) => {
	editWarehouseStoreTitle.value = '批次详情';
	batchRef.value.openDialog(row);
};

// 删除
const delWarehouseStore = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await deleteWarehouseStore(row);
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	tableParams.value.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	tableParams.value.page = val;
	handleQuery();
};

onBeforeUnmount(() => {
	handleUrlParamsDebounced.cancel();
});
</script>


