﻿using SqlSugar;
using System;

namespace Admin.NET.Application;

/// <summary>
/// 商品采购输出参数
/// </summary>
public class WarehousePurchaseOutput
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 采购单号
    /// </summary>
    public string OrderNumber { get; set; }

    /// <summary>
    /// 供应商ID
    /// </summary>
    public long? SupplierId { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public string SupplierName { get; set; }

    /// <summary>
    /// 仓库编号
    /// </summary>
    public long? WarehouseId { get; set; }

    /// <summary>
    /// 仓库编号
    /// </summary>
    public string WarehouseName { get; set; }

    /// <summary>
    /// 单据状态
    /// </summary>
    public OrderStatusEnum DocumenntStatus { get; set; }

    /// <summary>
    /// 入库状态
    /// </summary>
    public RcvStatusEnum InhouseStatus { get; set; }

    /// <summary>
    /// 商品信息
    /// </summary>
    public string? GoodsInfo { get; set; }

    /// <summary>
    /// 总金额
    /// </summary>
    public decimal? TotalAmt { get; set; }

    /// <summary>
    /// 优惠金额
    /// </summary>
    public decimal? DiscountAmt { get; set; }

    /// <summary>
    /// 实际金额
    /// </summary>
    public decimal? ActualAmt { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 审批单号
    /// </summary>
    public string ApprNo { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 创建者
    /// </summary>
    public string CreateUserName { get; set; }


}


