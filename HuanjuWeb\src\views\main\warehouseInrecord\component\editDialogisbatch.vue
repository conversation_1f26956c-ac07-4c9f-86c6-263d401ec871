﻿<template>
	<div class="warehouseconvertrecord-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="1200" draggable="" :close-on-click-modal="false">
			<el-form>

				<el-button class="addBtn" type="primary" icon="ele-Plus" @click="addTableDataMX">添加批次商品明细</el-button>
			</el-form>
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" :rules="rules">
				<el-table :data="tableDataMx" style="width: 100%;padding: 20px 0;" tooltip-effect="light">
					<el-table-column prop="warehousegoodsName" label="商品名称" show-overflow-tooltip="" width="109" />
					<el-table-column prop="quanProduceTimetity" label="生产日期" show-overflow-tooltip="">
						<template #default="scope">
							<el-date-picker v-model="scope.row.ProduceTime" type="date" placeholder=""
								format="YYYY-MM-DD" value-format="YYYY-MM-DD" class="w120"
								@change="changeProduceTime($event, scope.$index)" />
						</template>
					</el-table-column>
					<el-table-column prop="warrantyTime" label="保质期" show-overflow-tooltip="">
						<template #default="scope">
							<el-input-number data-unit="天" class="my-el-input-number" v-model="scope.row.warrantyTime"
								:min="0" @change="changeExpirationTime($event, scope.$index)"
								@blur="changeExpirationTime($event, scope.$index)" clearable />
						</template>
					</el-table-column>
					<el-table-column prop="expiryReminder" label="过期提醒" show-overflow-tooltip="">
						<template #default="scope">
							<el-input-number data-unit="天" class="my-el-input-number" v-model="scope.row.expiryReminder"
								:min="0" @change="changeExpirationTime($event, scope.$index)"
								@blur="changeExpirationTime($event, scope.$index)" clearable />
						</template>
					</el-table-column>
					<el-table-column prop="expirationTime" label="到期时间" show-overflow-tooltip="">
						<template #default="scope">
							<el-date-picker type="date" placeholder="" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
								class="w120" v-model="scope.row.expirationTime"
								@change="changeWarranty($event, scope.$index)" />
						</template>
					</el-table-column>
					<el-table-column prop="batchnumber" label="批次号" show-overflow-tooltip="">
						<template #default="scope">
							<el-input v-model="scope.row.batchnumber"></el-input>
						</template>
					</el-table-column>
					<el-table-column prop="goodProductNum" label="批次数量" show-overflow-tooltip="">
						<template #default="scope">
							<el-input-number v-model="scope.row.goodProductNum" clearable />
						</template>
					</el-table-column>
					<el-table-column prop="" label="操作" show-overflow-tooltip="" fixed="right" width="150">
						<template #default="scope">
							<el-button size="small" type="primary" icon="ele-Delete"
								@click="deleteRkdDetail(scope.$index)">删除</el-button>
						</template>
					</el-table-column>
					<!-- </el-row> -->
				</el-table>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { ElMessage, ElTable, ElButton } from "element-plus";
import type { FormRules } from "element-plus";
import moment from "moment";

//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: "",
	},
	batchList: {
		type: Array,
	}
});
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable", "isBatchDataListCz"]);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const loading = ref(false);
const ruleForm = ref<any>({});
//自行添加其他规则
const rules = ref<FormRules>({
	ProduceTime: [
		{ required: true, message: '请选择生产日期', trigger: 'change' }
	],
	warrantyTime: [
		{ required: true, message: '请输入保质期', trigger: 'blur' }
	],
	expirationTime: [
		{ required: true, message: '请选择到期时间', trigger: 'change' }
	]
});

const getMx = ref([])
const isBatchData = ref<any>([]);
const tableDataMx = ref<any>([]);
// 打开弹窗
const openDialog = (row: any) => {
	tableDataMx.value = row;
	debugger;
	getMx.value = row;
	isShowDialog.value = true;
	if (!tableDataMx.value[0].batchnumber) {
		tableDataMx.value[0].batchnumber = getCurrentTime()
	}
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};

// 提交
const submit = async () => {
	// debugger;
	let sl = 0;
	tableDataMx.value.forEach((item: any) => {
		sl += Number(item.quantity)
		return
	})
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			loading.value = true;
			if (sl > tableDataMx.value[0].documentNum - tableDataMx.value[0].rcvQty) {
				console.log(sl, tableDataMx.value[0].documentNum, tableDataMx.value[0].rcvQty)
				tableDataMx.value.forEach((item: any) => {
					item.quantity = ''
				})
				ElMessage.warning('填写的入库数量不能超出可入库数量');
				setTimeout(() => {
					loading.value = false;
				}, 500)
			} else {
				emit('isBatchDataListCz', tableDataMx.value);
				isShowDialog.value = false;
				setTimeout(() => {
					loading.value = false;
				}, 500)
			}

		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

// 添加多个批批次号的批次商品
const addTableDataMX = () => {
	isBatchData.value = []
	isBatchData.value.push({
		warehousegoodsName: tableDataMx.value[0].warehousegoodsName,
		quanProduceTimetity: null,
		warranty: tableDataMx.value[0].warranty,
		warrantyTime: tableDataMx.value[0].warrantyTime,
		expiryReminder: tableDataMx.value[0].expiryReminder,
		expirationTime: null,
		batchnumber: getNextBatchNumber(),
		quantity: null
	})
	console.log(isBatchData.value);
	tableDataMx.value = tableDataMx.value.concat(isBatchData.value)
	console.log(tableDataMx.value);
};
// 删除批次商品
const deleteRkdDetail = (index: number) => {
	var deleteRow = tableDataMx.value[index];
	if (deleteRow.id > 0) {
		deleteRow.isDelete = true;
		tableDataMx.value.push(deleteRow);
	}
	tableDataMx.value.splice(index, 1);
};

const getNextBatchNumber = () => {
  const lastBatchNumber = tableDataMx.value[tableDataMx.value.length - 1]?.batchnumber || '';

  // Check if the last batch number contains a hyphen
  const lastHyphenIndex = lastBatchNumber.lastIndexOf('-');

  if (lastHyphenIndex !== -1) {
    // If there's a hyphen, split the string
    const prefix = lastBatchNumber.substring(0, lastHyphenIndex);
    const numberPart = lastBatchNumber.substring(lastHyphenIndex + 1);

    // Check if the part after the hyphen is a valid number
    if (/^\d+$/.test(numberPart)) {
      const newNumber = parseInt(numberPart, 10) + 1;
      return `${prefix}-${newNumber}`;
    }
  }

  // If there's no hyphen or the part after the hyphen is not a valid number,
  // simply append '-1' to the entire last batch number
  return `${lastBatchNumber}-1`;
};

// 获取当前日期时间
const getCurrentTime = () => {
	var getTime = new Date().getTime(); //获取到当前时间戳
	var time = new Date(getTime); //创建一个日期对象
	var year = String(time.getFullYear()).slice(2); // 年
	var month = (time.getMonth() + 1).toString().padStart(2, '0'); // 月
	var date = time.getDate().toString().padStart(2, '0'); // 日
	var hour = time.getHours().toString().padStart(2, '0'); // 时
	var minute = time.getMinutes().toString().padStart(2, '0'); // 分

	return (
		year + month + date + hour + minute
	)
};
// 生产日期改变
const changeProduceTime = (value: moment.MomentInput, index: string | number) => {
	if (!value) return;
	
	// 如果已有保质期,则自动计算到期时间
	if (tableDataMx.value[index].warrantyTime) {
		tableDataMx.value[index].expirationTime = moment(value)
			.add(tableDataMx.value[index].warrantyTime, 'days')
			.format('YYYY-MM-DD');
	}

	// 如果已有到期时间,则校验生产日期不能晚于到期时间
	if (tableDataMx.value[index].expirationTime) {
		if (moment(value).isAfter(moment(tableDataMx.value[index].expirationTime))) {
			ElMessage.error('生产日期不能晚于到期时间');
			tableDataMx.value[index].ProduceTime = '';
			return;
		}
	}
};
// 根据生产日期跟保质期计算到期时间
const changeExpirationTime = (value: any, index: string | number) => {
	if (!value) return;
	
	// 有生产日期时,根据保质期自动计算到期时间
	if (tableDataMx.value[index].ProduceTime) {
		tableDataMx.value[index].expirationTime = moment(tableDataMx.value[index].ProduceTime)
			.add(value, 'days')
			.format('YYYY-MM-DD');
	}
};
// 根据生产日期和到期时间计算保质期
const changeWarranty = (value: moment.MomentInput, index: string | number) => {
	if (!value) return;

	// 有生产日期时,校验到期时间不能早于生产日期
	if (tableDataMx.value[index].ProduceTime) {
		if (moment(value).isBefore(moment(tableDataMx.value[index].ProduceTime))) {
				ElMessage.error('到期时间不能早于生产日期');
				tableDataMx.value[index].expirationTime = '';
				return;
		}
		
		// 自动计算保质期天数
		tableDataMx.value[index].warrantyTime = moment(value)
			.diff(moment(tableDataMx.value[index].ProduceTime), 'days');
	}
};
// 页面加载时
onMounted(async () => {
	console.log('批次号', tableDataMx.value[0])

});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<style lang="scss" scoped>
.my-el-input-number[data-unit] {
	--el-input-number-unit-offset-x: 35px;
	position: relative;
}

.my-el-input-number[data-unit]::after {
	content: attr(data-unit);
	height: 100%;
	display: flex;
	align-items: center;
	position: absolute;
	top: 0;
	right: var(--el-input-number-unit-offset-x);
	color: #999999;
}

.my-el-input-number[data-unit] .el-input__inner {
	padding-left: 30px;
	padding-right: calc(var(--el-input-number-unit-offset-x) + 12px);
}
</style>
