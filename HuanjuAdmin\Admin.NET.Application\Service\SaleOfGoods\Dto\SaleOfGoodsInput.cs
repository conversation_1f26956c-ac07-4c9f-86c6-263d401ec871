﻿using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 商品明细基础输入参数
/// </summary>
public class SaleOfGoodsBaseInput
{
    /// <summary>
    /// 商品名称
    /// </summary>
    public virtual string? TradeName { get; set; }

    /// <summary>
    /// 合同数量
    /// </summary>
    public virtual int? ContractNum { get; set; }

    /// <summary>
    /// 销售单号
    /// </summary>
    public virtual string SalesOrder { get; set; }

}

/// <summary>
/// 商品明细分页查询输入参数
/// </summary>
public class SaleOfGoodsInput : BasePageInput
{
    /// <summary>
    /// 主键
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 商品ID
    /// </summary>
    public long goodsId { get; set; }
    /// <summary>
    /// 商品名称
    /// </summary>
    public string? TradeName { get; set; }
    /// <summary>
    /// 合同数量
    /// </summary>

    public int? puchQty { get; set; }
    /// <summary>
    /// 销售单号
    /// </summary>
    public long SalesOrder { get; set; }

    public double? puchPrice { get; set; }
    public double? puchAmt { get; set; }
    public bool IsDelete { get; set; } = false;

}

/// <summary>
/// 商品明细增加输入参数
/// </summary>
public class AddSaleOfGoodsInput : SaleOfGoodsBaseInput
{
}

/// <summary>
/// 商品明细删除输入参数
/// </summary>
public class DeleteSaleOfGoodsInput : BaseIdInput
{
}

/// <summary>
/// 商品明细更新输入参数
/// </summary>
public class UpdateSaleOfGoodsInput : SaleOfGoodsBaseInput
{
    /// <summary>
    /// 主键
    /// </summary>
    [Required(ErrorMessage = "主键不能为空")]
    public long Id { get; set; }

}

/// <summary>
/// 商品明细主键查询输入参数
/// </summary>
public class QueryByIdSaleOfGoodsInput : DeleteSaleOfGoodsInput
{

}
