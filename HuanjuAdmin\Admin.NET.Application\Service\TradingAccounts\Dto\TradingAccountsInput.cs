﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 交易账户基础输入参数
    /// </summary>
    public class TradingAccountsBaseInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public virtual long Id { get; set; }
        
        /// <summary>
        /// 账户名称
        /// </summary>
        public virtual string Name { get; set; }
        
        /// <summary>
        /// 账户
        /// </summary>
        public virtual string? BankCode { get; set; }
        
        /// <summary>
        /// 银行
        /// </summary>
        public virtual string? BankName { get; set; }
        
        /// <summary>
        /// 开户行
        /// </summary>
        public virtual string? BankAddress { get; set; }
        
        /// <summary>
        /// 账户金额
        /// </summary>
        public virtual decimal? Amount { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public virtual bool Status { get; set; }
        
    }

    /// <summary>
    /// 交易账户分页查询输入参数
    /// </summary>
    public class TradingAccountsInput : BasePageInput
    {
        /// <summary>
        /// 账户名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 账户
        /// </summary>
        public string? BankCode { get; set; }
        
        /// <summary>
        /// 银行
        /// </summary>
        public string? BankName { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public bool Status { get; set; }
        
    }

    /// <summary>
    /// 交易账户增加输入参数
    /// </summary>
    public class AddTradingAccountsInput : TradingAccountsBaseInput
    {
        /// <summary>
        /// 账户名称
        /// </summary>
        [Required(ErrorMessage = "账户名称不能为空")]
        public override string Name { get; set; }
        
        /// <summary>
        /// 账户
        /// </summary>
        [Required(ErrorMessage = "账户不能为空")]
        public override string? BankCode { get; set; }
        
    }

    /// <summary>
    /// 交易账户删除输入参数
    /// </summary>
    public class DeleteTradingAccountsInput : BaseIdInput
    {
    }

    /// <summary>
    /// 交易账户更新输入参数
    /// </summary>
    public class UpdateTradingAccountsInput : TradingAccountsBaseInput
    {
    }

    /// <summary>
    /// 交易账户主键查询输入参数
    /// </summary>
    public class QueryByIdTradingAccountsInput : DeleteTradingAccountsInput
    {

    }
