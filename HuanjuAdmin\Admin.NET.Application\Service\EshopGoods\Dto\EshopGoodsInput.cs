﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 商品基础输入参数
    /// </summary>
    public class EshopGoodsBaseInput
    {
        /// <summary>
        /// 标题
        /// </summary>
        public virtual string? Title { get; set; }
        
        /// <summary>
        /// 详情
        /// </summary>
        public virtual string? Detail { get; set; }
        
        /// <summary>
        /// 排序
        /// </summary>
        public virtual int OrderNo { get; set; }
        
    }

    /// <summary>
    /// 商品分页查询输入参数
    /// </summary>
    public class EshopGoodsInput : BasePageInput
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string? Title { get; set; }
        
    }

    /// <summary>
    /// 商品增加输入参数
    /// </summary>
    public class AddEshopGoodsInput : EshopGoodsBaseInput
    {
    }

    /// <summary>
    /// 商品删除输入参数
    /// </summary>
    public class DeleteEshopGoodsInput : BaseIdInput
    {
    }

    /// <summary>
    /// 商品更新输入参数
    /// </summary>
    public class UpdateEshopGoodsInput : EshopGoodsBaseInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        [Required(ErrorMessage = "主键Id不能为空")]
        public long Id { get; set; }
        
    }

    /// <summary>
    /// 商品主键查询输入参数
    /// </summary>
    public class QueryByIdEshopGoodsInput : DeleteEshopGoodsInput
    {

    }
