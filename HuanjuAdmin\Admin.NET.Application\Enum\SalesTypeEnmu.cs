﻿// MIT License
//
// Copyright (c) 2021-present <PERSON><PERSON><PERSON><PERSON><PERSON>, Daming Co.,Ltd and Contributors
//
// 电话/微信：18020030720 QQ群1：87333204 QQ群2：252381476

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Enum;
[Description("销售类型枚举")]
public enum SalesTypeEnmu
{
    /// <summary>
    /// 发货
    /// </summary>
    [Description("发货")]
    SendOut = 0,

    /// <summary>  
    /// 收款
    /// </summary>
    [Description("收款")]
    Collection = 1,

    /// <summary>
    /// 开票
    /// </summary>
    [Description("开票")]
    Invoicing = 2,
}
