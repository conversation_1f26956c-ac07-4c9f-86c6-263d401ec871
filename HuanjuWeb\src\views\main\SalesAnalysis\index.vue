﻿<template>
  <div class="salesperFormanceplan-container">
    <el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="时间">
          <el-date-picker placeholder="请选择时间" value-format="YYYY/MM/DD" type="daterange" v-model="queryParams.planTimeRange" />
          
        </el-form-item>
        <el-form-item label="类型">
						<el-select clearable filterable v-model="queryParams.Type" placeholder="请选择类型"
							:disabled="queryParams.id > 0">
							<el-option v-for="(item, index) in type" :key="index" :value="item.id"
								:label="item.name" />
						</el-select>
					</el-form-item>

        <el-form-item>
          <el-button-group>
            <el-button type="primary"  icon="ele-Search" @click="handleQuery" v-auth="'salesperFormanceplan:page'"> 查询 </el-button>
            <el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
            
          </el-button-group>
          
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="ele-Plus" @click="openAddSalesperFormanceplan" v-auth="'salesperFormanceplan:add'"> 新增 </el-button>
          
        </el-form-item>
         
      </el-form>
    </el-card>
    <el-card class="full-table" shadow="hover" style="margin-top: 8px">
      <el-table
				:data="tableData"
				style="width: 100%"
				v-loading="loading"
				tooltip-effect="light"
				row-key="id"
				border="">
        <el-table-column type="index" label="序号" width="55" align="center" fixed=""/>
         <el-table-column prop="name" label="名称" fixed="" show-overflow-tooltip="" />

         <el-table-column prop="turnover" label="交易额" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="transaction" label="交易占比" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="amountofprofit" label="利润额" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="profitproportion" label="利润占比" fixed="" show-overflow-tooltip="" />

      </el-table>
      <el-pagination
				v-model:currentPage="tableParams.page"
				v-model:page-size="tableParams.pageSize"
				:total="tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				small=""
				background=""
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
	/>
      <editDialog
			    ref="editDialogRef"
			    :title="editSalesperFormanceplanTitle"
			    @reloadTable="handleQuery"
      />
    </el-card>
  </div>
</template>

<script lang="ts" setup="" name="salesperFormanceplan">
  import { ref } from "vue";
  import { ElMessageBox, ElMessage } from "element-plus";
  import { auth } from '/@/utils/authFunction';

  import editDialog from '/@/views/main/salesperFormanceplan/component/editDialog.vue'
  import { SalesAnalysis } from '/@/api/main/SalesAnalysis';


  const type = ref([
  {
	id:0,
    name: '商品'
  },
  {
	id:1,
    name: '客户'
  },
  {
	id:2,
    name: '销售'
  }
]);
    const editDialogRef = ref();
    const loading = ref(false);
    const tableData = ref<any>
      ([]);
      const queryParams = ref<any>
        ({});
        const tableParams = ref({
        page: 1,
        pageSize: 10,
        total: 0,
        });
        const editSalesperFormanceplanTitle = ref("");


        // 查询操作
        const handleQuery = async () => {
        loading.value = true;
        var res = await SalesAnalysis(Object.assign(queryParams.value, tableParams.value));
        tableData.value = res.data.result?.items ?? [];
        tableParams.value.total = res.data.result?.total;
        loading.value = false;
        };
        // 重置查询条件
        const resetQuery = () => {
          queryParams.value = {};
          handleQuery();
        };
        // 打开新增页面
        const openAddSalesperFormanceplan = () => {
        editSalesperFormanceplanTitle.value = '添加履约计划';
        editDialogRef.value.openDialog({});
        };

        // 打开编辑页面
        const openEditSalesperFormanceplan = (row: any) => {
        editSalesperFormanceplanTitle.value = '编辑履约计划';
        editDialogRef.value.openDialog(row);
        };

        // 删除
        const delSalesperFormanceplan = (row: any) => {
        ElMessageBox.confirm(`确定要删除吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        })
        .then(async () => {
        await deleteSalesperFormanceplan(row);
        handleQuery();
        ElMessage.success("删除成功");
        })
        .catch(() => {});
        };

        // 改变页面容量
        const handleSizeChange = (val: number) => {
        tableParams.value.pageSize = val;
        handleQuery();
        };

        // 改变页码序号
        const handleCurrentChange = (val: number) => {
        tableParams.value.page = val;
        handleQuery();
        };


handleQuery();
</script>


