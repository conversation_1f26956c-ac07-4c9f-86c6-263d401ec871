﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using SqlSugar;
using System;

namespace Admin.NET.Application;
/// <summary>
/// 商品采购明细服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WarehousePurchaseMXService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarClient _db;
    private readonly SqlSugarRepository<WarehousePurchaseMX> _rep;
    private readonly SqlSugarRepository<WarehousePurchase> _repPurchase;
    public WarehousePurchaseMXService(
        SqlSugarRepository<WarehousePurchaseMX> rep,
        SqlSugarRepository<WarehousePurchase> repPurchase, ISqlSugarClient db )
    {
        _db = db;
        _rep = rep;
        _repPurchase = repPurchase;
    }

    /// <summary>
    /// 分页查询商品采购明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WarehousePurchaseMXOutput>> Page(WarehousePurchaseMXInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(x => x.IsDelete == false)
                    .WhereIF(input.GoodsId > 0, u => u.GoodsId == input.GoodsId)
                    .WhereIF(input.SupplierId > 0, u => u.SupplierId == input.SupplierId)

                    .Select(u => new WarehousePurchaseMXOutput
                    {
                        Id = u.Id,
                        GoodsId = u.GoodsId,
                        WarehousegoodsName = u.Warehousegoods.Name,
                        Unit = u.Unit,
                        PuchQty = u.PuchQty,
                        PuchPrice = u.PuchPrice,
                        PuchAmt = u.PuchAmt,
                        RcvQty = u.RcvQty,
                        SupplierId = u.SupplierId,
                        PubSupplierName = u.PubSupplier.Name,
                        IsDelete = u.IsDelete,
                        ProductCode = u.Warehousegoods.Code,
                        BarcodeName = u.Warehousegoods.barcode,
                        SpecsName = u.Warehousegoods.Specs,
                        UnitName = u.warehousegoodsunit.Name
                    });

        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    public async Task<int> AddOrUpdate(List<WarehousePurchaseMX> listMx)
    {
        return await _rep.AsSugarClient().Storageable(listMx).ExecuteCommandAsync();
    }

    /// <summary>
    /// 增加商品采购明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddWarehousePurchaseMXInput input)
    {
        var entity = input.Adapt<WarehousePurchaseMX>();

        //更新采购单的总金额
        var warehousePurchase = await _repPurchase.GetFirstAsync(u => u.Id == input.PurchaseId && u.IsDelete == false);
        warehousePurchase.TotalAmt += entity.PuchAmt;
        await _repPurchase.AsUpdateable(warehousePurchase).UpdateColumns(u => new { u.TotalAmt }).ExecuteCommandAsync();

        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除商品采购明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteWarehousePurchaseMXInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);

        //更新采购单的总金额
        var warehousePurchase = await _repPurchase.GetFirstAsync(u => u.Id == entity.PurchaseId && u.IsDelete == false);
        warehousePurchase.TotalAmt -= entity.PuchAmt;
        await _repPurchase.AsUpdateable(warehousePurchase).UpdateColumns(u => new { u.TotalAmt }).ExecuteCommandAsync();

        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新商品采购明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWarehousePurchaseMXInput input)
    {
        var oringEntity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        var entity = input.Adapt<WarehousePurchaseMX>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();

        if (oringEntity.PuchAmt != entity.PuchAmt)
        {
            //同步更新库存，库存总量+=采购数量 库存数量+=入库数量
            var warehousePurchase = await _repPurchase.GetFirstAsync(u => u.Id == oringEntity.PurchaseId && u.IsDelete == false);
            warehousePurchase.TotalAmt += entity.PuchAmt - oringEntity.PuchAmt;
            await _repPurchase.AsUpdateable(warehousePurchase).UpdateColumns(u => new { u.TotalAmt }).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 获取商品采购明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<WarehousePurchaseMX> Get([FromQuery] QueryByIdWarehousePurchaseMXInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取商品采购明细列表
    /// </summary>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WarehousePurchaseMXOutput>> List(long purchaseId)
    {
        return await _rep.AsQueryable().Where(u => u.PurchaseId == purchaseId && u.IsDelete == false)
            .Select(u => new WarehousePurchaseMXOutput
            {
                Id = u.Id,
                GoodsId = u.GoodsId,
                PurchaseId = u.PurchaseId,
                WarehousegoodsName = u.Warehousegoods.Name,
                Unit = u.Unit,
                PuchQty = u.PuchQty,
                PuchPrice = u.PuchPrice,
                PuchAmt = u.PuchAmt,
                RcvQty = u.RcvQty,
                SupplierId = u.SupplierId,
                PubSupplierName = u.PubSupplier.Name,
                ProductCode = u.Warehousegoods.Code,
                BarcodeName = u.Warehousegoods.barcode,
                SpecsName = u.Warehousegoods.Specs,
                UnitName = u.warehousegoodsunit.Name,
                BrandName= u.Warehousegoods.Brand
            }).ToListAsync();
    }

    /// <summary>
    /// 获取商品ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "WarehousegoodsDropdown"), HttpGet]
    public async Task<dynamic> WarehousegoodsDropdown()
    {
        return await _rep.Context.Queryable<Warehousegoods>()
                .Where(x => x.IsDelete == false)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id
                }
                ).ToListAsync();
    }
    /// <summary>
    /// 获取供应商ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "PubSupplierDropdown"), HttpGet]
    public async Task<dynamic> PubSupplierDropdown()
    {
        return await _rep.Context.Queryable<PubSupplier>()
                .Where(x => x.IsDelete == false)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id
                }
                ).ToListAsync();
    }




}

