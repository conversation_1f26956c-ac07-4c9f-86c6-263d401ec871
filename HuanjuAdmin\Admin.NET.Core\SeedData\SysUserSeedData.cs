namespace Admin.NET.Core;

/// <summary>
/// 系统用户表种子数据
/// </summary>
public class SysUserSeedData : ISqlSugarEntitySeedData<SysUser>
{
    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    [IgnoreUpdate]
    public IEnumerable<SysUser> HasData()
    {
        var encryptPasswod = CryptogramUtil.Encrypt("123456");

        return new[]
        {
            new SysUser{ Id=*************, Account="superadmin", Password=encryptPasswod, NickName="超级管理员", RealName="超级管理员", Phone="***********", Birthday=DateTime.Parse("1986-06-28"), Sex=GenderEnum.Male, AccountType=AccountTypeEnum.SuperAdmin, Remark="超级管理员", CreateTime=DateTime.Parse("2022-02-10 00:00:00"), TenantId=************* },
            new SysUser{ Id=*************, Account="admin", Password=encryptPasswod, NickName="系统管理员", RealName="系统管理员", Phone="***********", Birthday=DateTime.Parse("1986-06-28"), Sex=GenderEnum.Male, AccountType=AccountTypeEnum.Admin, Remark="系统管理员", CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrgId=*************, PosId=*************, TenantId=************* },
            new SysUser{ Id=*************, Account="user1", Password=encryptPasswod, NickName="部门主管", RealName="部门主管", Phone="***********", Birthday=DateTime.Parse("1986-06-28"), Sex=GenderEnum.Female, AccountType=AccountTypeEnum.User, Remark="部门主管", CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrgId=*************, PosId=*************, TenantId=************* },
            new SysUser{ Id=*************, Account="user2", Password=encryptPasswod, NickName="部门职员", RealName="部门职员", Phone="***********", Birthday=DateTime.Parse("1986-06-28"), Sex=GenderEnum.Female, AccountType=AccountTypeEnum.User, Remark="部门职员", CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrgId=*************, PosId=*************, TenantId=************* },
            new SysUser{ Id=*************, Account="user3", Password=encryptPasswod, NickName="普通用户", RealName="普通用户", Phone="***********", Birthday=DateTime.Parse("1986-06-28"), Sex=GenderEnum.Female, AccountType=AccountTypeEnum.User, Remark="普通用户", CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrgId=*************, PosId=*************, TenantId=************* },
            new SysUser{ Id=*************, Account="user4", Password=encryptPasswod, NickName="其他", RealName="其他", Phone="***********", Birthday=DateTime.Parse("1986-06-28"), Sex=GenderEnum.Female, AccountType=AccountTypeEnum.None, Remark="普通用户", CreateTime=DateTime.Parse("2022-02-10 00:00:00"), OrgId=*************, PosId=*************, TenantId=************* },
        };
    }
}