﻿using SqlSugar;

namespace Admin.NET.Application;

    /// <summary>
    /// 科目设置输出参数
    /// </summary>
    public class SubjectDto
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 科目编码
        /// </summary>
        public string? SubjectCode { get; set; }
        
        /// <summary>
        /// 科目名称
        /// </summary>
        public string? SubjectName { get; set; }
        /// <summary>
        /// 科目类型
        /// </summary>
        public string? SubjectType { get; set; }
        /// <summary>
        /// 余额方向
        /// </summary>
        public string? SubjectDerict { get; set; }

}
