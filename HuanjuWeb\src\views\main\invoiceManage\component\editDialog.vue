﻿<template>
	<div class="invoiceManage-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="1400" draggable=""  :close-on-click-modal="false">
			<!-- <el-card style="70px">
				<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" :rules="rules">
					<el-row :gutter="35">
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="时间" prop="time">
								<el-date-picker v-model="ruleForm.time" type="date" placeholder="时间" />

							</el-form-item>

						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="发票性质" prop="natureInvoice">
								<el-input-number v-model="ruleForm.natureInvoice" placeholder="请输入发票性质" clearable />

							</el-form-item>

						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="发票时间" prop="invoiceDate">
								<el-date-picker v-model="ruleForm.invoiceDate" type="date" placeholder="发票时间" />

							</el-form-item>

						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="来往单位" prop="comeUnit">
								<el-input v-model="ruleForm.comeUnit" placeholder="请输入来往单位" clearable />

							</el-form-item>

						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="发票号码" prop="invoiceNum">
								<el-input v-model="ruleForm.invoiceNum" placeholder="请输入发票号码" clearable />

							</el-form-item>

						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="未税金额" prop="untaxedMoney">
								<el-input v-model="ruleForm.untaxedMoney" placeholder="请输入未税金额" clearable />

							</el-form-item>

						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="税额" prop="taxAmount">
								<el-input v-model="ruleForm.taxAmount" placeholder="请输入税额" clearable />

							</el-form-item>

						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="发票金额" prop="invoiceValue">
								<el-input v-model="ruleForm.invoiceValue" placeholder="请输入发票金额" clearable />

							</el-form-item>

						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="税率" prop="taxRate">
								<el-input v-model="ruleForm.taxRate" placeholder="请输入税率" clearable />

							</el-form-item>

						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="收款单号" prop="receiptNum">
								<el-input v-model="ruleForm.receiptNum" placeholder="请输入收款单号" clearable />

							</el-form-item>

						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="发票状态" prop="invoiceStatus">
								<el-input-number v-model="ruleForm.invoiceStatus" placeholder="请输入发票状态" clearable />

							</el-form-item>

						</el-col>
					</el-row>
				</el-form>
			</el-card> -->
			<el-card>
				<el-form :model="ruleForm" ref="ruleFormRef" size="default"  :rules="rules">
					<el-row :gutter="35" v-if="props.title == '添加发票管理'">
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item>
								<el-button type="primary" icon="ele-Plus" @click="addInvoiceDetail">
									新增发票明细 </el-button>
							</el-form-item>
						</el-col>
					</el-row>
					<el-table :data="tableData" style="width: 100%" tooltip-effect="light">
						<el-table-column type="index" label="序号" width="55" align="center" /> 
						<el-table-column prop="natureInvoice" label="发票性质" show-overflow-tooltip="">
							<template #default="scope">
								<el-select clearable filterable  placeholder="发票性质" v-model="scope.row.natureInvoice">
                                 <el-option v-for=" (item, index) in natureInvoiceList" :key="index"
										:value="item.value" :label="item.label" />
                                </el-select> 
							</template>
						</el-table-column>
						<el-table-column prop="invoiceDate" label="开票时间" show-overflow-tooltip="" width="150" >
							<template #default="scope">
								<el-date-picker v-model="scope.row.invoiceDate" type="date" placeholder="开票时间" style="width:100%;">
                                </el-date-picker>
							</template>
						</el-table-column>
						<el-table-column prop="comeUnit" label="来往单位" show-overflow-tooltip="" width="150">
                            <template #default="scope">
                                <el-input  v-model="scope.row.comeUnit"  placeholder="来往单位"/>
							</template>
						</el-table-column>
						<el-table-column prop="invoiceNum" label="发票号码" show-overflow-tooltip="">
                            <template #default="scope">
                                <el-input v-model="scope.row.invoiceNum" placeholder="发票号码"/>
							</template>
						</el-table-column>
						<el-table-column prop="untaxedMoney" label="未税金额" show-overflow-tooltip="">
                            <template #default="scope">
                                <el-input v-model="scope.row.untaxedMoney" placeholder="未税金额"/>
							</template>
						</el-table-column>
						<el-table-column prop="taxRate" label="税率" show-overflow-tooltip="">
                            <template #default="scope">
                                <el-input @blur="taxRateBlur(scope.$index)" @focus="taxRateFocus(scope.$index)" v-model="scope.row.taxRate" placeholder="税率"/>
							</template>
						</el-table-column>
						<el-table-column prop="taxAmount" label="税额" show-overflow-tooltip="">
                            <template #default="scope">
                                <el-input v-model="scope.row.taxAmount" placeholder="税额"/>
							</template>
						</el-table-column>
						<el-table-column prop="invoiceValue" label="价税合计" show-overflow-tooltip="">
                            <template #default="scope">
                                <el-input v-model="scope.row.invoiceValue" placeholder="价税合计"/>
							</template>
						</el-table-column>
						<el-table-column prop="invoiceType" label="发票种类" show-overflow-tooltip="" width="120">
							<template #default="scope">
							  <el-select clearable filterable v-model="scope.row.invoiceType" placeholder="发票种类">
								<el-option v-for=" (item, index) in invoiceType" :key="index"
										:value="item.value" :label="item.label" />
							  </el-select>
							</template>
						</el-table-column>
						<el-table-column prop="invoiceTypeLx" label="发票类型" show-overflow-tooltip=""  width="120">
							<template #default="scope">
							  <el-select clearable filterable v-model="scope.row.invoiceTypeLx" placeholder="发票类型">
								<el-option v-for=" (item, index) in invoiceTypeLx" :key="index"
										:value="item.value" :label="item.label" />
							  </el-select>
							</template>
						</el-table-column>
						<el-table-column prop="download" label="下载链接" show-overflow-tooltip=""  width="150">
                            <template #default="scope">
                                <el-input v-model="scope.row.download" placeholder="下载链接"/>
							</template>
						</el-table-column>
						<el-table-column prop="receiptNum" label="收付款单号" show-overflow-tooltip="" width="150">
                            <template #default="scope">
                                <el-input v-model="scope.row.receiptNum" placeholder="收付款单号"/>
							</template>
						</el-table-column>
						<el-table-column prop="notes" label="备注" show-overflow-tooltip="">
                            <template #default="scope">
                                <el-input v-model="scope.row.notes" placeholder="备注"/>
							</template>
						</el-table-column>
						<!-- <el-table-column label="操作" width="70" align="center" fixed="right" show-overflow-tooltip="">
							<template #default="scope">
								<el-button icon="ele-Delete" size="small" text="" type="primary"
									@click="deletePurchaseDetail(scope.$index)" v-auth="'warehousePurchaseMX:delete'"> 删除
								</el-button>
							</template>
						</el-table-column> -->
					</el-table>
				</el-form>
			</el-card>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { useGoodsStore } from '/@/stores/goods'
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { addInvoiceManage, updateInvoiceManage } from "/@/api/main/invoiceManage";
import { WarehouseGoodsUnit } from '/@/api/main/warehouseInrecord';
import moment from 'moment';
let tableData = ref<any>([]);
const goodsStore = useGoodsStore();
let tableMxDeleted = ref<any>([]);
const loading = ref(false);
const addInvoiceDetail = () => {

	tableData.value.push({
		natureInvoice: '',
		invoiceDate: '',
		comeUnit: '',
		invoiceNum: '',
		untaxedMoney: '',
		taxRate: '',
		invoiceValue: '',
		invoiceType: '',
		invoiceTypeLx: '',
		receiptNum: '',
		notes: '',
		id: null,
		download: ''
		// add other fields as necessary
	});
};
const natureInvoiceList = [
{
	label: '销项发票',
	value: 0
   },
   {
	label: '进项发票',
	value: 1
   }
]; //发票性质
const invoiceType = [
	{
      label:'蓝票',
	  value: 0
	},
	{
	  label:'红票',
	  value: 1
	}
]; //发票种类
const invoiceTypeLx = [
	{
      label:'普票',
	  value: 0
	},
	{
	  label:'专票',
	  value: 1
	}
]; //发票类型

const getGoodsDetail = (row: any, val: any) => {
	debugger;
	let obj = goodsStore.goodsList.find((v: { value: any; }) => {
		return v.value == val
	})
	row.tradename = obj?.label || ""
	row.brandName = obj?.brand
	row.barcode = obj?.id
	row.productCode = obj?.code
	row.specsName = obj?.specs
	row.unit = obj?.unit || ""
	row.auxiliaryunit = obj?.auxiliaryunit || ""
};

const WarehouseUnit = ref<any>([]);
const WareUnit = async () => {
	debugger;
	var res = await WarehouseGoodsUnit();
	WarehouseUnit.value = res.data.result ?? [];
}

WareUnit();
const deletePurchaseDetail = (index: number) => {
	var deleteRow = tableData.value[index];
	// console.log(deleteRow);
	if (deleteRow.id > 0) {
		deleteRow.isDelete = true;
		tableMxDeleted.value.push(deleteRow);
	}
	// alert(tableDataMX.value.length);
	tableData.value.splice(index, 1);
	// alert(tableDataMX.value.length);
	recalculateTotalAmt();
};

const recalculateTotalAmt = () => {
	ruleForm.value.totalAmt = tableData.value.reduce((total: number, row: any) => total + (row.puchAmt || 0), 0);
};

const recalculateAmount = (row: any) => {
	debugger;
	row.puchPrice = row.puchAmt / row.puchQty
	recalculateTotalAmt();
}

//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: "",
	},
});
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const ruleForm = ref<any>({});
//自行添加其他规则
const rules = ref<FormRules>({
});

// 打开弹窗
const openDialog = (row:any) => {
	console.log(row)
	if (JSON.stringify(row) !== '{}') {
		tableData.value[0] = row;
	} else {
		tableData.value = []
	}
	isShowDialog.value = true;
};

// 关闭弹窗
const closeDialog = () => {
	tableData.value = [];
	emit("reloadTable");
	isShowDialog.value = false;
	setTimeout(() => {
		loading.value = false;
	},500)
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			loading.value = true;
			let values = {
				addInvoiceManageInput: tableData.value ,
				listMx: [] // 添加tableDataMX到发送的数据
			};
			tableData.value.forEach((element: {
				invoiceDate: string | Date;
				taxRate: string;
			}) => {
				element.invoiceDate = moment(element.invoiceDate).format('YYYY-MM-DD')
				element.taxRate = element.taxRate.replace('%','')
			});
			if (tableMxDeleted.value.length > 0) { // 如果tableMxDeleted不为空，那么添加到发送的数据中
				values.listMx = values.listMx.concat(tableMxDeleted.value);
			}
			if (props.title == '添加发票管理') {
				await addInvoiceManage(values);
			} else {
				await updateInvoiceManage(tableData.value[0]);
			}
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

// 税率获得焦点
const taxRateFocus = (index: any) => {
	console.log(index,'taxRateBlurtaxRateBlurtaxRateBlur',tableData.value[index].taxRate)
	tableData.value[index].taxRate = tableData.value[index].taxRate.replace('%','')
}
// 税率失去焦点
const taxRateBlur = (index: any) => {
	console.log(index,'taxRateBlurtaxRateBlurtaxRateBlur',tableData.value[index].taxRate)
	tableData.value[index].taxRate = tableData.value[index].taxRate + '%'
}





// 页面加载时
onMounted(async () => {
});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>




