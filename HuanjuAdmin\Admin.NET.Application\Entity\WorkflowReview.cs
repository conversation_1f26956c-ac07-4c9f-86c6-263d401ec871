﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 流程审批表
    /// </summary>
    [SugarTable("workflowreview","流程审批表")]
    [Tenant("1300000000001")]
    public class WorkflowReview  : EntityTenant
    {
        /// <summary>
        /// 流程名称
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "流程名称", Length = 20)]
        public string ProcessName { get; set; }
        /// <summary>
        /// 流程地址
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "流程地址", Length = 50)]
        public string ProcessAddress { get; set; }
        /// <summary>
        /// 是否启用
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "是否启用")]
        public int Status { get; set; }
    }
}