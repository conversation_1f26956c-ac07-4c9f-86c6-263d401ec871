using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Core;
using Furion.DependencyInjection;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using System;
using System.Threading.Tasks;

namespace Admin.NET.Application;

/// <summary>
/// 批次更新队列服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WarehouseBatchQueueService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<WarehouseBatchUpdateQueue> _queueRepo;
    private readonly SqlSugarRepository<warehousebatch> _batchRepo;

    public WarehouseBatchQueueService(
        SqlSugarRepository<WarehouseBatchUpdateQueue> queueRepo,
        SqlSugarRepository<warehousebatch> batchRepo)
    {
        _queueRepo = queueRepo;
        _batchRepo = batchRepo;
    }


    /// <summary>
    /// 批量添加到更新队列
    /// </summary>
    [HttpPost("addBatch")]
    public async Task AddBatchToQueue(long batchId, DateTime? targetTime, DateTime? expirationTime)
    {
        var queueItems = new List<WarehouseBatchUpdateQueue> { };
        if (targetTime.HasValue)
            queueItems.Add(new WarehouseBatchUpdateQueue
            {
                BatchId = batchId,
                TargetUpdateTime = targetTime.Value.Date,
                Type = 0,
                Status = 0
            });
        if (expirationTime.HasValue)
            queueItems.Add(new WarehouseBatchUpdateQueue
            {
                BatchId = batchId,
                TargetUpdateTime = expirationTime.Value.Date,
                Type = 1,
                Status = 0
            });

        await _queueRepo.InsertRangeAsync(queueItems);
    }

    /// <summary>
    /// 获取待处理的队列项
    /// </summary>
    /// <param name="pageSize">每页数量</param>
    [HttpGet("pending")]
    public async Task<List<WarehouseBatchUpdateQueue>> GetPendingItems(int pageSize = 1000)
    {
        var nowDate = DateTime.Now.Date;
        var query = _queueRepo.AsQueryable()
            .Where(x => x.Status == 0 && x.TargetUpdateTime <= nowDate);

        return await query.Take(pageSize).ToListAsync();
    }

    /// <summary>
    /// 更新队列项状态
    /// </summary>
    [HttpPost("updateStatus")]
    public async Task UpdateQueueStatus(List<long> listIds)
    {
        await _queueRepo.UpdateAsync(
            it => new WarehouseBatchUpdateQueue
            {
                Status = 1,
            },
            it => listIds.Contains(it.Id)
        );
    }

    /// <summary>
    /// 清理已处理的队列项
    /// </summary>
    /// <param name="days">保留天数</param>
    [HttpPost("cleanup")]
    public async Task CleanupProcessedItems(int days = 30)
    {
        var cutoffDate = DateTime.Now.AddDays(-days);
        await _queueRepo.DeleteAsync(x =>
            x.Status == 1 &&
            x.TargetUpdateTime <= cutoffDate
        );
    }
}