<template>
	<div class="sys-user-container">
		<el-row :gutter="8" style="width: 100%">
			<el-col :span="4" :xs="24">
				<OrgTree ref="orgTreeRef" @node-click="nodeClick" />
			</el-col>
			<el-col :span="20" :xs="24">
				<el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
					<el-form :model="state.queryParams" ref="queryForm" :inline="true">
						<el-form-item label="姓名" prop="account">
							<el-input placeholder="姓名" clearable @keyup.enter="handleQuery" v-model="state.queryParams.account" />
						</el-form-item>
						<!-- <el-form-item label="姓名" prop="realName">
							<el-input placeholder="姓名" clearable @keyup.enter="handleQuery" v-model="state.queryParams.realName" />
						</el-form-item> -->
						<el-form-item label="手机号码" prop="phone">
							<el-input placeholder="手机号码" clearable @keyup.enter="handleQuery" v-model="state.queryParams.phone" />
						</el-form-item>
						<el-form-item>
							<el-button-group>
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'sysEmployee:page'"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
							</el-button-group>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" icon="ele-Plus" @click="openAddUser" v-auth="'sysEmployee:add'"> 新增 </el-button>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" icon="ele-Plus" @click="openAccount" :disabled="state.btnStatus"> 开通账号 </el-button>
						</el-form-item>
					</el-form>
				</el-card>

				<el-card class="full-table" shadow="hover" style="margin-top: 8px">
					<el-table :data="state.userData" style="width: 100%" v-loading="state.loading" border @row-click="handleClick">
						<el-table-column type="index" label="序号" width="55" align="center" fixed />
						<el-table-column prop="realName" label="姓名" width="120" show-overflow-tooltip fixed />
						<el-table-column label="出生日期" width="100" align="center" show-overflow-tooltip>
							<template #default="scope">
								{{ formatDate(new Date(scope.row.birthday), 'YYYY-mm-dd') }}
							</template>
						</el-table-column>
						<el-table-column label="性别" width="70" align="center" show-overflow-tooltip>
							<template #default="scope">
								<el-tag type="success" v-if="scope.row.sex === 1"> 男 </el-tag>
								<el-tag type="danger" v-else> 女 </el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="phone" label="手机号码" width="120" align="center" show-overflow-tooltip />
						<el-table-column prop="account" label="账号" width="120" align="center" show-overflow-tooltip />
						<el-table-column label="状态" width="70" align="center" show-overflow-tooltip>
							<template #default="scope">
								<el-switch v-model="scope.row.status" :active-value="1" :inactive-value="2" size="small" @change="changeStatus(scope.row)" v-auth="'sysUser:setStatus'" />
							</template>
						</el-table-column>
						<el-table-column prop="entryTime" label="入职时间" width="120" align="center" show-overflow-tooltip />
						<el-table-column prop="contractTime" label="合同到期时间" width="120" align="center" show-overflow-tooltip />
						<el-table-column prop="contractStatus" label="合同状态" width="120" align="center" show-overflow-tooltip>
							<template #default="scope">
								<text v-if="scope.row.contractStatus == 0">试用</text>
								<text v-if="scope.row.contractStatus == 1">正式</text>
								<text v-if="scope.row.contractStatus == 2">离职</text>
							</template>
						</el-table-column>
						<el-table-column prop="confirmationTime" label="转正时间" width="120" align="center" show-overflow-tooltip />
						<el-table-column prop="resignationTime" label="离职时间" width="120" align="center" show-overflow-tooltip />
						<el-table-column prop="orgname" label="部门" width="120" align="center" show-overflow-tooltip />
						<el-table-column prop="posname" label="职位" width="120" align="center" show-overflow-tooltip />
						<el-table-column prop="orderNo" label="排序" width="70" align="center" show-overflow-tooltip />
						<!-- <el-table-column prop="createTime" label="修改时间" width="160" show-overflow-tooltip /> -->
						<el-table-column prop="remark" label="备注" show-overflow-tooltip />
						<el-table-column label="操作" width="150" align="center" fixed="right" show-overflow-tooltip>
							<template #default="scope">
								<!-- 只有当不是管理员时才显示编辑和删除按钮 -->
								<template v-if="scope.row.accountType !== 4 && scope.row.accountType !== 999">
									<el-button icon="ele-Edit" size="small" text type="primary" @click="openEditUser(scope.row)" v-auth="'sysEmployee:update'"> 编辑 </el-button>
									<el-button icon="ele-Delete" size="small" text type="primary" @click="delUser(scope.row)"> 删除 </el-button>
								</template>
								<!-- 如果是管理员，显示提示文本或者什么都不显示 -->
								<template v-else>
									<el-tag size="small" type="info">管理员账号</el-tag>
								</template>
							</template>
						</el-table-column>
					</el-table>
					<el-pagination
						v-model:currentPage="state.tableParams.page"
						v-model:page-size="state.tableParams.pageSize"
						:total="state.tableParams.total"
						:page-sizes="[10, 20, 50, 100]"
						small
						background
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
						layout="total, sizes, prev, pager, next, jumper"
					/>
				</el-card>
			</el-col>
		</el-row>

		<EditUser ref="editUserRef" :title="state.editUserTitle" :orgData="state.orgTreeData" @handleQuery="handleQuery" />
	</div>
</template>

<script lang="ts" setup name="sysUser">
import { onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatDate } from '/@/utils/formatTime';
import { auth } from '/@/utils/authFunction';
import OrgTree from '/@/views/system/org/component/orgTree.vue';
import EditUser from '/@/views/system/employee/component/editUser.vue';

import { getAPI } from '/@/utils/axios-utils';
import { SysUserApi, SysOrgApi } from '/@/api-services/api';
import { SysUser, SysOrg } from '/@/api-services/models';

const orgTreeRef = ref<InstanceType<typeof OrgTree>>();
const editUserRef = ref<InstanceType<typeof EditUser>>();
const router = useRouter();
const state = reactive({
	loading: false,
	userData: [] as Array<SysUser>,
	orgTreeData: [] as Array<SysOrg>,
	queryParams: {
		orgId: -1,
		account: undefined,
		realName: undefined,
		phone: undefined,
	},
	tableParams: {
		page: 1,
		pageSize: 10,
		total: 0 as any,
	},
	editUserTitle: '',
	btnStatus: true,
	rowInfo: {}, //选中员工信息
});

onMounted(async () => {
	loadOrgData();
	handleQuery();
});

// 查询机构数据
const loadOrgData = async () => {
	state.loading = true;
	var res = await getAPI(SysOrgApi).apiSysOrgListGet(0);
	state.orgTreeData = res.data.result ?? [];
	state.loading = false;
};

// 查询操作
const handleQuery = async () => {
	state.loading = true;
	let params = Object.assign(state.queryParams, state.tableParams);
	debugger;
	var res = await getAPI(SysUserApi).apiSysUserPagePost(params);
	debugger;
	state.userData = res.data.result?.items ?? [];
	state.tableParams.total = res.data.result?.total;
	state.loading = false;
};

// 重置操作
const resetQuery = () => {
	state.queryParams.orgId = -1;
	state.queryParams.account = undefined;
	state.queryParams.realName = undefined;
	state.queryParams.phone = undefined;
	handleQuery();
};

// 打开新增页面
const openAddUser = () => {
	state.editUserTitle = '添加员工信息';
	editUserRef.value?.openDialog({});
};

// 打开编辑页面
const openEditUser = (row: any) => {
	if (row.accountType === 4 || row.accountType === 999) {
		ElMessage.warning('管理员账号不允许编辑');
		return;
	}
	state.editUserTitle = '编辑员工信息';
	editUserRef.value?.openDialog(row);
};

// 删除
const delUser = (row: any) => {
	if (row.accountType === 4 || row.accountType === 999) {
		ElMessage.warning('管理员账号不允许删除');
		return;
	}
	ElMessageBox.confirm(`确定删除员工信息：【${row.account}】?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await getAPI(SysUserApi).apiSysUserDeletePost({ id: row.id });
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	state.tableParams.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	state.tableParams.page = val;
	handleQuery();
};

// 修改状态
const changeStatus = (row: any) => {
    if (row.accountType === 4 || row.accountType === 999) {
        ElMessage.warning('管理员账号不允许修改状态');
        row.status = row.status === 1 ? 2 : 1; // 恢复开关状态
        return;
    }
    getAPI(SysUserApi)
        .apiSysUserSetStatusPost({ id: row.id, status: row.status })
        .then(() => {
            ElMessage.success('账号状态设置成功');
        })
        .catch(() => {
            row.status = row.status === 1 ? 2 : 1;
        });
};

// 重置密码
const resetUserPwd = async (row: any) => {
	ElMessageBox.confirm(`确定重置密码：【${row.account}】?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await getAPI(SysUserApi).apiSysUserResetPwdPost({ id: row.id });
			ElMessage.success('密码重置成功：123456');
		})
		.catch(() => {});
};

// 树组件点击
const nodeClick = async (node: any) => {
	state.queryParams.orgId = node.id;
	state.queryParams.account = undefined;
	state.queryParams.realName = undefined;
	state.queryParams.phone = undefined;
	handleQuery();
};
const query = ref({ id: 1 });
// 点击开通账号按钮跳转到新增账号页面
const openAccount = () => {
	if (state.rowInfo.account) {
		ElMessage.warning('该员工已有账号');
	} else {
		router.push({ name: 'sysUser', state: state.rowInfo });
	}
};
// 点击列表某一行数据
const handleClick = (row: any) => {
	if (row) {
		state.btnStatus = false;
		state.rowInfo = row;
	}
};
</script>
