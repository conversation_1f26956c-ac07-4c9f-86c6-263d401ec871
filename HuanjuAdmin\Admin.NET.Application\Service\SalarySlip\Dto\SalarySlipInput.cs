﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 工资条基础输入参数
/// </summary>
public class SalarySlipBaseInput
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public virtual long UserID { get; set; }

    /// <summary>
    /// 月份
    /// </summary>
    public virtual DateTime? SlipMonth { get; set; }

    /// <summary>
    /// 应勤天数
    /// </summary>
    public virtual decimal? ShouldDays { get; set; }

    /// <summary>
    /// 出勤天数
    /// </summary>
    public virtual decimal? WorkDays { get; set; }

    /// <summary>
    /// 应发工资
    /// </summary>
    public virtual decimal? Should<PERSON><PERSON> { get; set; }

    /// <summary>
    /// 绩效等级
    /// </summary>
    public virtual string? Grading { get; set; }

    /// <summary>
    /// 绩效
    /// </summary>
    public virtual decimal? KpiMoney { get; set; }

    /// <summary>
    /// 提成基数
    /// </summary>
    public virtual decimal? CommissionBase { get; set; }

    /// <summary>
    /// 提成系数
    /// </summary>
    public virtual decimal? CommissionRatio { get; set; }

    /// <summary>
    /// 提成
    /// </summary>
    public virtual decimal? Commission { get; set; }

    /// <summary>
    /// 迟到早退
    /// </summary>
    public virtual int? WorkLateLeveEarly { get; set; }

    /// <summary>
    /// 考勤扣除
    /// </summary>
    public virtual decimal? AttendanceTakeOff { get; set; }

    /// <summary>
    /// 餐补
    /// </summary>
    public virtual decimal? Meals { get; set; }

    /// <summary>
    /// 话费补贴
    /// </summary>
    public virtual decimal? PhoneBill { get; set; }

    /// <summary>
    /// 交通补贴
    /// </summary>
    public virtual decimal? TafficBill { get; set; }

    /// <summary>
    /// 住房补贴
    /// </summary>
    public virtual decimal? HouseBill { get; set; }

    /// <summary>
    /// 全勤奖
    /// </summary>
    public virtual decimal? Attendance { get; set; }

    /// <summary>
    /// 其它奖励
    /// </summary>
    public virtual decimal? OrtheMoney { get; set; }

    /// <summary>
    /// 社保代扣
    /// </summary>
    public virtual decimal? SocialInsurance { get; set; }

    /// <summary>
    /// 公积金代缴
    /// </summary>
    public virtual decimal? AccumulationFund { get; set; }

    /// <summary>
    /// 个税代缴
    /// </summary>
    public virtual decimal? Tax { get; set; }

    /// <summary>
    /// 其它扣款
    /// </summary>
    public virtual decimal? OrtherTakOff { get; set; }

    /// <summary>
    /// 实发
    /// </summary>
    public virtual decimal? ActualMoney { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public virtual SalarySlipStatusEnum Status { get; set; }

}

/// <summary>
/// 工资条分页查询输入参数
/// </summary>
public class SalarySlipInput : BasePageInput
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public long UserID { get; set; }

    /// <summary>
    /// 用户
    /// </summary>
    public string SysUserRealName { get; set; }

    /// <summary>
    /// 绩效等级
    /// </summary>
    public string? Grading { get; set; }

    /// <summary>
    /// 月份
    /// </summary>
    public DateTime? SlipMonth { get; set; } 

    /// <summary>
    /// 状态
    /// </summary>
    public SalarySlipStatusEnum Status { get; set; }

}

/// <summary>
/// 工资条增加输入参数
/// </summary>
public class AddSalarySlipInput : SalarySlipBaseInput
{
    public DateTime? SlipMonth { get; set; }
}

/// <summary>
/// 工资条删除输入参数
/// </summary>
public class DeleteSalarySlipInput : BaseIdInput
{
}

/// <summary>
/// 工资条更新输入参数
/// </summary>
public class UpdateSalarySlipInput : SalarySlipBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long Id { get; set; }

}

/// <summary>
/// 工资条主键查询输入参数
/// </summary>
public class QueryByIdSalarySlipInput : DeleteSalarySlipInput
{

}
