﻿<!-- <template>
	出库单弹窗 
	<div class="warehouseout-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="520" draggable="">
			<el-table :data="tableData" tooltip-effect="light" row-key="id" border="" class="tcTable">
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="productCode" label="商品编码" show-overflow-tooltip="" width="120" />
				<el-table-column prop="brandName" label="品牌" show-overflow-tooltip="" width="120" />
				<el-table-column prop="specsName" label="规格" show-overflow-tooltip="" width="120" />
					<el-table-column  prop="OutboundNum" label="出库数量" width="120"
						show-overflow-tooltip="">

						<template #default="scope">
							<el-input v-model="scope.row.OutboundNum" placeholder=""
								clearable class="w85" />
						</template>
					</el-table-column>	
	</el-table>
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" class="mb10" :rules="rules">
					<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="ruleForm.id" />
					</el-form-item>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="出库数量" prop="OutboundNum">
							<el-input-number v-model="ruleForm.OutboundNum" placeholder="请输入出库数量" clearable />
							</el-form-item>
							</el-col>
				</el-row>
			</el-form> 
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button type="primary" @click="GetOutbound" size="default">确 定</el-button>
				</span>
			</template>  
		</el-dialog>
	</div>
</template>
 -->


<template>
	<div class="warehouseconvertrecord-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="1100" draggable="" :close-on-click-modal="false">
			<el-form>
				<!-- 			<el-form-item>
  <el-upload
      ref="uploadRefs"
      :limit="1"
      :auto-upload="false"
      action=""
      accept=".xlsx, .xls"
	  :http-request="uploadAction"
	  :on-success="handleSuccess"
      :before-upload="uploadExcel"
      :show-file-list="true"
      v-model:file-list="form.fileList"
    >
    <el-button type="primary">导入唯一码</el-button>
    </el-upload>  
	<el-button  @click="DownloadImportTemps">下载模板</el-button>
</el-form-item> -->
				<!--      <el-button  class="addBtn" type="primary" icon="ele-Plus" @click="Warehousing"
            :disabled="!disb"> 导入唯一码 </el-button> -->
			</el-form>
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" :rules="rules">
				<el-table :data="tableData" style="width: 100%" tooltip-effect="light">
					<!-- 	<el-row :gutter="35"> -->
					<el-table-column prop="tradename" label="商品名称" show-overflow-tooltip="" width="109" />
					<el-table-column prop="brand" label="品牌" show-overflow-tooltip="" width="80" />
					<el-table-column prop="specifications" label="规格" show-overflow-tooltip="" width="80" />
					<el-table-column prop="unitName" label="单位" show-overflow-tooltip="" width="109">
						<template #default="scope">
							<el-select clearable filterable v-model="scope.row.unitName" placeholder=" " disabled>
								<el-option v-for=" (item, index) in counterStore.unitList" :key="index" :value="item.value"
									:label="item.label" />
							</el-select>
						</template>
					</el-table-column>
					<el-table-column label="是否良品" show-overflow-tooltip="" width="100">
						<template #default="scope">
							<el-tag v-if="scope.row.goodProduct"> 良品 </el-tag>
							<el-tag type="danger" v-else=""> 次品 </el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="outCount" label="单据数量" show-overflow-tooltip="" width="100">

						<!-- 			<template #default="scope">
								<el-input-number v-model="scope.row.outCount"  clearable />
							</template> -->
					</el-table-column>
					<el-table-column prop="trueOutCount" label="已出库数量" show-overflow-tooltip="" width="109" />
					<el-table-column prop="thisOutCount" label="出库数量" show-overflow-tooltip="" width="200">
						<template #default="scope">
							<el-input-number v-model="scope.row.txOutCount" clearable />
						</template>
					</el-table-column>
					<el-table-column prop="" label="操作" show-overflow-tooltip="" width="150" fixed="right">
						<template #default="scope">
							<el-button size="small" text="" type="primary" v-if="scope.row.isbatch"
								@click="openIsBatchDialog(scope.row)">批次号</el-button>
							<el-button size="small" text="" type="primary" v-if="scope.row.isuniquecode">唯一码</el-button>
						</template>
					</el-table-column>
					<!-- </el-row> -->
				</el-table>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="GetOutbound" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
		<batchDialog ref="batchDialogRef" :title="batchDialogTitle" />
	</div>
</template>

<script lang="ts" setup>
import useCounter from '/@/stores/counter'
import { ref, onMounted } from "vue";
import type { FormRules } from "element-plus";
import { addWarehouseout, updateWarehouseout } from "/@/api/main/warehouseout";
import { Pubcustom } from "/@/api/main/pubcustom";
import { ElMessageBox, ElMessage } from "element-plus";
import { Outbound } from '/@/api/main/warehouseout';
import batchDialog from '/@/views/main/warehouseout/component/batchDialog.vue';

import { addWarehouseconvertrecord, updateWarehouseconvertrecord } from "/@/api/main/warehouseconvertrecord";
import { GetImport, DownloadImportTemp } from "/@/api/main/warehouseInrecordMX";
import { WarehouseStoreAdd } from '/@/api/main/warehouseInrecord';
import type { UploadInstance } from 'element-plus'
//父级传递来的函数，用于回调
const tableData = ref<any>([]);
const batchDialogTitle = ref('');//批次商品标题
const batchDialogRef = ref();
let tableMxDeleted = ref<any>([]);
const counterStore = useCounter()
const getMx = ref([])
//自行添加其他规则
const rules = ref<FormRules>({
});

//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: "",
	},
});
const emit = defineEmits(["reloadTable"]);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const loading = ref(false);
const ruleForm = ref<any>({});
//自行添加其他规则

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	isShowDialog.value = false;
	setTimeout(() => {
		loading.value = false;
	},500)
};
// 已出库数量
// const changeTxOutCount = (value :Number) => {
//    if (value == 0) {
// 	 ElMessage.warning('出库数量不得为0');
//    }
// };
// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};
// 打开弹窗
const openDialog = (row: any) => {

	tableData.value = JSON.parse(JSON.stringify(row));
	// tableData.value.forEach(item => {
	//    item.txOutCount = ''
	// });
	isShowDialog.value = true;
	/* tableDataMX.value = row; */
	getMx.value = row;
	isShowDialog.value = true;
};



const GetOutbound = (row: any) => {
	ElMessageBox.confirm(`确定要出库吗?`, "提示", {
		confirmButtonText: "确定",
		cancelButtonText: "取消",
		type: "warning",
	})
		.then(async () => {

			ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
				if (isValid) {
					loading.value = true;
					// 计算所有行的出库数量总和
					const totalOutboundCount = tableData.value.reduce((sum: any, item: any) => {
						return sum + (item.txOutCount || 0); // 使用 || 0 确保 txOutCount 为数字
					}, 0);

					// 判断总和是否小于等于0
					if (totalOutboundCount <= 0) {
						ElMessage.warning('出库总数量必须大于0');
						return false; // 终止出库操作
					}
					let arr: any[] = []
					tableData.value.every((item: any) => {
						if (item.txOutCount > (item.outCount - item.trueOutCount)) {
							item.txOutCount = 0;
							ElMessage.warning('填写的出库数量不能超出可出库数量');
							return false
						} else if (item.txOutCount==null ||item.txOutCount === 0) {
							return true;
						} else if (item.txOutCount < 0) {
							ElMessage.warning('出库数量不得小于0');
							return false;
						}
						else {
							let obj = {
								id: item.id,//序号
								OutId: item.outId,
								outCount: item.outCount,
								goodsId: item.goodsId,
								thisOutCount: item.txOutCount,
								goodProduct: item.goodProduct,
								warehouseId: item.warehouseId
							}
							arr.push(obj);
							return true
						}
					})

					if (arr.length != 0) {
						await Outbound(arr);
						ElMessage.success('出库成功！');
						emit("reloadTable");
						isShowDialog.value = false;
					}
					setTimeout(() => {
						loading.value = false;
					},500)
				} else {
					ElMessage({
						message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
						type: "error",
					});
				}
			});

			//handleQuery();

		})
		.catch(() => { });
};

//上传成功提示
const handleSuccess = () => {
	debugger;
	ElMessage.success('导入数据成功！');
};
const exceedFile = async (file: any) => {
	debugger;

}
const uploadAction = async (file: any) => {
	debugger;

}
const DownloadImportTemps = async () => {
	debugger;
	var res = await DownloadImportTemp();

	let href = window.URL.createObjectURL(new Blob([res.data]));
	const now = new Date()
	let link = document.createElement('a');
	link.style.display = 'none';
	link.href = href;
	link.setAttribute('download', "唯一码导入模板" + '.xlsx');
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link); // 下载完成移除元素
	window.URL.revokeObjectURL(href); // 释放掉blob对象
}
// 文件上传
const uploadExcel = async (file: File) => {
	debugger;
	const formData = new FormData();
	formData.append('file', file);
	let gasDataFile = form.value.fileList[0].raw;
	formData.append('file', gasDataFile);
	/* if(form.time=='' || form.fileList[0]== '' || form.files[0] == '') */
	/* return ElMessage.error('日期或者文件不能为空！') */
	/*  let gasDataFile =file[0].raw 
	  */
	await GetImport(formData).then((res: any) => {
		if (res.message == '成功') {
			ElMessage.success('导入成功！');
			//  dialogFormVisible.value=false
		} else {
			ElMessage.error('导入失败！')
		}
	})
}
/* const openDialog = (row: any, rowMX: any,str:any) => {
	debugger;
	ruleForm.value = row;
	if (rowMX == undefined)
		tableDataMX.value = [];
	else
		tableDataMX.value = rowMX;	

	// console.log(tableDataMX.value);
	isShowDialog.value = true;
}; */

// 打开批次商品
const openIsBatchDialog = () => {
	batchDialogTitle.value = '添加批次商品明细';
	batchDialogRef.value.openDialog(tableData.value);
};



// 页面加载时
onMounted(async () => {
});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<style lang="scss" scoped>
.el-form-item--default {
	margin-bottom: 10px;
}
</style>




