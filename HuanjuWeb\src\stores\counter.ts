import { defineStore } from 'pinia';

/*defineStore 是需要传参数的，其中第一个参数是id，就是一个唯一的值，
简单点说就可以理解成是一个命名空间.
第二个参数就是一个对象，里面有三个模块需要处理，第一个是 state，
第二个是 getters，第三个是 actions。
*/
const useCounter = defineStore("counter", {
	state: () => ({
		inhouseTypeList: [//入库类型
			{ label: '采购入库', value: 0 },
			{ label: '生产入库', value: 1 },
			{ label: '退货入库', value: 2 },
			{ label: '换货入库', value: 3 },
			{ label: '其他入库', value: 4 },
		],
		outboundStatusList: [//出库类型
			{ label: '销售出库', value: 0 },
			{ label: '换货出库', value: 1 },
			{ label: '维修出库', value: 2 },
			{ label: '其它出库', value: 3 },
			{ label: '领料出库', value: 4 },
		],
		ratingList: [
			{ label: '良品', value: 1 },
			{ label: '次品', value: 0 },
		],
		ShelflifeUnitList: [
			{ label: '年', value: '年' },
			{ label: '月', value: '月' },
			{ label: '日', value: '日' },
		],
		unitList: [//包装
			{ label: '个', value: '个' },
			{ label: '件', value: '件' },
			{ label: '箱', value: '箱' },
			{ label: '袋', value: '袋' },
			{ label: '瓶', value: '瓶' },
			{ label: '包', value: '包' },
			{ label: '盒', value: '盒' },
			{ label: '件', value: '件' },
			{ label: '条', value: '条' },
		],
		contractList: [//员工状态
			{ label: '试用', value: '0' },
			{ label: '正式', value: '1' },
			{ label: '离职', value: '2' },
		],
		hasList: [
			{ label: '否', value: 0 },
			{ label: '是', value: 1 },
		],
		warehouseList: [],//仓库
		supplierList: [], //供应商
		outboundTypeList: [
			{ label: '待出库', value: '0' },
			{ label: '部分出库', value: '1' },
			{ label: '全部出库', value: '2' },

		],//出库类型
		//goodsList:[],// 商品列表已经移到 stores/goods.ts 中统一管理,这里可以删除
		sexList: [
			{ label: '男', value: 1 },
			{ label: '女', value: 2 },
		],
		canUseList: [

			{ label: '启用', value: 1 },
			{ label: '禁用', value: 0 },
		],
		taxRateList: [

			{ label: '0%', value: 0 },
			{ label: '1%', value: 0.01 },
			{ label: '1.5%', value: 0.015 },
			{ label: '3%', value: 0.03 },
			{ label: '4%', value: 0.04 },
			{ label: '5%', value: 0.05 },
			{ label: '6%', value: 0.06 },
			{ label: '9%', value: 0.09 },
			{ label: '10%', value: 0.1 },
			{ label: '11%', value: 0.11 },
			{ label: '13%', value: 0.13 },
			{ label: '16%', value: 0.16 },
			{ label: '17%', value: 0.17 },
		],
	}),

	getters: {

	},

	actions: {

	}
})

//暴露这个useCounter模块
export default useCounter