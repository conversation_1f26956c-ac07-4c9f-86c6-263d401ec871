<template>
	<div class="outInBound-container">
		<el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="queryParams" ref="queryForm" :inline="true">
				<el-form-item label="类型：">
					<el-select v-model="queryParams.type" placeholder="请选择类型" clearable="">
						<el-option v-for="item in outInTypes" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="打印状态：">
					<el-select v-model="queryParams.printState" placeholder="打印状态：" clearable="">
						<el-option v-for="item in printStatus" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="单号">
					<el-input v-model="queryParams.orderNumber" clearable="" placeholder="请输入单号" />
				</el-form-item>

				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'outInBound:page'"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="() => (queryParams = {})"> 重置 </el-button>
						<el-button type="success" icon="ele-Printer" @click="handlePrint" :disabled="selectedRows.length === 0"> 打印 </el-button>
						<el-button type="danger" icon="ele-RefreshLeft" @click="handleRedInk" :disabled="selectedRows.length === 0" v-auth="'outInBound:redink'"> 红冲 </el-button>
					</el-button-group>
				</el-form-item>
			</el-form>
		</el-card>
		<el-card class="full-table" shadow="hover" style="margin-top: 8px">
			<el-table :data="tableData" style="width: 100%" v-loading="loading" tooltip-effect="light" row-key="id" border @selection-change="handleSelectionChange">
				<el-table-column type="selection" width="55" fixed />
				<el-table-column type="index" label="序号" width="55" align="center" fixed />
				<el-table-column prop="orderNum" label="流水号" width="120" show-overflow-tooltip fixed />
				<!-- <el-table-column prop="orderNumber" label="单号" show-overflow-tooltip /> -->
				<el-table-column prop="type" label="类型" show-overflow-tooltip>
					<template #default="scope">
						<el-tag type="danger" v-if="scope.row.type === '0'">出库</el-tag>
						<el-tag type="success" v-if="scope.row.type === '1'">入库</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="printCount" label="打印状态" show-overflow-tooltip>
					<template #default="scope">
						<el-tag type="danger" v-if="scope.row.printCount === 0">未打印</el-tag>
						<el-tag type="success" v-if="scope.row.printCount > 0">已打印</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="company" label="客户/供应商" width="155" show-overflow-tooltip />
				<el-table-column prop="warehouseName" label="仓库" show-overflow-tooltip />
				<el-table-column prop="goodsName" label="商品名称" width="155" show-overflow-tooltip />
				<el-table-column prop="barCode" label="商品条码" show-overflow-tooltip />
				<el-table-column prop="code" label="编码" show-overflow-tooltip />
				<el-table-column prop="brand" label="品牌" show-overflow-tooltip />
				<el-table-column prop="specs" label="规格" show-overflow-tooltip />
				<el-table-column prop="unitName" label="单位" show-overflow-tooltip />
				<el-table-column prop="outInCount" label="数量" show-overflow-tooltip />
				<el-table-column prop="goodProduct" label="产品类型" width="100" show-overflow-tooltip>
					<template #default="scope">
						<el-tag 
							:type="getProductTypeTagType(scope.row.goodProduct)" 
							size="small">
							{{ getProductTypeText(scope.row.goodProduct) }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="redInkStatus" label="红冲状态" width="100" show-overflow-tooltip>
					<template #default="scope">
						<el-tag 
							:type="getRedInkStatusTagType(scope.row)" 
							size="small">
							{{ getRedInkStatusText(scope.row) }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="batchNumber" label="批次号" width="120" show-overflow-tooltip />
				<el-table-column label="修改记录" show-overflow-tooltip fixed="right">
					<template #default="scope">
						<ModifyRecord :data="scope.row" />
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="tableParams.page"
				v-model:page-size="tableParams.pageSize"
				:total="tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				small=""
				background=""
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
		</el-card>
	</div>
</template>

<script lang="ts" setup="" name="outInBound">
import { Session } from '/@/utils/storage';
import { ref } from 'vue';
import ModifyRecord from './components/modifyRecord.vue';
import { pageOutboundRecord, getOnePrint, updatePrintCount, redInkOutboundRecord, redInkInboundRecord } from '/@/api/main/outInBound';
import { ElMessage, ElMessageBox } from 'element-plus';
import { hiprint } from 'vue-plugin-hiprint';

const loading = ref(false);
const tableData = ref<any>([]);
const queryParams = ref<any>({});
const tableParams = ref({
	page: 1,
	pageSize: 10,
	total: 0,
});

const selectedRows = ref<any>([]);
let hiprintTemplate = ref();
const printedRecords = []; // 用于存储已打印的记录 ID 和类型

const outInTypes = ref([
	{
		label: '出库',
		value: '0',
	},
	{
		label: '入库',
		value: '1',
	},
]);

const printStatus = ref([
	{
		label: '未打印',
		value: '0',
	},
	{
		label: '已打印',
		value: '1',
	},
]);

// 判断是否为良品的函数
const isGoodProduct = (value: any): boolean => {
	// 详细的类型检查和转换
	console.log('isGoodProduct 检查:', { value, type: typeof value, json: JSON.stringify(value) });
	
	// 处理 true 值
	if (value === true || value === 'true' || value === 1 || value === '1') {
		return true;
	}
	
	// 处理 false 值
	if (value === false || value === 'false' || value === 0 || value === '0') {
		return false;
	}
	
	// 处理字符串形式的布尔值
	if (typeof value === 'string') {
		const lowerValue = value.toLowerCase();
		if (lowerValue === 'true' || lowerValue === 'yes' || lowerValue === '是') {
			return true;
		}
		if (lowerValue === 'false' || lowerValue === 'no' || lowerValue === '否') {
			return false;
		}
	}
	
	// 对于 null 或 undefined，默认为良品（这是业务逻辑决定）
	if (value === null || value === undefined) {
		console.warn('GoodProduct值为null/undefined，默认设为良品');
		return true;
	}
	
	// 其他未识别的值，默认为次品
	console.warn('未识别的GoodProduct值:', value);
	return false;
};

// 获取产品类型文本
const getProductTypeText = (value: any): string => {
	if (value === null || value === undefined) {
		return '良品'; // 默认为良品
	}
	return isGoodProduct(value) ? '良品' : '次品';
};

// 获取产品类型标签类型
const getProductTypeTagType = (value: any): string => {
	if (value === null || value === undefined) {
		return 'success'; // 默认为良品的绿色
	}
	return isGoodProduct(value) ? 'success' : 'warning';
};

// 获取红冲状态文本
const getRedInkStatusText = (row: any): string => {
	// 优先使用redInkStatus字段
	if (row.redInkStatus !== undefined && row.redInkStatus !== null) {
		switch (row.redInkStatus) {
			case 0: return '正常';
			case 1: return '已红冲';
			case 2: return '红冲记录';
			default: return '未知';
		}
	}
	
	// 兼容性检查：通过流水号和数量判断（用于没有redInkStatus字段的旧数据）
	if (row.orderNum?.includes('-红冲')) {
		return '红冲记录';
	}
	if (row.outInCount < 0) {
		return '红冲记录';
	}
	
	return '正常';
};

// 获取红冲状态标签类型
const getRedInkStatusTagType = (row: any): string => {
	const statusText = getRedInkStatusText(row);
	switch (statusText) {
		case '正常': return 'success';
		case '已红冲': return 'warning';
		case '红冲记录': return 'danger';
		default: return 'info';
	}
};

// 查询操作
const handleQuery = async () => {
	loading.value = true;
	var res = await pageOutboundRecord(Object.assign(queryParams.value, tableParams.value));
	tableData.value = res.data.result?.items ?? [];
	tableParams.value.total = res.data.result?.total;
	loading.value = false;
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	tableParams.value.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	tableParams.value.page = val;
	handleQuery();
};

// 处理多选变化
const handleSelectionChange = (val: any[]) => {
	selectedRows.value = val;
};

// Modified initializeHiprintTemplate function:
async function initializeHiprintTemplate(type: string) {
	let printTemType: string;

	if (type === '0') {
		printTemType = 'CkdProviderModule'; // 出库单模板类型
	} else if (type === '1') {
		printTemType = 'RkdProviderModule'; // 入库单模板类型
	} else {
		throw new Error('无效的单据类型');
	}

	// const printInput = {
	//   printTemType: printTemType,
	// };

	try {
		const res = await getOnePrint({ printTemType: printTemType });
		let templateContent = res.data.result.template;
		hiprintTemplate.value = new hiprint.PrintTemplate({ template: JSON.parse(templateContent) });
	} catch (error) {
		console.error('初始化或打印模板时出错:', error);
		throw error; // Propagate the error
	}
}

// Proposed solution:
async function handlePrint() {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请选择要打印的记录');
		return;
	}

	var userName = '';
	if (Session.get('userInfo')) {
		let userInfos = Session.get('userInfo');
		userName = userInfos.realName;
	}

	const groupedRows = selectedRows.value.reduce((acc, row) => {
		if (!acc[row.orderNumber]) {
			acc[row.orderNumber] = [];
		}
		acc[row.orderNumber].push(row);
		return acc;
	}, {});

	for (const [orderNumber, rows] of Object.entries(groupedRows)) {
		const type = rows[0].type; // 获取当前订单的类型
		const printData = {
			header: rows[0].type === '0' ? '出库单' : '入库单',
			orderNum: orderNumber,
			warehouseName: rows[0].warehouseName,
			companyName: rows[0].company,
			telPhone: rows[0].telPhone || '',
			creater: userName,
			printDate: new Date().toLocaleString('zh-CN', { hour12: false }),
			table: rows.map((row) => ({
				ORDERNUM: row.orderNum,
				NAME: row.goodsName,
				SL: row.outInCount,
				DW: row.unitName,
				GG: row.specs,
				PCH: row.batchNumber,
				TM: row.barCode,
			})),
		};

		await initializeHiprintTemplate(type);
		await new Promise((resolve) => setTimeout(resolve, 500)); // Give time for template to render
		hiprintTemplate.value.print(printData);
		printedRecords.push(...rows.map((row) => ({ id: row.id, type: row.type })));
	}

	// 打印完成后,更新打印次数
	try {
		await updatePrintCount(printedRecords);
		printedRecords.length = 0; // 清空数组
		ElMessage.success('打印完成');
		handleQuery(); // 重新加载数据以显示更新后的打印状态
	} catch (error) {
		console.error('更新打印次数时出错:', error);
		ElMessage.error('打印完成,但更新打印次数失败');
		printedRecords.length = 0; // 即使发生错误也清空数组
	}
}

// 红冲功能
const handleRedInk = async () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请选择要红冲的记录');
		return;
	}

	// 检查选中的记录是否都是同一类型
	const types = [...new Set(selectedRows.value.map(row => row.type))];
	if (types.length > 1) {
		ElMessage.warning('请选择相同类型的记录进行红冲');
		return;
	}

	// 检查红冲限制
	const invalidRecords = selectedRows.value.filter(row => {
		// 1. 检查是否已经被红冲过
		if (row.redInkStatus === 1) {
			return true;
		}
		// 2. 检查本身是否是红冲记录
		if (row.redInkStatus === 2) {
			return true;
		}
		// 3. 兼容性检查：通过流水号和数量判断（用于没有redInkStatus字段的旧数据）
		if (row.orderNum?.includes('-红冲')) {
			return true;
		}
		if (row.outInCount < 0) {
			return true;
		}
		return false;
	});

	if (invalidRecords.length > 0) {
		const invalidTypes = invalidRecords.map(row => {
			if (row.redInkStatus === 1) return '已被红冲';
			if (row.redInkStatus === 2) return '红冲记录';
			if (row.orderNum?.includes('-红冲')) return '红冲记录';
			if (row.outInCount < 0) return '红冲记录';
			return '不可红冲';
		});
		ElMessage.warning(`选中的记录中包含${invalidTypes.join('、')}的记录，不允许红冲`);
		return;
	}

	const recordType = types[0];
	const recordTypeName = recordType === '0' ? '出库' : '入库';

	ElMessageBox.confirm(
		`确定要对选中的 ${selectedRows.value.length} 条${recordTypeName}记录进行红冲操作吗？\n红冲操作将会：\n- 生成负数记录\n- 调整库存数量\n- 更新相关单据状态`,
		'红冲确认',
		{
			confirmButtonText: '确定红冲',
			cancelButtonText: '取消',
			type: 'warning',
			dangerouslyUseHTMLString: true,
		}
	)
		.then(async () => {
			try {
				const promises = selectedRows.value.map(async (row) => {
					if (row.type === '0') {
						// 出库红冲
						return await redInkOutboundRecord(row.id);
					} else {
						// 入库红冲
						return await redInkInboundRecord(row.id);
					}
				});

				await Promise.all(promises);
				
				ElMessage.success(`${recordTypeName}红冲操作完成`);
				handleQuery(); // 重新加载数据
			} catch (error) {
				console.error('红冲操作失败:', error);
				ElMessage.error(`红冲操作失败: ${error.message || '未知错误'}`);
			}
		})
		.catch(() => {
			ElMessage.info('已取消红冲操作');
		});
};

handleQuery();
</script>
