﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Core.Service;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Http;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.IO;
using System;

namespace Admin.NET.Application;
/// <summary>
/// 供应商管理服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class PubSupplierService : IDynamicApiController, ITransient
{
    private readonly UserManager _userManager;
    private readonly SqlSugarRepository<PubSupplier> _pubSupplierRep;
    private readonly SysRoleMenuService _sysRoleMenuService;
    private readonly SysUserRoleService _sysUserRoleService;
    private readonly SysCacheService _sysCacheService;
    private readonly SqlSugarRepository<ImportTemplate> _repImport;

    public PubSupplierService(UserManager userManager,
        SqlSugarRepository<PubSupplier> pubSupplierRep,
        SysRoleMenuService sysRoleMenuService,
        SysUserRoleService sysUserRoleService,
        SysCacheService sysCacheService, 
        SqlSugarRepository<ImportTemplate> repImport)
    {
        _userManager = userManager;
        _pubSupplierRep = pubSupplierRep;
        _sysRoleMenuService = sysRoleMenuService;
        _sysUserRoleService = sysUserRoleService;
        _sysCacheService = sysCacheService;
        _repImport = repImport;
    }

    /// <summary>
    /// 分页查询供应商管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<PubSupplierOutput>> Page(PubSupplierInput input)
    {
        var query = _pubSupplierRep.AsQueryable()
                    .Where(u => u.TenantId == _userManager.TenantId)
                    //管理员能看到全部，其它人可以看到公用和部门下的所有  
                    .WhereIF(!_userManager.SuperAdmin && !_userManager.Admin, u => u.IsCommunal || (!u.IsCommunal && u.OrgId == _userManager.OrgId))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Type), u => u.Type.Contains(input.Type.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Contacts), u => u.Contacts.Contains(input.Contacts.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Phone), u => u.Phone.Contains(input.Phone.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.TaxId), u => u.TaxId.Contains(input.TaxId.Trim()))
                    .Where(u => u.IsDelete == false)
                    .Select<PubSupplierOutput>();
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加供应商管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddPubSupplierInput input)
    {
        var entity = input.Adapt<PubSupplier>();
        entity.OrgId = _userManager.OrgId;
        await _pubSupplierRep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除供应商管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeletePubSupplierInput input)
    {
        var entity = await _pubSupplierRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _pubSupplierRep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新供应商管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdatePubSupplierInput input)
    {
        var entity = input.Adapt<PubSupplier>();
        entity.OrgId = _userManager.OrgId;
        await _pubSupplierRep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取供应商管理
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<PubSupplier> Get([FromQuery] QueryByIdPubSupplierInput input)
    {
        return await _pubSupplierRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取供应商管理列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<PubSupplierOutput>> List([FromQuery] PubSupplierInput input)
    {
        return await _pubSupplierRep.AsQueryable().Select<PubSupplierOutput>().ToListAsync();
    }


    /// <summary>
    /// 获取供应商ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "PubSupplierDropdown"), HttpGet]
    public async Task<dynamic> PubSupplierDropdown()
    {
        return await _pubSupplierRep.Context.Queryable<PubSupplier>()
                .Where(x => x.IsDelete == false && x.TenantId == _userManager.TenantId)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id
                }
                ).ToListAsync();
    }

    /// <summary>
    /// 供应商信息导入
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("import")]
    public async Task Import([FromForm] IFormFile file)
    {
        if (file == null || file.Length == 0)
            throw new Exception("文件不能为空");

        var importTemplate = await _repImport.GetFirstAsync(x => x.Name == "供应商信息");
        if (importTemplate == null)
        {
            throw new Exception("供应商信息模板不存在");
        }

        string currentRownum = "1";

        try
        {
            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                stream.Position = 0;

                IWorkbook workbook;
                if (file.FileName.EndsWith(".xlsx"))
                {
                    workbook = new XSSFWorkbook(stream);
                }
                else if (file.FileName.EndsWith(".xls"))
                {
                    workbook = new HSSFWorkbook(stream);
                }
                else
                {
                    throw new Exception("不支持的文件格式，请上传.xlsx或.xls文件");
                }

                ISheet sheet = workbook.GetSheetAt(0);

                for (int i = 0; i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row == null) continue;
                    if (i == 0)
                    {
                        if (GetCellValue(row.GetCell(0)).Trim() != importTemplate.Name) throw new Exception("模板名称不正确");
                        if (GetCellValue(row.GetCell(4)).Trim() != "v" + importTemplate.Version.ToString()) throw new Exception("模板版本不正确");
                        continue;
                    }
                    else if (i == 1) continue;

                    var supplier = new PubSupplier();

                    var xuhao = GetCellValue(row.GetCell(0));
                    currentRownum = xuhao;

                    supplier.Name = GetCellValue(row.GetCell(1));
                    if (supplier.Name.IsNullOrEmpty()) throw new Exception($"序号：{currentRownum}供应商名称为空");
                    supplier.Type = GetCellValue(row.GetCell(2));
                    supplier.Contacts = GetCellValue(row.GetCell(3));
                    if (supplier.Contacts.IsNullOrEmpty()) throw new Exception($"序号：{currentRownum}联系人为空");
                    supplier.Phone = GetCellValue(row.GetCell(4));
                    if (supplier.Phone.IsNullOrEmpty()) throw new Exception($"序号：{currentRownum}联系方式为空");
                    supplier.IsCommunal = true;
                    supplier.TaxId = GetCellValue(row.GetCell(5));
                    supplier.BankName = GetCellValue(row.GetCell(6));
                    supplier.BankCode = GetCellValue(row.GetCell(7));
                    supplier.Remark = GetCellValue(row.GetCell(8));
                    supplier.Address = GetCellValue(row.GetCell(9));
                    supplier.OrgId = _userManager.OrgId;

                    await _pubSupplierRep.InsertAsync(supplier);
                }
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"导入失败：{ex.Message}\t 序号：{currentRownum}");
        }
    }

    private string GetCellValue(ICell cell)
    {
        if (cell == null)
            return string.Empty;

        switch (cell.CellType)
        {
            case CellType.Numeric:
                return cell.NumericCellValue.ToString();
            case CellType.String:
                return cell.StringCellValue;
            case CellType.Boolean:
                return cell.BooleanCellValue.ToString();
            case CellType.Formula:
                return cell.CellFormula;
            default:
                return string.Empty;
        }
    }
}

