﻿using SqlSugar;
using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 商品出库明细基础输入参数
/// </summary>
public class WarehouseoutMXBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }


    /// <summary>
    /// 商品名称
    /// </summary>
    public string? Tradename { get; set; }

    /// <summary>
    /// 商品条码
    /// </summary>
    public string? Barcode { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    public string? Productcode { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Specifications { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public long? Unit { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal Unitprice { get; set; }

    /// <summary>
    /// 合计
    /// </summary>
    public decimal TotalAmt { get; set; }

    /// <summary>
    /// 是否缺货
    /// </summary>
    public bool Vacancy { get; set; }

    /// <summary>
    /// 辅助单位数量
    /// </summary>
    public int? AuxiliaryOutCount { get; set; }

    /// <summary>
    /// 辅助单位
    /// </summary>
    public long? auxiliaryunit { get; set; }

    /// <summary>
    /// 商品Id
    /// </summary>
    public long goodsId { get; set; }

    /// <summary>
    /// 出库数量
    /// </summary>
    public int? OutCount { get; set; }

    /// <summary>
    /// 实际出库数量
    /// </summary>
    public int? TrueOutCount { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// 出库单ID
    /// </summary>
    public long OutId { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? Deliverytime { get; set; }

    public bool GoodProduct { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    [SugarColumn(ColumnDescription = "软删除")]
    public virtual bool IsDelete { get; set; } = false;
}

/// <summary>
/// 商品出库明细分页查询输入参数
/// </summary>
public class WarehouseoutMXInput : BasePageInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }


    /// <summary>
    /// 商品名称
    /// </summary>
    public string? Tradename { get; set; }

    /// <summary>
    /// 商品条码
    /// </summary>
    public string? Barcode { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    public string? Productcode { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Specifications { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 是否缺货
    /// </summary>
    public int? Vacancy { get; set; }


    /// <summary>
    /// 商品Id
    /// </summary>
    public long GoodsId { get; set; }

    /// <summary>
    /// 出库数量
    /// </summary>
    public int OutCount { get; set; }

    /// <summary>
    /// 实际出库数量
    /// </summary>
    public int TrueOutCount { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// 出库单ID
    /// </summary>
    public long OutId { get; set; }


}

/// <summary>
/// 商品出库明细增加输入参数
/// </summary>
public class AddWarehouseoutMXInput : WarehouseoutMXBaseInput
{
}

/// <summary>
/// 商品出库明细删除输入参数
/// </summary>
public class DeleteWarehouseoutMXInput : BaseIdInput
{
}

/// <summary>
/// 商品出库明细更新输入参数
/// </summary>
public class UpdateWarehouseoutMXInput : WarehouseoutMXBaseInput
{
}

/// <summary>
/// 商品出库明细主键查询输入参数
/// </summary>
public class QueryByIdWarehouseoutMXInput : DeleteWarehouseoutMXInput
{

}
