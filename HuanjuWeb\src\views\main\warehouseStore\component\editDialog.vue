﻿<template>
	<div class="warehouseStore-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="500" draggable=""  :close-on-click-modal="false">
			<el-form class="form" :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" :rules="rules">
				<!-- <el-row :gutter="35"> -->
					<el-form-item v-show="false">
						<el-input v-model="ruleForm.id" />
					</el-form-item>
				<!-- 	<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="序号" prop="number">
							<el-input-number v-model="ruleForm.number" placeholder="请输入序号" clearable />
							
						</el-form-item>
						
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="仓库" prop="warehouse">
							<el-input v-model="ruleForm.warehouse" placeholder="请输入仓库" clearable />
							
						</el-form-item>
						
					</el-col> -->
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20"> -->

						<el-form-item label="批次号" prop="batchId" :rules="[{required:true,message:'请选择一个批次号',trigger:'change'}]" v-if="ruleForm.isbatch">
							<el-select v-model="ruleForm.batchId" placeholder="批次号" clearable style="width: 100%" >
								<el-option v-for="item in bitchs" :key="item.id" :value="item.id" :label="item.batchnumber"/>
							</el-select>
						</el-form-item>

						<el-form-item label="类型" prop="type" :rules="[{required:true,message:'请选择一个选项',trigger:'change'}]">
							<el-select v-model="ruleForm.type" placeholder="类型" clearable style="width: 100%" >
								<el-option label="良品转次品" :value="1" />
								<el-option label="次品转良品" :value="2" />
							</el-select>
						</el-form-item>
			    	<!-- </el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20"> -->
						<el-form-item label="转换数量" prop="connvertNum" :rules="[{required:true,message:'请输入转换数量',trigger:'change'}]">
							<el-input-number v-model="ruleForm.connvertNum" placeholder="请输入转换数量" clearable style="width: 100%;" :min="1" max="9999999"/>
							
						</el-form-item>
						
					<!-- </el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20"> -->
						<el-form-item label="备注" prop="notes">
							<el-input type="textarea" v-model="ruleForm.notes" placeholder="请输入备注" clearable />
						</el-form-item>
					<!-- </el-col> -->
				<!-- 	<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="商品名称" prop="tradeName">
							<el-input v-model="ruleForm.tradeName" placeholder="请输入商品名称" clearable />
							
						</el-form-item>
						
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="品牌" prop="brand">
							<el-input v-model="ruleForm.brand" placeholder="请输入品牌" clearable />
							
						</el-form-item>
						
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="规格" prop="specifications">
							<el-input v-model="ruleForm.specifications" placeholder="请输入规格" clearable />
							
						</el-form-item>
						
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="单位" prop="unit">
							<el-input v-model="ruleForm.unit" placeholder="请输入单位" clearable />
							
						</el-form-item>
						
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="是否保质期" prop="isWarranty">
							<el-switch v-model="ruleForm.isWarranty" active-text="是" inactive-text="否" />
							
						</el-form-item>
						
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="供应商" prop="supplier">
							<el-input v-model="ruleForm.supplier" placeholder="请输入供应商" clearable />
							
						</el-form-item>
						
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="商品编码" prop="productCode">
							<el-input v-model="ruleForm.productCode" placeholder="请输入商品编码" clearable />
							
						</el-form-item>
						
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="商品条码" prop="barCode">
							<el-input v-model="ruleForm.barCode" placeholder="请输入商品条码" clearable />
							
						</el-form-item>
						
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="是否唯一码" prop="isUniqueCode">
							<el-switch v-model="ruleForm.isUniqueCode" active-text="是" inactive-text="否" />
							
						</el-form-item>
						
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="保质期" prop="warranty">
							<el-input-number v-model="ruleForm.warranty" placeholder="请输入保质期" clearable />
							
						</el-form-item>
						
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="生产日期" prop="produceTime">
							<el-date-picker v-model="ruleForm.produceTime" type="date" placeholder="生产日期" />
							
						</el-form-item>
						
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="备注" prop="notes">
							<el-input v-model="ruleForm.notes" placeholder="请输入备注" clearable />
							
						</el-form-item>
						
					</el-col> -->
				<!-- </el-row> -->
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
	import { ref,onMounted } from "vue";
	import { ElMessage } from "element-plus";
	import type { FormRules } from "element-plus";
	import { AttributeConvert} from "/@/api/main/warehouseStore";
	import { pagewarehousebatch } from '/@/api/main/warehousebatch';
	//父级传递来的参数
	var props = defineProps({
	title: {
	type: String,
	default: "",
	},
	});
	//父级传递来的函数，用于回调
	const emit = defineEmits(["reloadTable"]);
	const type = ref();
	const sy = ref();
	const ruleFormRef = ref();
	const isShowDialog = ref(false);
	const loading = ref(false);
	const ruleForm = ref<any>({});
		let bitchs = ref<any>([]);
		//自行添加其他规则
		const rules = ref<FormRules>({
});

// 打开弹窗
const openDialog = (row: any) => {
  queryParams.value=row;
  ruleForm.value = JSON.parse(JSON.stringify(row));
  isShowDialog.value = true;
  handleQuery();
};

const queryParams = ref<any>
        ({});
		const tableParams = ref({
        page: 1,
        pageSize: 10,
        total: 0,
        });
        // 查询操作
        const handleQuery = async () => {
        var res = await pagewarehousebatch(Object.assign(queryParams.value, tableParams.value));
        bitchs.value  = res.data.result?.items ?? [];

        isShowDialog.value = true;
        };

// 关闭弹窗
const closeDialog = () => {
  emit("reloadTable");
  isShowDialog.value = false;
  setTimeout(() => {
		loading.value = false;
	},500)
};

// 取消
const cancel = () => {
  isShowDialog.value = false;
  loading.value = false;
};

// 提交
const submit = async () => {
	
  ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
	debugger;
    if (isValid) {
		loading.value = true;
      let values = ruleForm.value;
	  if(ruleForm.value.isbatch){
		let batch=bitchs.value.find(item=>item.id===ruleForm.value.batchId);
		if(values.type==1){
			if(values.connvertNum>batch.goodProductNum)
			{
			ElMessage.warning("转换数量超过库存数量");return;
			}
		}
		else{
			if(values.connvertNum>batch.gejectNum)
			{
			ElMessage.warning("转换数量超过库存数量");return;
			}
		}
	  }
	  if(values.type==1)
	  {
        if(values.connvertNum>values.goodProduct)
		{
			ElMessage.warning("转换数量超过库存数量");
		}else{
			values.goodProduct=values.goodProduct-values.connvertNum
			values.reject=values.reject+values.connvertNum
			await AttributeConvert(values);
		}
	  }else{
        if(values.connvertNum>values.reject)
		{
			ElMessage.warning("转换数量超过库存数量");
		}else{
			values.reject=values.reject-values.connvertNum
			values.goodProduct=values.goodProduct+values.connvertNum
			await AttributeConvert(values);
		}
	  }
      closeDialog();
    } else {
      ElMessage({
        message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
        type: "error",
      });
    }
  });
};





// 页面加载时
onMounted(async () => {
});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<style scoped>
  .form{
	width: calc(100% - 40px);
	padding:0 20px 20px;
	/* padding-bottom: 20px; */
  }
</style>



