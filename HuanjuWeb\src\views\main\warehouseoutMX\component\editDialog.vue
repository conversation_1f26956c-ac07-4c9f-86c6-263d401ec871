﻿<template>
	<div class="warehouseoutMX-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="700" draggable=""  :close-on-click-modal="false">
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" :rules="rules">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="仓库" prop="warehouseId">
							<el-select clearable filterable v-model="ruleForm.warehouseId" placeholder="请选择仓库">
								<el-option v-for=" (item, index) in counterStore.warehouseList" :key="index"
									:value="item.value" :label="item.label" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="商品名称" prop="goodsId">
							<el-select clearable filterable v-model="ruleForm.goodsId" placeholder="请选择商品"
								@change="getGoodsDetail(ruleForm, ruleForm.goodsId)">
								<el-option v-for=" (item, index) in goodsStore.goodsList" :key="index" :value="item.id"
									:label="item.name" />

							</el-select>
						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="商品条码" prop="barcode">
							<el-input v-model="ruleForm.barcode" disabled />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="商品编码" prop="productcode">
							<el-input v-model="ruleForm.productcode" disabled />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="品牌" prop="brand">
							<el-input v-model="ruleForm.brand" disabled />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="规格" prop="specifications">
							<el-input v-model="ruleForm.specifications" disabled />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="出库数量" prop="OutCount">
							<el-input-number v-model="ruleForm.OutCount"  />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="单位" prop="unit">
							<el-select clearable filterable v-model="ruleForm.unit" disabled=""  placeholder="请选择单位" >
								<el-option v-for=" (item, index) in WarehouseUnit" :key="index" :value="item.value"
									:label="item.label" />

							</el-select>

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="辅助单位数量" prop="auxiliaryOutCount">
							<el-input-number v-model="ruleForm.auxiliaryOutCount"  />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="辅助单位" prop="auxiliaryunit">
							<el-select clearable filterable v-model="ruleForm.auxiliaryunit" placeholder="请选择辅助单位" disabled>
								<el-option v-for=" (item, index) in WarehouseUnit" :key="index" :value="item.value"
									:label="item.label" />

							</el-select>

						</el-form-item>

					</el-col>


					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="是否缺货" prop="vacancy">
							<el-select clearable filterable v-model="ruleForm.vacancy" placeholder="请选择">
								<el-option v-for=" (item, index) in counterStore.hasList" :key="index" :value="item.value"
									:label="item.label" />

							</el-select>

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="客户名称" prop="customerName">
							<el-input v-model="ruleForm.customerName" placeholder="请输入客户名称" clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="联系人" prop="contacts">
							<el-input v-model="ruleForm.contacts" placeholder="请输入联系人" clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="电话" prop="phone">
							<el-input v-model="ruleForm.phone" placeholder="请输入电话" clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="地址" prop="address">
							<el-input v-model="ruleForm.address" placeholder="请输入地址" clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="出库时间" prop="deliverytime">
							<el-date-picker v-model="ruleForm.deliverytime" type="date" placeholder="出库时间" />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="备注" prop="notes">
							<el-input v-model="ruleForm.notes" placeholder="请输入备注" clearable />

						</el-form-item>

					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { useGoodsStore } from '/@/stores/goods'
import useCounterStore from '/@/stores/counter'
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { addWarehouseoutMX, updateWarehouseoutMX } from "/@/api/main/warehouseoutMX";
import { WarehouseGoodsUnit } from '/@/api/main/warehouseInrecord';
//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: "",
	},
});

const goodsStore = useGoodsStore();
const counterStore = useCounterStore();

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const loading = ref(false);
const ruleForm = ref<any>({});
//自行添加其他规则
const rules = ref<FormRules>({
});
const WarehouseUnit = ref<any>
        ([]);
const  WareUnit=async () => {
          debugger;
          var res = await WarehouseGoodsUnit();		
          WarehouseUnit.value=res.data.result;
        }

const getGoodsDetail = (row: any, val: any) => {
	debugger;
	let obj = goodsStore.getGoodsDetail(val);
	row.unit = obj?.unit || ""
	row.auxiliaryunit = obj?.auxiliaryunit || ""
	row.tradename = obj?.label || ""
	row.brand = obj?.brand
	row.barcode = obj?.id
	row.productcode = obj?.code
	row.specifications = obj?.specs
}

// 打开弹窗
const openDialog = (row: any) => {
	ruleForm.value = JSON.parse(JSON.stringify(row));
	isShowDialog.value = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	isShowDialog.value = false;
	setTimeout(() => {
		loading.value = false;
	},500)
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			loading.value = true;
			let values = ruleForm.value;
			if (ruleForm.value.id != undefined && ruleForm.value.id > 0) {
				await updateWarehouseoutMX(values);
			} else {
				await addWarehouseoutMX(values);
			}
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};





// 页面加载时
onMounted(async () => {
	WareUnit();
});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>




