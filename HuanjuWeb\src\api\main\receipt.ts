﻿import request from '/@/utils/request';
enum Api {
  AddReceipt = '/api/receipt/add',
  DeleteReceipt = '/api/receipt/delete',
  UpdateReceipt = '/api/receipt/update',
  PageReceipt = '/api/receipt/page',
  Submit = 'api/receipt/submit', 
  Retract = 'api/receipt/retract',
  Suspend = '/api/receipt/Suspend',
  InvoiceOpen = '/api/receipt/InvoiceOpen',
}

// 增加收款单
export const addReceipt = (params?: any) =>
	request({
		url: Api.AddReceipt,
		method: 'post',
		data: params,
	});

// 删除收款单
export const deleteReceipt = (params?: any) => 
	request({
			url: Api.DeleteReceipt,
			method: 'post',
			data: params,
		});

// 编辑收款单
export const updateReceipt = (params?: any) => 
	request({
			url: Api.UpdateReceipt,
			method: 'post',
			data: params,
		});

// 分页查询收款单
export const pageReceipt = (params?: any) => 
	request({
			url: Api.PageReceipt,
			method: 'post',
			data: params,
		});

// 提交
export const Submit = (params?: any) => 
    request({
		url: Api.Submit,
		method: 'post',
		data: params
    });
// 撤回
export const Retract = (params?: any) => 
    request({
		url: Api.Retract,
		method: 'post',
		data: params
    });
// 中止
export const Suspend = (params?: any) =>
	request({
		url: Api.Suspend,
		method: 'post',
		data: params,
	});
// 收款单开票
export const InvoiceOpen = (params?: any) => 
	request({
			url: Api.InvoiceOpen,
			method: 'post',
			data: params,
		});
