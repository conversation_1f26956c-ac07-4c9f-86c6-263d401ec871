﻿<template>
	<div class="warehouseInrecordMX-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="700" draggable=""  :close-on-click-modal="false">
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="商品">
							<el-select clearable filterable v-model="ruleForm.goodsId" placeholder=""
							@change="getGoodsDetail(ruleForm, ruleForm.goodsId)">
								<el-option v-for="(item, index) in warehousegoodsDropdownList" :key="index"
									:value="item.value" :label="item.label" />

							</el-select>

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="商品条码" prop="barcode">
							<el-input v-model="ruleForm.barcode" disabled />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="商品编码" prop="productCode">
							<el-input v-model="ruleForm.productCode" disabled />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="品牌" prop="brandName">
							<el-input v-model="ruleForm.brandName" disabled />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="规格" prop="specsName">
							<el-input v-model="ruleForm.specsName" disabled />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="品级">
							<el-select clearable v-model="ruleForm.rating" placeholder="">
								<el-option v-for="(item, index) in  counterStore.ratingList" :key="index" :value="item.value"
									:label="item.label"></el-option>

							</el-select>

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="采购数量" prop="puchQty">
							<el-input-number v-model="ruleForm.puchQty" placeholder="" disabled="" />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="入库数量" prop="rcvQty">
							<el-input-number v-model="ruleForm.rcvQty" placeholder="" clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="单位">
							<el-select clearable disabled="" v-model="ruleForm.unit" placeholder="">  
								<el-option v-for="(item, index) in  WarehouseUnit" :key="index" :value="item.value"
									:label="item.label"></el-option>

							</el-select>

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="辅助单位">
							<el-select clearable disabled="" v-model="ruleForm.AuxiliaryUnit" placeholder="">  
								<el-option v-for="(item, index) in  WarehouseUnit" :key="index" :value="item.value"
									:label="item.label"></el-option>

							</el-select>

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="转换数量">
							<el-input-number v-model="ruleForm.Conuantity" placeholder=""  />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="生产日期" prop="productDate">
							<el-date-picker v-model="ruleForm.productDate" type="date" placeholder=""
								@change="changeDate(ruleForm)" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="保质期">
							<el-input-number v-model="ruleForm.shelflife" placeholder="" clearable
								@change="changeDate(ruleForm)" />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="保质期单位">
							<el-select clearable v-model="ruleForm.shelflifeUnit" placeholder=""
								@change="changeDate(ruleForm)">
								<el-option v-for="(item, index) in  counterStore.ShelflifeUnitList" :key="index"
									:value="item.value" :label="item.label"></el-option>

							</el-select>

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="过期时间">
							<el-date-picker v-model="ruleForm.expires" type="date" placeholder="" disabled />

						</el-form-item>

					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="供应商">
							<el-select clearable filterable v-model="ruleForm.supplierId" placeholder="">
								<el-option v-for="(item, index) in pubSupplierDropdownList" :key="index" :value="item.value"
									:label="item.label" />

							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { useGoodsStore } from '/@/stores/goods'
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { addWarehouseInrecordMX, updateWarehouseInrecordMX } from "/@/api/main/warehouseInrecordMX";
// import { getWarehousegoodsDropdown } from '/@/api/main/warehouseInrecordMX';
// import { getPubSupplierDropdown } from '/@/api/main/warehouseInrecordMX';
import {PubSupplierDropdown } from '/@/api/main/pubSupplier';
import { expirationDate } from "/@/utils/formatTime";
import { getDictDataList } from '/@/api/system/admin';
import { WarehouseGoodsUnit } from '/@/api/main/warehouseInrecord';
import { WarehousegoodsDropdown } from '/@/api/main/warehousegoods';
const getEditunitData = ref<any>([]);
const getEditshelflifeUnitData = ref<any>([]);

const counterStore = useGoodsStore()
const ratingList = ref([
	{ label: '良品', value: 1 },
	{ label: '次品', value: 0 },
]);


const getGoodsDetail = (row: any, val: any) => {
	debugger;
	let obj = counterStore.goodsList.find((v: { value: any; }) => {
		return v.value == val
	})
	row.unit = obj?.unit || ""
	row.auxiliaryunit = obj?.auxiliaryunit || ""
	row.tradename = obj?.label || ""
	row.brand = obj?.brand
	row.barcode = obj?.id
	row.productcode = obj?.code
	row.specifications = obj?.specs
}

//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: "",
	},
});
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const loading = ref(false);
const ruleForm = ref<any>({});

const WarehouseUnit = ref<any>
        ({});
		
const  WareUnit=async () => {
          debugger;
          var res = await WarehouseGoodsUnit({});
          WarehouseUnit.value=res.data.result;
        }
		WareUnit();
// 自动计算过期时间
const changeDate = (row: any) => {
	if (row.productDate && row.shelflife && row.shelflifeUnit) {
		row.expires = expirationDate(row.productDate, row.shelflife, row.shelflifeUnit)

	}

}



// 打开弹窗
const openDialog = (row: any) => {
	ruleForm.value.InrecordId = JSON.parse(JSON.stringify(row)).inrecordId;
	ruleForm.value = JSON.parse(JSON.stringify(row));
	isShowDialog.value = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	isShowDialog.value = false;
	setTimeout(() => {
		loading.value = false;
	},500)
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			loading.value = true;
			let values = ruleForm.value;
			if (ruleForm.value.id != undefined && ruleForm.value.id > 0) {
				await updateWarehouseInrecordMX(values);
			} else {
				await addWarehouseInrecordMX(values);
			}
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};


const warehousegoodsDropdownList = ref<any>([]);

const pubSupplierDropdownList = ref<any>([]);
const getPubSupplierDropdownList = async () => {
	let list = await PubSupplierDropdown();
	pubSupplierDropdownList.value = list.data.result ?? [];
};
getPubSupplierDropdownList();

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>




