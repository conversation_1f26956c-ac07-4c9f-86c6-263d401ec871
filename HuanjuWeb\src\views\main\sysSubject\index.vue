﻿<template>
  <div class="subject-container">
    <el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="科目编码">
          <el-input v-model="queryParams.subjectCode" clearable="" placeholder="请输入科目编码" />

        </el-form-item>
        <el-form-item label="科目名称">
          <el-input v-model="queryParams.subjectName" clearable="" placeholder="请输入科目名称" />

        </el-form-item>
        <el-form-item label="科目类型">
          <el-input v-model="queryParams.subjectType" clearable="" placeholder="请输入科目类型" />

        </el-form-item>
        <el-form-item label="余额方向">
          <el-input v-model="queryParams.subjectDerict" clearable="" placeholder="请输入余额方向" />

        </el-form-item>
        <el-form-item>
          <el-button-group>
            <el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'subject:page'"> 查询
            </el-button>
            <el-button icon="ele-Refresh" @click="() => queryParams = {}"> 重置 </el-button>
            
          </el-button-group>

        </el-form-item>
        <!-- <el-form-item>
          <el-button type="primary" icon="ele-Plus" @click="openAddSubject" v-auth="'subject:add'"> 新增
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="ele-Upload" v-auth="'subject:import'" @Click="handleImport"> 导入
          </el-button>
          <input type="file" ref="fileInput" style="display: none;" @change="onFileSelected" accept=".xls,.xlsx">
        </el-form-item> -->

      </el-form>
    </el-card>
    <el-card class="full-table" shadow="hover" style="margin-top: 8px">
      <el-table :data="tableData" style="width: 100%" v-loading="loading" tooltip-effect="light" row-key="id" border="">
        <el-table-column type="index" label="序号" width="55" align="center" fixed="" />
        <el-table-column prop="subjectCode" label="科目编码" fixed="" show-overflow-tooltip="" />
        <el-table-column prop="subjectName" label="科目名称" fixed="" show-overflow-tooltip="" />
        <el-table-column prop="subjectType" label="科目类型" fixed="" show-overflow-tooltip="" />
        <el-table-column prop="subjectDerict" label="余额方向" fixed="" show-overflow-tooltip="" />

        <el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip=""
          v-if="auth('subject:edit') || auth('subject:delete')">
          <template #default="scope">
            <el-button icon="ele-Edit" size="small" text="" type="primary" @click="openEditSubject(scope.row)"
              v-auth="'subject:edit'"> 编辑 </el-button>
            <el-button icon="ele-Delete" size="small" text="" type="primary" @click="delSubject(scope.row)"
              v-auth="'subject:delete'"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-model:currentPage="tableParams.page" v-model:page-size="tableParams.pageSize"
        :total="tableParams.total" :page-sizes="[10, 20, 50, 100]" small="" background=""
        @size-change="handleSizeChange" @current-change="handleCurrentChange"
        layout="total, sizes, prev, pager, next, jumper" />
      <editDialog ref="editDialogRef" :title="editSubjectTitle" @reloadTable="handleQuery" />
    </el-card>
  </div>
</template>

<script lang="ts" setup="" name="subject">
import { ref } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { auth } from '/@/utils/authFunction';
//import { formatDate } from '/@/utils/formatTime';

import editDialog from '/@/views/main/sysSubject/component/editDialog.vue'
import { pageSubject, deleteSubject, ImportSubject } from '/@/api/main/sysSubject';


const editDialogRef = ref();
const loading = ref(false);
const tableData = ref<any>
  ([]);
const queryParams = ref<any>
  ({});
const tableParams = ref({
  page: 1,
  pageSize: 10,
  total: 0,
});
const editSubjectTitle = ref("");

//导入操作
const fileInput = ref<HTMLInputElement | null>(null);
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB limit

const handleImport = () => {
  fileInput.value?.click();
};

const onFileSelected = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0];
  if (!file) return;

  if (file.size > MAX_FILE_SIZE) {
    ElMessage.error(`文件大小不能超过 ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
    if (fileInput.value) {
      fileInput.value.value = ''; // Reset file input
    }
    return;
  }

  const formData = new FormData();
  formData.append('file', file);

  try {
    loading.value = true;
    ElMessage.info("正在导入数据，请稍候...");
    await ImportSubject(formData);
    ElMessage.success("导入成功");
    handleQuery();
  } catch (error) {
    //ElMessage.error("导入失败，请检查文件格式是否正确");
  } finally {
    loading.value = false;
    if (fileInput.value) {
      fileInput.value.value = ''; // Reset file input
    }
  }
};
// 查询操作
const handleQuery = async () => {
  loading.value = true;
  var res = await pageSubject(Object.assign(queryParams.value, tableParams.value));
  tableData.value = res.data.result?.items ?? [];
  tableParams.value.total = res.data.result?.total;
  loading.value = false;
};

// 打开新增页面
const openAddSubject = () => {
  editSubjectTitle.value = '添加科目';
  editDialogRef.value.openDialog({});
};

// 打开编辑页面
const openEditSubject = (row: any) => {
  editSubjectTitle.value = '编辑科目';
  editDialogRef.value.openDialog(row);
};

// 删除
const delSubject = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      await deleteSubject(row);
      handleQuery();
      ElMessage.success("删除成功");
    })
    .catch(() => { });
};

// 改变页面容量
const handleSizeChange = (val: number) => {
  tableParams.value.pageSize = val;
  handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
  tableParams.value.page = val;
  handleQuery();
};


handleQuery();
</script>
