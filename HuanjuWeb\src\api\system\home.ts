import request from '/@/utils/request';

// 获取首页数据统计
export const getHomeThreeData = () => {
  return request({
    url: '/api/sysHome/statistics',
    method: 'get'
  });
};

// 获取首页推荐数据
export function getHomeRecommendData() {
  return request({
    url: '/api/sysHome/recommend',
    method: 'get'
  });
}

// 获取销售趋势数据
export const getSalesTrend = (params?: {
  type?: string;  // amount/quantity
  timeRange?: string; // month/year
}) => {
  return request({
    url: '/api/sysHome/salesTrend',
    method: 'get',
    params: {
      type: params?.type || 'amount',
      timeRange: params?.timeRange || 'month'
    },
    headers: {
      'accept': 'text/plain'
    }
  });
};
