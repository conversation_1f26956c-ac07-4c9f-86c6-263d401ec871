﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Http;

namespace Admin.NET.Application;
/// <summary>
/// 商品服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class EshopGoodsService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<EshopGoods> _rep;
    public EshopGoodsService(SqlSugarRepository<EshopGoods> rep)
    {
        _rep = rep;
    }

    /// <summary>
    /// 分页查询商品
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<EshopGoodsOutput>> Page(EshopGoodsInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(x => x.IsDelete == false)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Title), u => u.Title.Contains(input.Title.Trim()))

                    .Select<EshopGoodsOutput>()
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加商品
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddEshopGoodsInput input)
    {
        var entity = input.Adapt<EshopGoods>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除商品
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteEshopGoodsInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新商品
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateEshopGoodsInput input)
    {
        var entity = input.Adapt<EshopGoods>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 上传文件
    /// </summary>
    [HttpPost]
    [ApiDescriptionSettings(Name = "UploadFile")]
    public async Task UploadFile(IFormFileCollection files)
    {
        await FileUtil.UploadPicToQiniu(files, "img", "");
    }

    /// <summary>
    /// 获取商品
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<EshopGoods> Get([FromQuery] QueryByIdEshopGoodsInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取商品列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<EshopGoodsOutput>> List([FromQuery] EshopGoodsInput input)
    {
        return await _rep.AsQueryable().Select<EshopGoodsOutput>().ToListAsync();
    }





}

