﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 开票记录基础输入参数
    /// </summary>
    public class InvoicingRecordBaseInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public virtual long Id { get; set; }
        
        /// <summary>
        /// 单据号
        /// </summary>
        public virtual string DocumentNumber { get; set; }
        
        /// <summary>
        /// 收款单ID
        /// </summary>
        public virtual long ReceiptID { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public virtual int? Status { get; set; }
        
    }

    /// <summary>
    /// 开票记录分页查询输入参数
    /// </summary>
    public class InvoicingRecordInput : BasePageInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 单据号
        /// </summary>
        public string DocumentNumber { get; set; }
        
        /// <summary>
        /// 收款单ID
        /// </summary>
        public long ReceiptID { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }
        
    }

    /// <summary>
    /// 开票记录增加输入参数
    /// </summary>
    public class AddInvoicingRecordInput : InvoicingRecordBaseInput
    {
    }

    /// <summary>
    /// 开票记录删除输入参数
    /// </summary>
    public class DeleteInvoicingRecordInput : BaseIdInput
    {
    }

    /// <summary>
    /// 开票记录更新输入参数
    /// </summary>
    public class UpdateInvoicingRecordInput : InvoicingRecordBaseInput
    {
    }

    /// <summary>
    /// 开票记录主键查询输入参数
    /// </summary>
    public class QueryByIdInvoicingRecordInput : DeleteInvoicingRecordInput
    {

    }
