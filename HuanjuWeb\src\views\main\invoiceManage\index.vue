﻿<template>
  <div class="invoiceManage-container">
    <el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <!-- <el-form-item label="时间">
          <el-date-picker placeholder="请选择时间" value-format="YYYY/MM/DD" type="daterange" v-model="queryParams.timeRange" />
          
        </el-form-item> -->
        <el-form-item label="发票号码">
          <el-input v-model="queryParams.invoiceNum" clearable="" placeholder="请输入发票号码"/>
        </el-form-item>
        <el-form-item label="来往单位">
          <el-input v-model="queryParams.comeUnit" clearable="" placeholder="请输入来往单位"/>
        </el-form-item>
        <el-form-item label="发票性质">
          <el-select clearable v-model="queryParams.natureInvoice" placeholder="请选择发票性质">
							<el-option v-for=" (item, index) in natureInvoice" :key="index"
									:value="item.value" :label="item.label" />
						</el-select>
        </el-form-item>
        <el-form-item label="发票种类">
            <el-select clearable v-model="queryParams.invoiceType" placeholder="请选择发票种类">
							<el-option v-for=" (item, index) in invoiceTypeList" :key="index"
										:value="item.value" :label="item.label" />
						</el-select>
        </el-form-item>
        <el-form-item label="发票类型">
            <el-select clearable v-model="queryParams.invoiceTypeLx" placeholder="请选择发票类型">
							<el-option v-for=" (item, index) in invoiceTypeLxList" :key="index"
										:value="item.value" :label="item.label" />
						</el-select>
        </el-form-item>
        <!-- <el-form-item label="收款单号">
          <el-input v-model="queryParams.receiptNum" clearable="" placeholder="请输入收款单号"/>
        </el-form-item> -->
        <div style="margin-top: -10px;margin-bottom: 10px;">
          <el-button-group>
            <el-button type="primary"  icon="ele-Search" @click="handleQuery" v-auth="'invoiceManage:page'"> 查询 </el-button>
            <el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
          </el-button-group>
          <!-- <el-button type="primary" icon="ele-Plus" style="margin-left: 20px;"  @click="openAddInvoiceManage" v-auth="'invoiceManage:add'"> 新增 </el-button>    -->
          <el-button type="primary" icon="ele-Refresh" @click="openSynchronous" > 同步 </el-button>    
          <el-button type="primary" icon="ele-Download" @click="download"> 下载 </el-button>    
          <el-button type="primary" icon="ele-Printer"> 打印 </el-button>
          <el-button type="primary" icon="ele-Connection"> 关联 </el-button>  
          <!-- <el-button type="primary" icon="ele-Sell" @click="infoMaintenance"> 信息维护 </el-button>   -->
        </div>
        <!-- <el-form-item>

          
        </el-form-item>
        <el-form-item>
          
        </el-form-item>
        <el-form-item>
          
        </el-form-item>
        <el-form-item>
                
        </el-form-item>
        <el-form-item>
         
        </el-form-item>
        <el-form-item>
                  
        </el-form-item> -->
        
      </el-form>
    </el-card>
    <el-card class="full-table" shadow="hover" style="margin-top: 8px">
      <el-table
				:data="tableData"
				style="width: 100%"
				v-loading="loading"
				tooltip-effect="light"
				row-key="id"
				border
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center"> </el-table-column>
        <el-table-column type="index" label="序号" width="55" align="center"/>
         <!-- <el-table-column prop="time" label="时间" fixed="" show-overflow-tooltip="" /> -->
         <el-table-column prop="natureInvoice" label="发票性质" show-overflow-tooltip="">
          <template #default="scope">
               <el-tag v-if="scope.row.natureInvoice == 0">销项发票</el-tag>
               <el-tag type="danger" v-else>进项发票</el-tag>
            </template>
         </el-table-column>
         <el-table-column prop="invoiceDate" label="开票时间" show-overflow-tooltip="" />
         <el-table-column prop="contacts" label="开票人" show-overflow-tooltip="" />
         <el-table-column prop="comeUnit" label="来往单位" show-overflow-tooltip="" />
         <el-table-column prop="invoiceNum" label="发票号码" show-overflow-tooltip="" />
         <el-table-column prop="untaxedMoney" label="未税金额" show-overflow-tooltip="" />  
         <el-table-column prop="taxRate" label="税率" show-overflow-tooltip="" />
         <el-table-column prop="taxAmount" label="税额" show-overflow-tooltip="" />
         <el-table-column prop="invoiceValue" label="价税合计" show-overflow-tooltip="" />
         <el-table-column prop="invoiceType" label="发票种类" show-overflow-tooltip="" >
            <template #default="scope">
               <el-tag v-if="scope.row.invoiceType == 0">蓝票</el-tag>
               <el-tag type="danger" v-else>红票</el-tag>
            </template>
         </el-table-column>
         <el-table-column prop="invoiceTypeLx" label="发票类型" show-overflow-tooltip="">
            <template #default="scope">
               <el-tag v-if="scope.row.invoiceTypeLx == 0">专票</el-tag>
               <el-tag type="danger" v-else>普票</el-tag>
            </template>
         </el-table-column>
         <el-table-column prop="invoiceAddress" label="下载链接" show-overflow-tooltip="" width="180">
            <template #default="scope">
              <a :href="scope.row.invoiceAddress">{{ scope.row.invoiceAddress }}</a>
            </template>
         </el-table-column>
         <el-table-column prop="receiptNum" label="收付款单号" show-overflow-tooltip="" />
         <el-table-column prop="invoiceStatus" label="报销" show-overflow-tooltip="">
            <template #default="scope">
                <el-switch v-model="scope.row.invoiceStatus"/>
						</template>
				 </el-table-column>
         <el-table-column prop="notes" label="备注" show-overflow-tooltip="" />
        <!-- <el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip="" v-if="auth('invoiceManage:edit') || auth('invoiceManage:delete')">
          <template #default="scope">
            <el-button icon="ele-Edit" size="small" text="" type="primary" @click="openEditInvoiceManage(scope.row)" v-auth="'invoiceManage:edit'"> 编辑 </el-button>
            <el-button icon="ele-Delete" size="small" text="" type="primary" @click="delInvoiceManage(scope.row)" v-auth="'invoiceManage:delete'"> 删除 </el-button>
          </template>
        </el-table-column> -->
      </el-table>
      <el-pagination
				v-model:currentPage="tableParams.page"
				v-model:page-size="tableParams.pageSize"
				:total="tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				small=""
				background=""
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
	/>
      <editDialog
        ref="editDialogRef"
        :title="editInvoiceManageTitle"
        @reloadTable="handleQuery"
      />
      <synchronous
        ref="synchronousRef"
        :title="synchronousInvoiceTitle"
        @reloadTable="handleQuery"
      />
      <!-- <InfoMaintenance
			    ref="infoMaintenanceRef"
			    :title="infoMaintenanceTitle"
			    @reloadTable="handleQuery"
      /> -->
    </el-card>
  </div>
</template>

<script lang="ts" setup="" name="invoiceManage">
  import { ref } from "vue";
  import { ElMessageBox, ElMessage } from "element-plus";
  import { storeToRefs } from 'pinia';
  import { auth } from '/@/utils/authFunction';
  //import { formatDate } from '/@/utils/formatTime';

  import editDialog from '/@/views/main/invoiceManage/component/editDialog.vue';
  import synchronous from '/@/views/main/invoiceManage/component/synchronous.vue'; 
  // import InfoMaintenance from '/@/views/main/invoiceManage/component/infoMaintenance.vue';   
  import { pageInvoiceManage, deleteInvoiceManage, downloadInvoiceManage } from '/@/api/main/invoiceManage';
  import { useUserInfo } from '/@/stores/userInfo';
  const stores = useUserInfo();
  const { userInfos } = storeToRefs(stores);
    const editDialogRef = ref();
    const synchronousRef = ref();
    // const infoMaintenanceRef = ref();
    const loading = ref(false);
    const tableData = ref<any>
      ([]);
      const queryParams = ref<any>
        ({});
        const tableParams = ref({
        page: 1,
        pageSize: 20,
        total: 0,
        });
  const editInvoiceManageTitle = ref("");
  const synchronousInvoiceTitle  = ref("");
  // const infoMaintenanceTitle = ref("");
  const natureInvoice = [
   {
	label: '销项发票',
	value: 0
   },
   {
	label: '进项发票',
	value: 1
   }
]; //发票性质
  const invoiceTypeList = [
	  {
     label:'蓝票',
	   value: 0
	  },
	  {
	   label:'红票',
	   value: 1
	 }
  ]; //发票种类
  const invoiceTypeLxList = [
	 {
    label:'普票',
	  value: 0
	 },
	 {
	  label:'专票',
	  value: 1
	 }
  ]; //发票类型
  const listFPXX: never[] = ([]);
  // 下载发票
  const handleSelectionChange = (val) => {
    listFPXX.value = [];
    val.forEach(item => {
      listFPXX.value.push({
        fphm: item.invoiceNum,
        natureInvoice: item.natureInvoice,
        downflag: item.invoiceAddress,
        nsrsbh: userInfos.nsrsbh,
        username: userInfos.dpUserName,
        kprq: item.invoiceDate
      })
    });
    //    console.log(invoiceNum.value);
  };
  const download = async (params:type) => {
    const reqData = ref({
      listFPXX: listFPXX.value
    })
    // invoiceNum.value = invoiceNum.value.slice(0, -1);
     var res = await downloadInvoiceManage(reqData.value);
      ElMessage({
				message: res.data.result,
				type: "warning",
			});
      handleQuery();
  };
    // 查询操作
  const handleQuery = async () => {
    loading.value = true;
    var res = await pageInvoiceManage(Object.assign(queryParams.value, tableParams.value));
    tableData.value = res.data.result?.items ?? [];
    if( tableData.value.length > 0 ){
      tableData.value.forEach(item => {
        item.taxRate = item.taxRate + '%';
      })
    }
    tableParams.value.total = res.data.result?.total;
    loading.value = false;
  };
    // 重置查询条件
    const resetQuery = () => {
      queryParams.value = {};
      handleQuery();
    };
    // 打开新增页面
    const openAddInvoiceManage = () => {
      editInvoiceManageTitle.value = '添加发票管理';
      editDialogRef.value.openDialog({});
    };

    // 打开编辑页面
    const openEditInvoiceManage = (row: any) => {
      console.log(row);
      editInvoiceManageTitle.value = '编辑发票管理';
      editDialogRef.value.openDialog(row);
    };
    // 打开发票同步页面
    const openSynchronous = () => {
      synchronousInvoiceTitle.value = '发票同步'
      synchronousRef.value.openDialog({});
    };
    // 删除
    const delInvoiceManage = (row: any) => {
      ElMessageBox.confirm(`确定要删除吗?`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      })
      .then(async () => {
        await deleteInvoiceManage(row);
        handleQuery();
        ElMessage.success("删除成功");
      })
      .catch(() => {});
    };

    // 改变页面容量
    const handleSizeChange = (val: number) => {
    tableParams.value.pageSize = val;
    handleQuery();
    };

    // 改变页码序号
    const handleCurrentChange = (val: number) => {
    tableParams.value.page = val;
    handleQuery();
    };
    //  信息维护
    // const infoMaintenance = () => {
    //   infoMaintenanceTitle.value = '信息维护';
    //   infoMaintenanceRef.value.openDialog({});
    // };

handleQuery();
</script>
<style lang="scss" scoped>
.el-form--inline .el-form-item{
  margin-right: 15px;
}
.el-select{
  width: 158px;
}
</style>


