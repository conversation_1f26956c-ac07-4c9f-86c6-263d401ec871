﻿<template>
	<div class="warehousePurchaseMX-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="700" draggable=""  :close-on-click-modal="false">
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="ruleForm.id" />
					</el-form-item>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="商品" prop="goodsId">
							<el-select clearable filterable v-model="ruleForm.goodsId" placeholder="请选择商品">
								<el-option v-for="(item, index) in warehousegoodsDropdownList" :key="index"
									:value="item.value" :label="item.label" />
							</el-select>
						</el-form-item>

					</el-col>
					
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="单位" prop="unit">
							<!-- <el-input v-model="ruleForm.unit" placeholder="请输入单位" clearable /> -->
							<el-select v-model="ruleForm.unit" placeholder="请选择单位" clearable >
								<el-option label="个" :value="'个'" />
								<el-option label="件" :value="'件'" />
								<el-option label="箱" :value="'箱'" />
								<el-option label="袋" :value="'袋'" />
								<el-option label="瓶" :value="'瓶'" />
								<el-option label="包" :value="'包'" />
								<el-option label="盒" :value="'盒'" />
								<el-option label="条" :value="'条'" />
							</el-select>
						</el-form-item>

					</el-col>
					
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="单价" prop="puchPrice">
							<el-input v-model="ruleForm.puchPrice" placeholder="请输入采购单价" clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="数量" prop="puchQty">
							<el-input-number v-model="ruleForm.puchQty" placeholder="请输入采购数量" clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="采购金额" prop="puchAmt">
							<el-input v-model="ruleForm.puchAmt" placeholder="请输入采购金额" clearable />

						</el-form-item>

					</el-col>
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" v-if="ruleForm.id">
						<el-form-item label="入库数量" prop="rcvQty">
							<el-input-number v-model="ruleForm.rcvQty" placeholder="请输入入库数量" clearable />

						</el-form-item>

					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="供应商" prop="supplierId">
							<el-select clearable filterable v-model="ruleForm.supplierId" placeholder="请选择供应商">
								<el-option v-for="(item, index) in pubSupplierDropdownList" :key="index" :value="item.value"
									:label="item.label" />

							</el-select>

						</el-form-item>

					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { addWarehousePurchaseMX, updateWarehousePurchaseMX } from "/@/api/main/warehousePurchaseMX";
// import { getWarehousegoodsDropdown } from '/@/api/main/warehousePurchaseMX';
import { WarehousegoodsDropdown } from '/@/api/main/warehousegoods';
// import { getPubSupplierDropdown } from '/@/api/main/warehousePurchaseMX';
import {PubSupplierDropdown } from '/@/api/main/pubSupplier';
import { useGoodsStore } from '/@/stores/goods';

//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: "",
	},
});
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const loading = ref(false);
const ruleForm = ref<any>({});
//自行添加其他规则
const rules = ref<FormRules>({});

const goodsStore = useGoodsStore();

watch(
	[() => ruleForm.value.puchQty, () => ruleForm.value.puchPrice],
	() => {
		if (isNaN(ruleForm.value.puchQty) || isNaN(ruleForm.value.puchPrice))
			ruleForm.value.puchAmt = 0;
		else
			ruleForm.value.puchAmt = ruleForm.value.puchQty * ruleForm.value.puchPrice;
	}
);

// 打开弹窗
const openDialog = (row: any) => {
	ruleForm.value = JSON.parse(JSON.stringify(row));
	isShowDialog.value = true;
};

const openDialogAdd = (purchaseId: any) => {
	ruleForm.value = {};
	ruleForm.value.purchaseId = purchaseId;
	isShowDialog.value = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	isShowDialog.value = false;
	setTimeout(() => {
		loading.value = false;
	},500)
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			loading.value = true;
			let values = ruleForm.value;
			if (ruleForm.value.id != undefined && ruleForm.value.id > 0) {
				await updateWarehousePurchaseMX(values);
			} else {
				await addWarehousePurchaseMX(values);
			}
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};


const warehousegoodsDropdownList = ref<any>([]);
	const getWarehousegoodsDropdownList = async () => {
	await goodsStore.fetchGoodsList();
	warehousegoodsDropdownList.value = goodsStore.dropdownList;
};
// getWarehousegoodsDropdownList();

const pubSupplierDropdownList = ref<any>([]);
const getPubSupplierDropdownList = async () => {
	let list = await PubSupplierDropdown();
	pubSupplierDropdownList.value = list.data.result ?? [];
};
getPubSupplierDropdownList();




// 页面加载时
onMounted(async () => {
});

//将属性或者函数暴露给父组件
defineExpose({ openDialog, openDialogAdd });
</script>




