﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 流程明细表
    /// </summary>
    [SugarTable("workflowreviewmx","流程明细表")]
    [Tenant("1300000000001")]
    public class WorkflowReviewMX  : EntityTenant
    {
        /// <summary>
        /// 流程ID
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "流程ID")]
        public long WorkflowId { get; set; }
        /// <summary>
        /// 节点类型
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "节点类型")]
        public int NodeType { get; set; }
        /// <summary>
        /// 职位ID
        /// </summary>
        [SugarColumn(ColumnDescription = "职位ID")]
        public long? PositionId { get; set; }
        /// <summary>
        /// 职位
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(PositionId))]
        public SysPos SysPos { get; set; }
        /// <summary>
        /// 用户ID
        /// </summary>
        [SugarColumn(ColumnDescription = "用户ID")]
        public long? UserID { get; set; }
        /// <summary>
        /// 用户
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(UserID))]
        public SysUser SysUser { get; set; }
        /// <summary>
        /// 步骤编号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "步骤编号")]
        public int StepNumber { get; set; }
    }
}