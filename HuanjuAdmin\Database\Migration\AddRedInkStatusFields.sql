-- 添加红冲状态字段的数据库迁移脚本
-- 执行日期：2024年

-- 1. 为出库记录表添加红冲状态字段
ALTER TABLE `outboundrecord` 
ADD COLUMN `RedInkStatus` int NOT NULL DEFAULT 0 COMMENT '红冲状态：0-正常记录，1-已被红冲，2-红冲记录',
ADD COLUMN `OriginalRecordId` bigint NULL DEFAULT NULL COMMENT '原始记录ID（红冲记录关联的原始记录）';

-- 2. 为入库记录表添加红冲状态字段
ALTER TABLE `inboundrecord` 
ADD COLUMN `RedInkStatus` int NOT NULL DEFAULT 0 COMMENT '红冲状态：0-正常记录，1-已被红冲，2-红冲记录',
ADD COLUMN `OriginalRecordId` bigint NULL DEFAULT NULL COMMENT '原始记录ID（红冲记录关联的原始记录）';

-- 3. 更新现有数据：根据流水号和数量判断红冲状态
-- 更新出库记录的红冲状态
UPDATE `outboundrecord` 
SET `RedInkStatus` = 2 
WHERE (`OrderNum` LIKE '%-红冲' OR `OutBoundCount` < 0) AND `RedInkStatus` = 0;

-- 更新入库记录的红冲状态
UPDATE `inboundrecord` 
SET `RedInkStatus` = 2 
WHERE (`OrderNum` LIKE '%-红冲' OR `InBoundCount` < 0) AND `RedInkStatus` = 0;

-- 4. 标记被红冲的原始记录
-- 为已经存在的红冲记录设置关联关系（通过解析红冲记录的备注）
UPDATE `outboundrecord` o1
JOIN `outboundrecord` o2 ON o2.Remark LIKE CONCAT('%原记录ID: ', o1.Id, '%')
SET o1.RedInkStatus = 1
WHERE o2.RedInkStatus = 2 AND o1.RedInkStatus = 0;

UPDATE `inboundrecord` i1
JOIN `inboundrecord` i2 ON i2.Remark LIKE CONCAT('%原记录ID: ', i1.Id, '%')
SET i1.RedInkStatus = 1
WHERE i2.RedInkStatus = 2 AND i1.RedInkStatus = 0;

-- 5. 删除现有的出入库记录视图
DROP VIEW IF EXISTS `View_OutInBound`;

-- 6. 重新创建包含红冲状态字段的视图
CREATE VIEW `View_OutInBound` AS
SELECT
    -- 出库记录
    o.Id,
    '0'                          as Type, -- 0表示出库
    o.Id                         as ParentId,
    o.OrderNum,
    o.OutBoundCount              as OutInCount,
    o.PrintCount,
    o.WarehouseBatchId,
    o.TenantId,
    o.CreateUserId,
    o.CreateTime,
    o.IsDelete,
    o.UpdateTime,
    COALESCE(wb.Batchnumber, '') as BatchNumber,
    COALESCE(wo.OutOrder, '')    as OrderNumber,
    wo.CustomId,
    NULL                         as SupplierId,
    COALESCE(pc.Name, '')        as Company,
    COALESCE(pc.Phone, '')       as TelPhone,
    wh.Id                        as WarehouseId,
    COALESCE(wh.Name, '')        as WarehouseName,
    wg.Id                        as GoodsId,
    COALESCE(wg.Name, '')        as GoodsName,
    COALESCE(wg.barcode, '')     as BarCode,
    COALESCE(wg.Code, '')        as Code,
    COALESCE(wg.Brand, '')       as Brand,
    COALESCE(wg.Specs, '')       as Specs,
    wu.Id                        as UnitId,
    COALESCE(wu.Name, '')        as UnitName,
    COALESCE(cu.RealName, '')    as CreateUserName,
    COALESCE(uu.RealName, '')    as UpdateUserName,
    o.GoodProduct,                        -- 良品次品字段
    o.RedInkStatus                        -- 红冲状态字段
FROM outboundrecord o
         LEFT JOIN warehouseoutmx omx ON o.WarehouseOutMxId = omx.Id
         LEFT JOIN warehouseout wo ON omx.OutId = wo.Id
         LEFT JOIN warehousebatch wb ON o.WarehouseBatchId = wb.Id
         LEFT JOIN pubcustom pc ON wo.CustomId = pc.Id
         LEFT JOIN warehouse wh ON wo.WarehouseId = wh.Id
         LEFT JOIN warehousegoods wg ON omx.goodsId = wg.Id
         LEFT JOIN warehousegoodsunit wu ON omx.Unit = wu.Id
         LEFT JOIN sysuser cu ON o.CreateUserId = cu.Id
         LEFT JOIN sysuser uu ON o.UpdateUserId = uu.Id
WHERE o.IsDelete = 0

UNION ALL

SELECT
    -- 入库记录
    i.Id,
    '1'                          as Type, -- 1表示入库
    i.Id                         as ParentId,
    i.OrderNum,
    i.InBoundCount               as OutInCount,
    i.PrintCount,
    i.WarehouseBatchId,
    i.TenantId,
    i.CreateUserId,
    i.CreateTime,
    i.IsDelete,
    i.UpdateTime,
    COALESCE(wb.Batchnumber, '') as BatchNumber,
    COALESCE(wi.OrderNumber, '') as OrderNumber,
    NULL                         as CustomId,
    wi.SupplierId,
    COALESCE(ps.Name, '')        as Company,
    COALESCE(ps.Phone, '')       as TelPhone,
    wh.Id                        as WarehouseId,
    COALESCE(wh.Name, '')        as WarehouseName,
    wg.Id                        as GoodsId,
    COALESCE(wg.Name, '')        as GoodsName,
    COALESCE(wg.barcode, '')     as BarCode,
    COALESCE(wg.Code, '')        as Code,
    COALESCE(wg.Brand, '')       as Brand,
    COALESCE(wg.Specs, '')       as Specs,
    wu.Id                        as UnitId,
    COALESCE(wu.Name, '')        as UnitName,
    COALESCE(cui.RealName, '')   as CreateUserName,
    COALESCE(uui.RealName, '')   as UpdateUserName,
    i.GoodProduct,                        -- 良品次品字段
    i.RedInkStatus                        -- 红冲状态字段
FROM inboundrecord i
         LEFT JOIN warehouseinrecordmx imx ON i.WarehouseIncordMxId = imx.Id
         LEFT JOIN warehouseinrecord wi ON imx.InrecordId = wi.Id
         LEFT JOIN warehousebatch wb ON i.WarehouseBatchId = wb.Id
         LEFT JOIN pubsupplier ps ON wi.SupplierId = ps.Id
         LEFT JOIN warehouse wh ON wi.Warehouseid = wh.Id
         LEFT JOIN warehousegoods wg ON imx.GoodsId = wg.Id
         LEFT JOIN warehousegoodsunit wu ON imx.Unit = wu.Id
         LEFT JOIN sysuser cui ON i.CreateUserId = cui.Id
         LEFT JOIN sysuser uui ON i.UpdateUserId = uui.Id
WHERE i.IsDelete = 0;

-- 7. 验证数据迁移结果
SELECT 'AddRedInkStatusFields 数据库迁移完成！' as message;
SELECT '出库记录'                                        as table_name,
       COUNT(*)                                          as total_records,
       SUM(CASE WHEN RedInkStatus = 0 THEN 1 ELSE 0 END) as normal_records,
       SUM(CASE WHEN RedInkStatus = 1 THEN 1 ELSE 0 END) as red_ink_original_records,
       SUM(CASE WHEN RedInkStatus = 2 THEN 1 ELSE 0 END) as red_ink_records
FROM outboundrecord
UNION ALL
SELECT '入库记录'                                        as table_name,
       COUNT(*)                                          as total_records,
       SUM(CASE WHEN RedInkStatus = 0 THEN 1 ELSE 0 END) as normal_records,
       SUM(CASE WHEN RedInkStatus = 1 THEN 1 ELSE 0 END) as red_ink_original_records,
       SUM(CASE WHEN RedInkStatus = 2 THEN 1 ELSE 0 END) as red_ink_records
FROM inboundrecord;