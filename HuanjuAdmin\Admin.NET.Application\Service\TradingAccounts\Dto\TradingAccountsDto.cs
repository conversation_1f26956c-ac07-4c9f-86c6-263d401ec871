﻿namespace Admin.NET.Application;

    /// <summary>
    /// 交易账户输出参数
    /// </summary>
    public class TradingAccountsDto
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 账户名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 账户
        /// </summary>
        public string? BankCode { get; set; }
        
        /// <summary>
        /// 银行
        /// </summary>
        public string? BankName { get; set; }
        
        /// <summary>
        /// 开户行
        /// </summary>
        public string? BankAddress { get; set; }
        
        /// <summary>
        /// 账户金额
        /// </summary>
        public decimal? Amount { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public bool Status { get; set; }
        
    }
