export interface OrderTableData {
    id: number;
    schemeNo: string;
    schemeName: string;
    createTime: string;
    updateTime: string;
    status: number;
    materialList: MaterialTableData[];
    produceList: ProduceTableData[];
    materialWarehouseId?: number;
    produceWarehouseId?: number;
}

export interface MaterialTableData {
    id: number;
    materialCode: string;
    materialName: string;
    specification: string;
    unit: string;
    quantity: number;
}

export interface ProduceTableData {
    id: number;
    productCode: string;
    productName: string;
    specification: string;
    unit: string;
    quantity: number;
}