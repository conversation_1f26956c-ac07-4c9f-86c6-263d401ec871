﻿namespace Admin.NET.Application;

    /// <summary>
    /// 个人审批明细输出参数
    /// </summary>
    public class WorkflowRecordDto
    {
        /// <summary>
        /// Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 审批单ID
        /// </summary>
        public long OrderId { get; set; }
        
        /// <summary>
        /// 步骤编号
        /// </summary>
        public int StepNumber { get; set; }
        
        /// <summary>
        /// 审批人
        /// </summary>
        public long Approver { get; set; }
        
        /// <summary>
        /// 审批状态
        /// </summary>
        public int Status { get; set; }
        
        /// <summary>
        /// 审批意见
        /// </summary>
        public string? Remark { get; set; }
        
    }
