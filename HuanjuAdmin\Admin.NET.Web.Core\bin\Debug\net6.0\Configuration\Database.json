{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

  "DbConnection": {
    // 具体配置见SqlSugar官网（第一个为默认库不需要设置ConfigId）
    "ConnectionConfigs": [
      {
        "DbType": "MySql", // MySql、SqlServer、Sqlite、Oracle、PostgreSQL、Dm、Kdbndp、Oscar、MySqlConnector、Access、OpenGauss、QuestDB、HG、ClickHouse、GBase、Odbc、Custom
        "ConnectionString": "server=**************;database=niucodedb;userid=root;password=**********;port=3306;AllowUserVariables=true;Connect Timeout=60;Max Pool Size=100;Min Pool Size=10;Pooling=true;Connection Lifetime=300; ConnectionReset=true; CharSet=utf8mb4;AllowLoadLocalInfile=false;SslMode=Required; DefaultCommandTimeout=30;", // 库连接字符串
        //"ConnectionString": "server=localhost;database=niucodedb;userid=root;password=******;port=3306;AllowUserVariables=true;Connect Timeout=60;Max Pool Size=100;Min Pool Size=10;Pooling=true;Connection Lifetime=300; ConnectionReset=true; CharSet=utf8mb4;AllowLoadLocalInfile=false;SslMode=Required; DefaultCommandTimeout=30;", // 库连接字符串
        "EnableInitDb": false, // 启用库表初始化
        "EnableInitSeed": false, // 启用种子初始化
        "EnableDiffLog": false, // 启用库表差异日志
        "EnableUnderLine": false // 启用驼峰转下划线
      }
      // 其他数据库配置（可以配置多个）
      //{
      //    "ConfigId": "test",
      //    "DbType": "Sqlite",
      //    "ConnectionString": "DataSource=./test.db", // 库连接字符串
      //    "EnableInitDb": true, // 启用库表初始化
      //    "EnableInitSeed": true, // 启用种子初始化
      //    "EnableDiffLog": false, // 启用库表差异日志
      //    "EnableUnderLine": false // 启用驼峰转下划线
      //}
    ]
  }
}