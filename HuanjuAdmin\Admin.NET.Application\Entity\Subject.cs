﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("sysSubject","")]
    [Tenant("1300000000001")]
    public class Subject  : EntityBase
    {
        /// <summary>
        /// 科目编码
        /// </summary>
        [SugarColumn(ColumnDescription = "科目编码", Length = 100)]
        public string? SubjectCode { get; set; }
        /// <summary>
        /// 科目名称
        /// </summary>
        [SugarColumn(ColumnDescription = "科目名称", Length = 255)]
        public string? SubjectName { get; set; }
        /// <summary>
        /// 科目类型
        /// </summary>
        [SugarColumn(ColumnDescription = "科目类型", Length = 255)]
        public string? SubjectType { get; set; }
        /// <summary>
        /// 余额方向
        /// </summary>
        [SugarColumn(ColumnDescription = "余额方向", Length = 255)]
        public string? SubjectDerict { get; set; }
    }
}