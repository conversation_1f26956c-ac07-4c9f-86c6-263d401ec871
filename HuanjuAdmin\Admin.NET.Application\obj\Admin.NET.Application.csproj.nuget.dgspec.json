{"format": 1, "restore": {"D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Application\\Admin.NET.Application.csproj": {}}, "projects": {"D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Application\\Admin.NET.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Application\\Admin.NET.Application.csproj", "projectName": "Admin.NET.Application", "projectPath": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Application\\Admin.NET.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Core\\Admin.NET.Core.csproj": {"projectPath": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Core\\Admin.NET.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.30, 6.0.30]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.30, 6.0.30]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.30, 6.0.30]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100-preview.5.24307.3\\RuntimeIdentifierGraph.json"}}}, "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Core\\Admin.NET.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Core\\Admin.NET.Core.csproj", "projectName": "Admin.NET.Core", "projectPath": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Core\\Admin.NET.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AngleSharp": {"target": "Package", "version": "[1.0.1, )"}, "AspNetCoreRateLimit": {"target": "Package", "version": "[5.0.0, )"}, "AspectCore.Extensions.Reflection": {"target": "Package", "version": "[2.3.0, )"}, "FluentEmail.Smtp": {"target": "Package", "version": "[3.0.2, )"}, "Furion.Extras.Authentication.JwtBearer": {"target": "Package", "version": "[********, )"}, "Furion.Extras.ObjectMapper.Mapster": {"target": "Package", "version": "[********, )"}, "Furion.Pure": {"target": "Package", "version": "[********, )"}, "IPTools.China": {"target": "Package", "version": "[1.6.0, )"}, "Lazy.Captcha.Core": {"target": "Package", "version": "[2.0.3, )"}, "Magicodes.IE.Excel": {"target": "Package", "version": "[*******, )"}, "Magicodes.IE.Pdf": {"target": "Package", "version": "[*******, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[6.0.16, )"}, "NEST": {"target": "Package", "version": "[7.17.5, )"}, "NPOI": {"target": "Package", "version": "[2.4.1, )"}, "NewLife.Redis": {"target": "Package", "version": "[5.3.2023.512, )"}, "OnceMi.AspNetCore.OSS": {"target": "Package", "version": "[1.1.9, )"}, "Qiniu": {"target": "Package", "version": "[8.3.0, )"}, "Quartz": {"target": "Package", "version": "[3.8.0, )"}, "SKIT.FlurlHttpClient.Wechat.Api": {"target": "Package", "version": "[2.27.0, )"}, "SKIT.FlurlHttpClient.Wechat.TenpayV3": {"target": "Package", "version": "[2.18.0, )"}, "SqlSugarCore": {"target": "Package", "version": "[********, )"}, "System.Linq.Dynamic.Core": {"target": "Package", "version": "[1.3.2, )"}, "UAParser": {"target": "Package", "version": "[3.1.47, )"}, "Yitter.IdGenerator": {"target": "Package", "version": "[1.0.14, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.30, 6.0.30]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.30, 6.0.30]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.30, 6.0.30]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100-preview.5.24307.3\\RuntimeIdentifierGraph.json"}}}}}