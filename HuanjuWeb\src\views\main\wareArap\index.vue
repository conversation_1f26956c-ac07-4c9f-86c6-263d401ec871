﻿<template>
	<div class="wareArap-container">
		<el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="queryParams" ref="queryForm" :inline="true">
				<el-form-item label="来往单位">
					<el-input v-model="queryParams.contactunits" clearable="" placeholder="请输入来往单位" />
				</el-form-item>
				<el-form-item label="类别">
					<!-- <el-input-number v-model="queryParams.category"  clearable="" placeholder="请输入类别"/> -->
					<el-select v-model="queryParams.category" filterable clearable class="w160" placeholder="类别">
						<el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item label="收支时间" required>
					<el-date-picker
						v-model="queryParams.timeRange"
						type="daterange"
						value-format="YYYY-MM-DD"
						format="YYYY-MM-DD"
						:default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
						:shortcuts="shortcuts"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						style="width: 240px"
					/>
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'wareArap:page'"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
				</el-form-item>
				<el-form-item>
					<!-- <el-button type="primary" icon="ele-Plus" @click="openAddWareArap" v-auth="'wareArap:add'"> 新增 </el-button> -->
					<el-button type="primary" icon="ele-Plus" @click="importreport">导出</el-button>
				</el-form-item>
			</el-form>
		</el-card>
		<el-card class="full-table" shadow="hover" style="margin-top: 8px">
			<el-table :data="tableData" style="width: 100%" v-loading="loading" tooltip-effect="light" row-key="id" border="">
				<el-table-column type="index" label="序号" width="55" align="center" fixed="" />
				<!-- <el-table-column prop="id" label="主键Id" fixed="" show-overflow-tooltip="" /> -->
				<el-table-column prop="contactunits" label="来往单位" fixed="" show-overflow-tooltip="" />
				<el-table-column prop="category" label="类别" fixed="" show-overflow-tooltip="">
					<template v-slot="scope">
						<el-tag type="success" v-if="scope.row.category == 0">应收</el-tag>
						<el-tag type="danger" v-if="scope.row.category == 1">应付</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="pendingAmount" label="应收金额" fixed="" show-overflow-tooltip="" />
				<el-table-column prop="amountPaid" label="已收金额" fixed="" show-overflow-tooltip="" />
				<el-table-column prop="outstandingAmount" label="待收金额（已中止）" fixed="" show-overflow-tooltip="">
					<template #default="scope">
						<span>{{ scope.row.outstandingAmount }}</span>
						<span v-if="scope.row.stopAmountReceivable" style="color: #ff4d4f">({{ scope.row.stopAmountReceivable }})</span>
					</template>
				</el-table-column>
				<el-table-column prop="pendingAmountPayment" label="应付金额" fixed="" show-overflow-tooltip="" />
				<el-table-column prop="amountPaidPayment" label="已付金额" fixed="" show-overflow-tooltip="" />
				<el-table-column prop="amountUnpaidPayment" label="待付金额（已中止）" fixed="" show-overflow-tooltip="">
					<template #default="scope">
						<span>{{ scope.row.amountUnpaidPayment }}</span>
						<span v-if="scope.row.stopAmountPayable" style="color: #ff4d4f">({{ scope.row.stopAmountPayable }})</span>
					</template>
				</el-table-column>
				<!-- <el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip=""
          v-if="auth('wareArap:edit') || auth('wareArap:delete')">
          <template #default="scope">
            <el-button icon="ele-Edit" size="small" text="" type="primary" @click="openEditWareArap(scope.row)"
              v-auth="'wareArap:edit'"> 编辑 </el-button>
            <el-button icon="ele-Delete" size="small" text="" type="primary" @click="delWareArap(scope.row)"
              v-auth="'wareArap:delete'"> 删除 </el-button>
          </template>
        </el-table-column> -->
			</el-table>
			<el-pagination
				v-model:currentPage="tableParams.page"
				v-model:page-size="tableParams.pageSize"
				:total="tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				small=""
				background=""
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
			<editDialog ref="editDialogRef" :title="editWareArapTitle" @reloadTable="handleQuery" />
		</el-card>
	</div>
</template>

<script lang="ts" setup="" name="wareArap">
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { pageWareArap } from '/@/api/main/wareArap';
import dayjs from 'dayjs';

// 基础数据定义
const loading = ref(false);
const tableData = ref([]);
const tableParams = ref({
	page: 1,
	pageSize: 10,
	total: 0,
});

// 获取当前月份的时间范围
const getCurrentMonthRange = () => {
	const now = dayjs();
	return [now.startOf('month').format('YYYY-MM-DD'), now.endOf('month').format('YYYY-MM-DD')];
};

// 初始化查询参数
const queryParams = ref({
	timeRange: getCurrentMonthRange(),
	contactunits: undefined,
	handledbyName: undefined,
	revenueType: undefined,
});

const typeList = ref([
	{
		label: '应收',
		value: '0',
	},
	{
		label: '应付',
		value: '1',
	},
]);

// 时间范围快捷选项
const shortcuts = [
	{
		text: '本月',
		value: () => {
			const now = dayjs();
			return [now.startOf('month').toDate(), now.endOf('month').toDate()];
		},
	},
	{
		text: '上月',
		value: () => {
			const now = dayjs();
			return [now.subtract(1, 'month').startOf('month').toDate(), now.subtract(1, 'month').endOf('month').toDate()];
		},
	},
	{
		text: '最近三个月',
		value: () => {
			const now = dayjs();
			return [now.subtract(2, 'month').startOf('month').toDate(), now.endOf('month').toDate()];
		},
	},
];

// 查询操作
const handleQuery = async () => {
	if (!queryParams.value.timeRange?.length) {
		ElMessage.warning('请选择时间范围');
		return;
	}

	loading.value = true;
	try {
		const params = {
			...queryParams.value,
			...tableParams.value,
			startDate: queryParams.value.timeRange[0],
			endDate: queryParams.value.timeRange[1],
		};
		delete params.timeRange;

		const res = await pageWareArap(params);
		tableData.value = res.data.result?.items ?? [];
		tableParams.value.total = res.data.result?.total;
	} finally {
		loading.value = false;
	}
};

// 重置查询条件
const resetQuery = () => {
	queryParams.value = {
		timeRange: getCurrentMonthRange(),
		contactunits: undefined,
		handledbyName: undefined,
		revenueType: undefined,
	};
	handleQuery();
};

// 组件挂载时执行查询
onMounted(() => {
	handleQuery();
});
</script>


