﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("TradingAccounts","")]
    [Tenant("*************")]
    public class TradingAccounts : EntityTenant
    {
        /// <summary>
        /// 账户名称
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "账户名称", Length = 64)]
        public string Name { get; set; }
        /// <summary>
        /// 账户
        /// </summary>
        [SugarColumn(ColumnDescription = "账户", Length = 64)]
        public string? BankCode { get; set; }
        /// <summary>
        /// 银行
        /// </summary>
        [SugarColumn(ColumnDescription = "银行", Length = 64)]
        public string? BankName { get; set; }
        /// <summary>
        /// 开户行
        /// </summary>
        [SugarColumn(ColumnDescription = "开户行", Length = 64)]
        public string? BankAddress { get; set; }
        /// <summary>
        /// 账户金额
        /// </summary>
        [SugarColumn(ColumnDescription = "账户金额")]
        public decimal? Amount { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "状态")]
        public bool Status { get; set; }
    }
}