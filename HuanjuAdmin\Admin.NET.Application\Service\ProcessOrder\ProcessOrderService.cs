﻿using System;
using System.Linq;
using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using SqlSugar;

namespace Admin.NET.Application;
/// <summary>
/// 加工单服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class ProcessOrderService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<ProcessOrder> _rep;
    private readonly WarehouseStoreService _warehouseStoreService;
    private readonly WarehouseoutService _warehouseOutService;
    private readonly WarehouseInrecordService _warehouseInrecordService;
    private readonly UserManager _userManager;
    private readonly WarehouseInrecordMXService _warehouseInrecordMXService;
    private readonly WarehouseoutMXService _warehouseoutMXService;
    public ProcessOrderService(SqlSugarRepository<ProcessOrder> rep,
    WarehouseStoreService warehouseStoreService,
    WarehouseoutService warehouseOutService,
    WarehouseInrecordService warehouseInrecordService,
    WarehouseInrecordMXService warehouseInrecordMXService,
    WarehouseoutMXService warehouseoutMXService,
    UserManager userManager)
    {
        _rep = rep;
        _warehouseStoreService = warehouseStoreService;
        _warehouseOutService = warehouseOutService;
        _warehouseInrecordService = warehouseInrecordService;
        _userManager = userManager;
        _warehouseInrecordMXService = warehouseInrecordMXService;
        _warehouseoutMXService = warehouseoutMXService;
    }

    /// <summary>
    /// 分页查询加工单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<ProcessOrder>> Page(ProcessOrderInput input)
    {
        var query = _rep.AsQueryable()
                    .WhereIF(input.Id > 0, u => u.Id == input.Id)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.OrderNo), u => u.OrderNo.Contains(input.OrderNo.Trim()))
                    .WhereIF(input.SchemeId > 0, u => u.SchemeId == input.SchemeId)
                    .WhereIF(input.MaterialWarehouseId > 0, u => u.MaterialWarehouseId == input.MaterialWarehouseId)
                    .WhereIF(input.ProduceWarehouseId > 0, u => u.ProduceWarehouseId == input.ProduceWarehouseId)
                    .Includes(x => x.Scheme)
                    .Includes(x => x.MaterialWarehouse)
                    .Includes(x => x.ProduceWarehouse)
                    .Includes(x=>x.CreateUser);
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    [HttpPost]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<ProcessOrder> GetOne(ProcessOrderBaseInput input)
    {
        return await _rep.AsQueryable()
        .Where(u => u.Id == input.Id)
        .Includes(x => x.MaterialList, m => m.Warehousegoods, g => g.WarehouseGoodsUnit)
        .Includes(x => x.ProduceList, p => p.Warehousegoods, g => g.WarehouseGoodsUnit)
        .FirstAsync();
    }

    /// <summary>
    /// 增加加工单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddProcessOrderInput input)
    {
        var entity = input.Adapt<ProcessOrder>();

        if (!entity.ProduceWarehouseId.HasValue||!entity.MaterialWarehouseId.HasValue)
        {
            throw Oops.Oh("原料仓库、成品仓库不允许为空");
        }

        // 插入加工单
        entity.OrderNo = await App.GetRequiredService<PubOrderService>().GetNewOrder("SC");
        await _rep.Context.InsertNav(entity, new InsertNavRootOptions()
        {
            IgnoreColumns = new string[] { nameof(entity.UpdateUserId), nameof(entity.UpdateTime) }//InsertColumns也可以用只插入哪几列
        })
        .Include(x => x.MaterialList)
        .Include(x => x.ProduceList)
        .ExecuteCommandAsync();

        // 插入出库单
        var outrecordId = await _warehouseOutService.Add(new AddWarehouseoutMx
        {
            addWarehouseoutInput = new AddWarehouseoutInput
            {
                ActualAmt = entity.MaterialList.Sum(x => x.TotalPrice),
                GoodsInfo = String.Join(",", entity.MaterialList.Select(x => x.Warehousegoods.Name)),
                Outboundtype = 4,
                TotalAmt = entity.MaterialList.Sum(x => x.TotalPrice),
                WarehouseId = entity.MaterialWarehouseId.Value,
                Remark = "加工单出库"
            },
            listMx = entity.MaterialList.Select(x => new AddWarehouseoutMXInput
            {
                Tradename = x.Warehousegoods.Name,
                Barcode = x.Warehousegoods.barcode,
                Productcode = x.Warehousegoods.Code,
                Brand = x.Warehousegoods.Brand,
                Specifications = x.Warehousegoods.Specs,
                Unit = x.Warehousegoods.Unit,
                Unitprice = x.UnitPrice.Value,
                TotalAmt = x.TotalPrice.Value,
                goodsId = x.WarehousegoodsId.Value,
                GoodProduct = true,
                OutCount = (int)x.ActQuantity,
                TrueOutCount = 0,
            }).ToList()
        });
        // 提交出库
        await _warehouseOutService.Submit(new List<long> { outrecordId });
        // 出库单明细
        var outrecordMX = await _warehouseoutMXService.GetOutBoundMxList(new WarehouseoutMXInput
        {
            OutId = outrecordId
        });
        var outrecordMXList = outrecordMX.Select(x => new WarehouseoutMX
        {
            Id = x.Id,
            OutId = x.OutId,
            OutCount = x.OutCount,
            goodsId = x.GoodsId,
            ThisOutCount = x.OutCount,
            GoodProduct = x.GoodProduct,
        }).ToList();
        // 出库
        await _warehouseOutService.Outbound(outrecordMXList);



        // 插入入库单
        var inrecordId = await _warehouseInrecordService.Add(new AddWarehouseInrecordMx
        {
            addWarehouseInrecordInput = new AddWarehouseInrecordInput
            {
                ActualAmt = entity.ProduceList.Sum(x => x.TotalPrice),
                GoodsInfo = String.Join(",", entity.ProduceList.Select(x => x.Warehousegoods.Name)),
                InhouseType = RcvTypeEnum.ShouHou,
                TotalAmt = entity.ProduceList.Sum(x => x.TotalPrice),
                Warehouseid = entity.ProduceWarehouseId.ToString(),
                Remark = "加工单入库"
            },
            listMx = entity.ProduceList.Select(x => new AddWarehouseInrecordMXInput
            {
                DocumentNum = (int)x.ActQuantity,
                GoodsId = x.WarehousegoodsId,
                TotalAmt = x.TotalPrice.Value,
                Unit = x.Warehousegoods.Unit.ToString(),
                Unitprice = x.UnitPrice.Value
            }).ToList()
        });
        // 提交入库
        await _warehouseInrecordService.Submit(new List<long> { inrecordId });
        // 入库
        var inrecordMX = await _warehouseInrecordMXService.GetIncordMxList(new WarehouseInrecordMXInput
        {
            InrecordId = inrecordId
        });
        var inrecordMXList = inrecordMX.Select(x => new Warehousing
        {
            Id = x.Id,
            inrecordId = x.InrecordId,
            quantity = x.documentNum.Value,
            warehouse = x.GoodsId.ToString(),
            goodsId = x.GoodsId,
            batchs = new List<AddwarehousebatchInput>(),
            Unitprice = x.Unitprice,
            isproduct = true,
            tradeName = x.WarehousegoodsName,
            unit = x.Unit.ToString(),
            isWarranty = true,
            productCode = x.Id.ToString(),
            documentNum = x.documentNum.Value,
        }).ToList();
        
        // 为批次商品生成默认批次信息
        foreach (var item in inrecordMXList)
        {
            // 获取商品信息以检查是否为批次商品
            var goods = await _rep.Context.Queryable<Warehousegoods>()
                .Where(g => g.Id == item.goodsId)
                .FirstAsync();
                
            if (goods?.isbatch == true)
            {
                // 为批次商品生成默认批次信息
                // 批次号格式完全参考前端逻辑：年份后两位 + 月日时分 + 商品ID后4位
                var now = DateTime.Now;
                var timeStr = $"{now.ToString("yy")}{(now.Month).ToString().PadLeft(2, '0')}{now.Day.ToString().PadLeft(2, '0')}{now.Hour.ToString().PadLeft(2, '0')}{now.Minute.ToString().PadLeft(2, '0')}";
                var goodsIdSuffix = item.goodsId.ToString().Length > 4 
                    ? item.goodsId.ToString().Substring(item.goodsId.ToString().Length - 4) 
                    : item.goodsId.ToString();
                var defaultBatch = new AddwarehousebatchInput
                {
                    Batchnumber = $"{timeStr}{goodsIdSuffix}", // 加工单批次号：年月日时分 + 商品ID后4位
                    GoodProductNum = item.quantity, // 良品数量等于入库数量
                    RejectNum = 0, // 次品数量为0
                    ProduceTime = DateTime.Now.Date, // 生产日期为当前日期
                    WarrantyTime = goods.ExpirationDate ?? 365, // 保质期，默认365天
                    ExpiryReminder = goods.ExpiryReminder ?? 30, // 过期提醒，默认30天
                    ExpirationTime = DateTime.Now.Date.AddDays(goods.ExpirationDate ?? 365), // 到期时间
                    ShelflifeStatus = 0 // 正常状态
                };
                
                item.batchs = new List<AddwarehousebatchInput> { defaultBatch };
            }
        }
        
        await _warehouseStoreService.Add(inrecordMXList);
    }

    /// <summary>
    /// 删除加工单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteProcessOrderInput input)
    {
        var entity = input.Adapt<ProcessOrder>();
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新加工单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateProcessOrderInput input)
    {
        var entity = input.Adapt<ProcessOrder>();
        await _rep.Context.UpdateNav(entity, new UpdateNavRootOptions()
        {
            IgnoreColumns = new string[] { nameof(entity.CreateUserId), nameof(entity.CreateTime) }
        })
        .Include(x => x.MaterialList)
        .Include(x => x.ProduceList)
        .ExecuteCommandAsync();
    }

    // /// <summary>
    // /// 获取加工单
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [HttpGet]
    // [ApiDescriptionSettings(Name = "Detail")]
    // public async Task<ProcessOrder> Get([FromQuery] QueryByIdProcessOrderInput input)
    // {
    // }

    /// <summary>
    /// 获取加工单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<ProcessOrderOutput>> List([FromQuery] ProcessOrderInput input)
    {
        return await _rep.AsQueryable().Select<ProcessOrderOutput>().ToListAsync();
    }

    /// <summary>
    /// 获取加工单配置列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetProcessOrderSchemeList")]
    public async Task<List<ProcessOrderSchemeOutput>> GetProcessOrderSchemeList(ProcessOrderSchemeInput input)
    {
        return await _rep.Context.Queryable<ProcessOrderScheme>()
        .WhereIF(!string.IsNullOrWhiteSpace(input.SchemeName), u => u.SchemeName.Contains(input.SchemeName.Trim()))
        .Select<ProcessOrderSchemeOutput>(
            x => new ProcessOrderSchemeOutput
            {
                Id = x.Id,
                SchemeName = x.SchemeName
            }
        ).ToListAsync();
    }

    /// <summary>
    /// 获取加工单配置详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetProcessOrderSchemeDetail")]
    public async Task<ProcessOrder> GetProcessOrderSchemeDetail(ProcessOrderSchemeInput input)
    {
        return await _rep.Context.Queryable<ProcessOrderScheme>()
        .Where(x => x.Id == input.Id)
        .Includes(x => x.MaterialList, m => m.Warehousegoods, g => g.WarehouseGoodsUnit).Includes(x => x.ProduceList, p => p.Warehousegoods, g => g.WarehouseGoodsUnit)
        .Select<ProcessOrder>(
            x => new ProcessOrder
            {
                SchemeId = x.Id,
                MaterialWarehouseId = x.MaterialWarehouseId,
                ProduceWarehouseId = x.ProduceWarehouseId,
                MaterialList = x.MaterialList.Select(y => new ProcessOrderMaterial
                {
                    Id = y.Id,
                    WarehousegoodsId = y.Warehousegoods.Id,
                    Warehousegoods = y.Warehousegoods,
                    BaseQuantity = y.Quantity,
                    TotalPrice = 0
                }).ToList(),
                ProduceList = x.ProduceList.Select(y => new ProcessOrderProduce
                {
                    Id = y.Id,
                    WarehousegoodsId = y.Warehousegoods.Id,
                    Warehousegoods = y.Warehousegoods,
                    BaseQuantity = y.Quantity,
                    UnitPrice = y.UnitPrice,
                    TotalPrice = 0
                }).ToList()
            }
        ).FirstAsync();
    }

    /// <summary>
    /// 获取仓库列表    
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetWarehouseList")]
    public async Task<List<WarehouseOutput>> GetWarehouseList(WarehouseInput input)
    {
        return await _rep.Context.Queryable<Warehouse>()
                .Where(x => x.Status == 1)
                .Select<WarehouseOutput>(
                    x => new WarehouseOutput
                    {
                        Id = x.Id,
                        Name = x.Name
                    }
                ).ToListAsync();
    }


    /// <summary>
    /// 获取库存列表    
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetWarehouseStockList")]
    public async Task<List<WarehouseStoreOutput>> GetWarehouseStockList(Warehousing input)
    {
        return (await _rep.Context.Queryable<WarehouseStore>()
        .Where(x => x.WarehouseId == input.Id)
        .Includes(x => x.Warehousegoods)
        .Includes(x => x.WarehouseGoodsUnit)
        .Includes(x => x.Warehouses)
        .ToListAsync()).Select(u => new WarehouseStoreOutput
        {
            Id = u.Id,
            BarCode = u.Warehousegoods?.barcode,
            Brand = u.Warehousegoods?.Brand,
            //Notes = u.Notes,
            Number = u.Number,
            Quantity = u.Quantity,
            Unit = u.Unit,
            UnitName = u.WarehouseGoodsUnit?.Name,
            CurrentCost = u.CurrentCost,
            WarehouseId = u.WarehouseId,
            TradeID = u.TradeID,
            IsUniqueCode = u.IsUniqueCode ?? false,
            ProduceTime = u.ProduceTime,
            ProductCode = u.Warehousegoods?.Code,
            PurchaseUnitPrice = u.PurchaseUnitPrice,
            SafetyStockTallNum = u.SafetyStockTallNum,
            SafetyStockLowNum = u.SafetyStockLowNum,
            SevenSalesNum = 0,
            Specifications = u.Warehousegoods?.Specs,
            StockNum = 0,
            Warranty = 0,
            Warehouse = u.Warehouses?.Name,
            IsWarranty = u.IsWarranty,
            TradeName = u.Warehousegoods?.Name,
            Supplier = u.Supplier,
            AdventRemind = "",
            GoodProduct = u.GoodProduct,
            SalesOccupancy = u.SalesOccupancy ?? 0,
            Marketable = u.Marketable ?? 0,
            IntransitNum = u.IntransitNum ?? 0,
            Compatible = u.Compatible ?? 0,
            ShippedoutNum = u.ShippedoutNum ?? 0,
            StockOrNot = u.StockOrNot ?? false,
            ExpiredWarning = u.ExpiredWarning,
            StockWarning = u.StockWarning,
            isbatch = u.Warehousegoods?.isbatch ?? false,
            Reject = u.Reject
        }).ToList();
    }

    /// <summary>
    /// 获取仓库商品列表    
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetWarehouseGoodsList")]
    public async Task<List<WarehousegoodsOutput>> GetWarehouseGoodsList(WarehousegoodsInput input)
    {
        return await _rep.Context.Queryable<Warehousegoods>()
        .LeftJoin<WarehouseGoodsUnit>((s, w) => s.Unit == w.Id)
        .Where(x => x.IsDelete == false)
        .Select<WarehousegoodsOutput>(
            (x, w) => new WarehousegoodsOutput
            {
                Id = x.Id.SelectAll(),
                unitName = w.Name,
            }
        ).ToListAsync();
    }
}

