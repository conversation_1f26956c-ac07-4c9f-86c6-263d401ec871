﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 商品采购表
    /// </summary>
    [SugarTable("WarehousePurchase", "")]
    [Tenant("1300000000001")]
    public class WarehousePurchase : EntityTenant
    {
        /// <summary>
        /// 采购单号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "采购单号", Length = 16)]
        public string OrderNumber { get; set; }
        /// <summary>
        /// 供应商ID
        /// </summary>
        [SugarColumn(ColumnDescription = "供应商ID")]
        public long? SupplierId { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(SupplierId))]
        public PubSupplier supplier { get; set; }
        /// <summary>
        /// 仓库编号
        /// </summary>
        [SugarColumn(ColumnDescription = "仓库编号")]
        public long? WarehouseId { get; set; }
        /// <summary>
        /// 仓库
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(WarehouseId))]
        public Warehouse Warehouse { get; set; }
        /// <summary>
        /// 单据状态
        /// </summary>
        [SugarColumn(ColumnDescription = "单据状态")]
        public OrderStatusEnum DocumenntStatus { get; set; }
        /// <summary>
        /// 入库状态
        /// </summary>
        [SugarColumn(ColumnDescription = "入库状态")]
        public RcvStatusEnum InhouseStatus { get; set; }

        /// <summary>
        /// 商品信息
        /// </summary>
        [SugarColumn(ColumnDescription = "商品信息", Length = 1000)]
        public string GoodsInfo { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        [SugarColumn(ColumnDescription = "总金额")]
        public decimal? TotalAmt { get; set; }

        /// <summary>
        /// 优惠金额
        /// </summary>
        [SugarColumn(ColumnDescription = "优惠金额")]
        public decimal? DiscountAmt { get; set; }

        /// <summary>
        /// 实际金额
        /// </summary>
        [SugarColumn(ColumnDescription = "实际金额")]
        public decimal? ActualAmt { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 100)]
        public string? Remark { get; set; }

        /// <summary>
        /// 审批单号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "审批单号", Length = 20)]
        public string ApprNo { get; set; }
    }
}