﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Application.Service.OutboundRecord.Dto;
using Admin.NET.Application.Service.WarehouseInrecordMX.Dto;
using Admin.NET.Core.Util.Npoi;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.StaticFiles;
using Nest;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using SqlSugar;
using System;
using System.Data.Common;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Security.Permissions;

namespace Admin.NET.Application;
/// <summary>
/// 入库单明细服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WarehouseInrecordMXService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<Warehousegoods> _repgood;
    private readonly SqlSugarRepository<Warehouseuniquecode> _repquecode;
    private readonly SqlSugarRepository<WarehouseInrecordMX> _rep;
    private readonly SqlSugarRepository<OutInBoundRecord> _repOutInBound;
    UserManager _userManager;
    public WarehouseInrecordMXService(SqlSugarRepository<WarehouseInrecordMX> rep, SqlSugarRepository<Warehousegoods> repgood, UserManager userManager, SqlSugarRepository<Warehouseuniquecode> repquecode, SqlSugarRepository<OutInBoundRecord> repOutInBound)
    {
        _repgood = repgood;
        _repquecode = repquecode;
        _rep = rep;
        _userManager = userManager;
        _repOutInBound = repOutInBound;
    }

    /// <summary>
    /// 分页查询入库单明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WarehouseInrecordMXOutput>> Page(WarehouseInrecordMXInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(u => u.TenantId == _userManager.TenantId)
                    .WhereIF(input.InrecordId > 0, u => u.InrecordId == input.InrecordId)
                    .WhereIF(input.GoodsId > 0, u => u.GoodsId == input.GoodsId)
                    .WhereIF(input.SupplierId > 0, u => u.SupplierId == input.SupplierId)
                    .Where(u => u.IsDelete == false)
                    .Select(u => new WarehouseInrecordMXOutput
                    {
                        Id = u.Id,
                        InrecordId = u.InrecordId,
                        GoodsId = u.GoodsId,
                        WarehousegoodsName = u.Warehousegoods.Name,
                        Rating = u.Rating,
                        Unit = u.Unit,
                        UnitName = u.WarehouseGoodsUnit.Name,
                        ProductDate = u.ProductDate,
                        Shelflife = u.Shelflife,
                        ShelflifeUnit = u.ShelflifeUnit,
                        Expires = u.Expires,
                        PuchQty = u.PuchQty,
                        RcvQty = u.RcvQty,
                        SupplierId = u.SupplierId,
                        PubSupplierName = u.PubSupplier.Name,
                        Unitprice = u.Unitprice,
                        TotalAmt = u.TotalAmt,
                        Barcode = u.Warehousegoods.barcode,
                        brandName = u.Warehousegoods.Brand,
                        productCode = u.Warehousegoods.Code,
                        specsName = u.Warehousegoods.Specs,
                        unique = u.Warehousegoods.isuniquecode,
                        isbatch = u.Warehousegoods.isbatch,
                        documentNum = u.DocumentNum,
                        isproduct = u.GoodProduct,
                        GoodProduct = u.GoodProduct
                    });

        if (input.ProductDateRange != null && input.ProductDateRange.Count > 0)
        {
            DateTime? start = input.ProductDateRange[0];
            query = query.WhereIF(start.HasValue, u => u.ProductDate > start);
            if (input.ProductDateRange.Count > 1 && input.ProductDateRange[1].HasValue)
            {
                var end = input.ProductDateRange[1].Value.AddDays(1);
                query = query.Where(u => u.ProductDate < end);
            }
        }
        if (input.ExpiresRange != null && input.ExpiresRange.Count > 0)
        {
            DateTime? start = input.ExpiresRange[0];
            query = query.WhereIF(start.HasValue, u => u.Expires > start);
            if (input.ExpiresRange.Count > 1 && input.ExpiresRange[1].HasValue)
            {
                var end = input.ExpiresRange[1].Value.AddDays(1);
                query = query.Where(u => u.Expires < end);
            }
        }
        query = query.OrderBuilder(input);
        var listMxPage = await query.ToPagedListAsync(input.Page, input.PageSize);
        return listMxPage;
    }

    /// <summary>
    /// 查询入库单明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetIncordMxList")]
    public async Task<List<WarehouseInrecordMXOutput>> GetIncordMxList(WarehouseInrecordMXInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(u => u.TenantId == _userManager.TenantId)
                    .WhereIF(input.InrecordId > 0, u => u.InrecordId == input.InrecordId)
                    .WhereIF(input.GoodsId > 0, u => u.GoodsId == input.GoodsId)
                    .WhereIF(input.SupplierId > 0, u => u.SupplierId == input.SupplierId)
                    .Where(u => u.IsDelete == false)
                    .Select(u => new WarehouseInrecordMXOutput
                    {
                        Id = u.Id,
                        InrecordId = u.InrecordId,
                        GoodsId = u.GoodsId,
                        WarehousegoodsName = u.Warehousegoods.Name,
                        Rating = u.Rating,
                        Unit = u.Unit,
                        UnitName = u.WarehouseGoodsUnit.Name,
                        ProductDate = u.ProductDate,
                        // Shelflife = u.Shelflife,
                        ShelflifeUnit = u.ShelflifeUnit,
                        Expires = u.Expires,
                        Shelflife = u.Warehousegoods.ExpirationDate,
                        ExpiryReminder = u.Warehousegoods.ExpiryReminder,
                        PuchQty = u.PuchQty,
                        RcvQty = u.RcvQty,
                        SupplierId = u.SupplierId,
                        PubSupplierName = u.PubSupplier.Name,
                        Unitprice = u.Unitprice,
                        TotalAmt = u.TotalAmt,
                        Barcode = u.Warehousegoods.barcode,
                        brandName = u.Warehousegoods.Brand,
                        productCode = u.Warehousegoods.Code,
                        specsName = u.Warehousegoods.Specs,
                        unique = u.Warehousegoods.isuniquecode,
                        isbatch = u.Warehousegoods.isbatch,
                        documentNum = u.DocumentNum,
                        isproduct = u.GoodProduct,
                        GoodProduct = u.GoodProduct
                    });

        if (input.ProductDateRange != null && input.ProductDateRange.Count > 0)
        {
            DateTime? start = input.ProductDateRange[0];
            query = query.WhereIF(start.HasValue, u => u.ProductDate > start);
            if (input.ProductDateRange.Count > 1 && input.ProductDateRange[1].HasValue)
            {
                var end = input.ProductDateRange[1].Value.AddDays(1);
                query = query.Where(u => u.ProductDate < end);
            }
        }
        if (input.ExpiresRange != null && input.ExpiresRange.Count > 0)
        {
            DateTime? start = input.ExpiresRange[0];
            query = query.WhereIF(start.HasValue, u => u.Expires > start);
            if (input.ExpiresRange.Count > 1 && input.ExpiresRange[1].HasValue)
            {
                var end = input.ExpiresRange[1].Value.AddDays(1);
                query = query.Where(u => u.Expires < end);
            }
        }

        var listMx = await query.OrderBuilder(input).ToListAsync();
        return listMx;
    }


    public async Task<int> AddOrUpdate(List<WarehouseInrecordMX> listMx)
    {
        //if (listMx.Count>0)
        //{
        //    var s= await _rep.AsQueryable().Where(x => x.InrecordId == listMx[0].InrecordId).ToListAsync();
        //    await _rep.DeleteAsync(s);
        //}
        //List<WarehouseInrecordMX> lx = new List<WarehouseInrecordMX>();
        //foreach (var item in listMx) {
        //   var s=await _rep.AsQueryable().Where(x => x.Id == item.Id).FirstAsync();
        //    if (s==null)
        //    {
        //        lx.Add(item);
        //    }
        //}
        return await _rep.AsSugarClient().Storageable(listMx).ExecuteCommandAsync();
    }
    /// <summary>
    /// 增加入库单明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddWarehouseInrecordMXInput input)
    {
        var entity = input.Adapt<WarehouseInrecordMX>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除入库单明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteWarehouseInrecordMXInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }


    [HttpPost]
    [ApiDescriptionSettings(Name = "Download")]
    /// <summary>
    /// 下载模板
    /// </summary>
    /// <returns></returns>
    [HttpGet("DownloadImportTemp")]
    public FileContentResult DownloadImportTemp()
    {
        List<DownloadDto> s = new List<DownloadDto>();
        s.Add(new DownloadDto
        {
            BarCode = "",
            Number = ""
        });
        Dictionary<string, string> dicColumns = new Dictionary<string, string>
        {
            ["Number"] = "商品名称",
            ["BarCode"] = "唯一码",
        };
        List<WarehouseStoreOutput> lw = new List<WarehouseStoreOutput>();
        var export = NpoiHelper.ExportExcel(s, dicColumns);
        var mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        return new FileContentResult(export, mimeType)
        {
            // FileDownloadName = fileName
        };
    }




    /// <summary>
    /// 数据导入
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Import")]
    public async Task<string> Import(IFormFile File)
    {
        //goodId
        string ReturnValue = string.Empty;
        //定义一个bool类型的变量用来做验证
        bool flag = true;
        try
        {
            string fileExt = Path.GetExtension(File.FileName).ToLower();
            //定义一个集合一会儿将数据存储进来,全部一次丢到数据库中保存
            var Data = new List<Warehouseuniquecode>();
            MemoryStream ms = new MemoryStream();
            File.CopyTo(ms);
            ms.Seek(0, SeekOrigin.Begin);
            IWorkbook book;
            if (fileExt == ".xlsx")
            {
                book = new XSSFWorkbook(ms);
            }
            else if (fileExt == ".xls")
            {
                book = new HSSFWorkbook(ms);
            }
            else
            {
                book = null;
            }
            ISheet sheet = book.GetSheetAt(0);

            int CountRow = sheet.LastRowNum + 1;//获取总行数

            if (CountRow - 1 == 0)
            {
                return "Excel列表数据项为空!";

            }
            #region 循环验证
            for (int i = 1; i < CountRow; i++)
            {
                //获取第i行的数据
                var row = sheet.GetRow(i);
                if (row != null)
                {
                    //循环的验证单元格中的数据
                    for (int j = 0; j < 2; j++)
                    {
                        if (row.GetCell(j) == null || row.GetCell(j).ToString().Trim().Length == 0)
                        {
                            flag = false;
                            ReturnValue += $"第{i + 1}行,第{j + 1}列数据不能为空。";
                        }
                    }
                }
            }
            #endregion
            if (flag)
            {
                for (int i = 1; i < CountRow; i++)
                {
                    //实例化实体对象
                    Warehouseuniquecode commodity = new Warehouseuniquecode();
                    var row = sheet.GetRow(i);
                    //具体字段赋值
                    //if (row.GetCell(0) != null && row.GetCell(0).ToString().Trim().Length > 0)
                    //{
                    //    commodity.UserID = row.GetCell(0).ToString();
                    //}
                    //if (row.GetCell(1) != null && row.GetCell(1).ToString().Trim().Length > 0)
                    //{
                    //    commodity.SysOrgName = row.GetCell(1).ToString();
                    //}
                    //if (row.GetCell(2) != null && row.GetCell(2).ToString().Trim().Length > 0)
                    //{
                    //    commodity.SysPosName = row.GetCell(2).ToString();
                    //}
                    //if (row.GetCell(3) != null && row.GetCell(3).ToString().Trim().Length > 0)
                    //{
                    //    commodity.SlipMonth = row.GetCell(3).ToString().ToString();
                    //}
                    if (row.GetCell(1) != null && row.GetCell(1).ToString().Trim().Length > 0)
                    {
                        var a = row.GetCell(0).ToString().ToString();
                        var s = await _repgood.AsQueryable().WhereIF(!string.IsNullOrEmpty(a), x => x.Name == a).FirstAsync();
                        commodity.CreateTime = DateTime.Now;
                        commodity.UniqueCode = row.GetCell(1).ToString().ToString();
                        commodity.TradeID = s.Id;
                    }
                    else
                    {
                        // commodity.Grading = "暂无";
                    }
                    Data.Add(commodity);
                }
                var data = await _repquecode.InsertRangeAsync(Data);
                ReturnValue = $"数据导入成功,共导入{CountRow - 1}条数据。";
            }

            if (!flag)
            {
                ReturnValue = "数据存在问题！" + ReturnValue;
            }
        }
        catch (Exception)
        {
            return "服务器异常";
        }

        return ReturnValue;
    }

    /// <summary>
    /// 更新入库单明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWarehouseInrecordMXInput input)
    {
        var entity = input.Adapt<WarehouseInrecordMX>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取入库单明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<WarehouseInrecordMX> Get([FromQuery] QueryByIdWarehouseInrecordMXInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取入库单明细列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WarehouseInrecordMXOutput>> List([FromQuery] WarehouseInrecordMXInput input)
    {
        return await _rep.AsQueryable().Select<WarehouseInrecordMXOutput>().ToListAsync();
    }

    /// <summary>
    /// 获取商品ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "WarehousegoodsDropdown"), HttpGet]
    public async Task<dynamic> WarehousegoodsDropdown()
    {
        return await _rep.Context.Queryable<Warehousegoods>()
                .Where(x => x.IsDelete == false)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id
                }
                ).ToListAsync();
    }
    /// <summary>
    /// 获取供应商ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "PubSupplierDropdown"), HttpGet]
    public async Task<dynamic> PubSupplierDropdown()
    {
        return await _rep.Context.Queryable<PubSupplier>()
                .Where(x => x.IsDelete == false)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id
                }
                ).ToListAsync();
    }




}

