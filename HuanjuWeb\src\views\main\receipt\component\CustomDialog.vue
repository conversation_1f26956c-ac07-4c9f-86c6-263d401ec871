<template>
	<el-dialog v-model="visible" style="border: none;" title="新时代云开票" width="100%" top="5vh" @closed="handleClosed">
		<iframe :src="redirectUrl" style="width: 100%; height: 100vh; border: none"></iframe>
	</el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';

const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
	redirectUrl: {
		type: String,
		default: '',
	},
});

const visible = ref(props.modelValue);

watch(
	() => props.modelValue,
	(newValue) => {
		visible.value = newValue;
	}
);

const emit = defineEmits(['update:modelValue', 'dialogClosed']);

const handleClosed = () => {
	emit('dialogClosed');
};
</script>

<style scoped>
.el-dialog__body {
	padding: 0;
	height: 100vh; /* 设置内容区域高度为100vh */
}
</style>