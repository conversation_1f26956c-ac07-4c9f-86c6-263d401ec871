using Furion.Schedule;
using Microsoft.Extensions.Logging;
using Quartz;
using System;
using Admin.NET.Application;
using Mapster;
using Admin.NET.Application.Entity;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel;

namespace Admin.NET.Application.Jobs;

/// <summary>
/// 批次商品保质期检查任务
/// </summary>
[JobDetail("job_batchExpiration", Description = "批次商品保质期检查", GroupName = "default", Concurrent = false)]
[Daily(TriggerId = "trigger_batchExpiration", Description = "批次商品保质期检查", StartTime = "02:00:00")]
public class BatchExpirationJob : Quartz.IJob, IDynamicApiController
{
    private readonly ILogger<BatchExpirationJob> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly SqlSugarRepository<BatchExpirationJobLog> _jobLogRepo;
    private readonly SqlSugarRepository<warehousebatch> _batchRepo;
    private readonly SqlSugarRepository<WarehouseStore> _storeRepo;

    public BatchExpirationJob(
        ILogger<BatchExpirationJob> logger,
        IServiceProvider serviceProvider,
        SqlSugarRepository<BatchExpirationJobLog> jobLogRepo,
        SqlSugarRepository<warehousebatch> batchRepo,
        SqlSugarRepository<WarehouseStore> storeRepo)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _jobLogRepo = jobLogRepo;
        _batchRepo = batchRepo;
        _storeRepo = storeRepo;
    }

    /// <summary>
    /// 手动触发任务
    /// </summary>
    /// <returns></returns>
    [HttpPost("/batchExpiration/trigger")]
    [DisplayName("手动触发批次商品保质期检查")]
    public async Task<string> TriggerJob()
    {
        try
        {
            await Execute(null);
            return "任务执行成功";
        }
        catch (Exception ex)
        {
            return $"任务执行失败: {ex.Message}";
        }
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var jobLog = new BatchExpirationJobLog
        {
            StartTime = DateTime.Now,
            Status = 0,
            ProcessCount = 0
        };

        try
        {
            _logger.LogInformation("批次商品保质期检查任务开始执行: {time}", DateTime.Now);

            using var scope = _serviceProvider.CreateScope();
            var batchQueueService = scope.ServiceProvider.GetRequiredService<WarehouseBatchQueueService>();
            var totalProcessed = 0;

            while (true)
            {
                var pendingItems = await batchQueueService.GetPendingItems(1000);
                if (pendingItems == null || pendingItems.Count == 0)
                {
                    break;
                }

                var processedIds = new List<long>();
                var batchUpdates = new List<warehousebatch>();
                var storeUpdates = new List<WarehouseStore>();

                foreach (var item in pendingItems)
                {
                    try
                    {
                        var batch = await _batchRepo.GetByIdAsync(item.BatchId);
                        if (batch != null)
                        {
                            batch.ShelflifeStatus = item.Type == 0 ? 1 : 2;
                            batchUpdates.Add(batch);
                            processedIds.Add(item.Id);

                            var store = await _storeRepo.AsQueryable()
                                .Where(x => x.Id == batch.InventoryId)
                                .FirstAsync();
                                
                            if (store != null)
                            {
                                store.ExpiredWarning = batch.ShelflifeStatus;
                                storeUpdates.Add(store);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "处理批次ID {batchId} 失败", item.BatchId);
                    }
                }

                if (batchUpdates.Count > 0)
                {
                    await _batchRepo.AsUpdateable(batchUpdates)
                        .UpdateColumns(x => new { x.ShelflifeStatus })
                        .ExecuteCommandAsync();
                }

                if (storeUpdates.Count > 0)
                {
                    await _storeRepo.AsUpdateable(storeUpdates)
                        .UpdateColumns(x => new { x.ExpiredWarning })
                        .ExecuteCommandAsync();
                }

                if (processedIds.Count > 0)
                {
                    await batchQueueService.UpdateQueueStatus(processedIds);
                }

                totalProcessed += processedIds.Count;
                await Task.Delay(200);
            }

            _logger.LogInformation("批次商品保质期检查任务执行完成: {time}", DateTime.Now);

            // 更新日志
            jobLog.Status = 1;
            jobLog.ProcessCount = totalProcessed;
            jobLog.EndTime = DateTime.Now;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批次商品保质期检查任务执行失败");
            jobLog.ErrorMessage = ex.Message;
            jobLog.EndTime = DateTime.Now;
            throw;
        }
        finally
        {
            await _jobLogRepo.InsertAsync(jobLog);
        }
    }
}