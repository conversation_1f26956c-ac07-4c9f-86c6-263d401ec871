﻿using SqlSugar;

namespace Admin.NET.Application;

/// <summary>
/// 商品信息输出参数
/// </summary>
public class WarehousegoodsDto
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Specs { get; set; }

    /// <summary>
    /// 库存数量
    /// </summary>
    public int InventoryCount { get; set; }

    /// <summary>
    /// 告警数量（库存不足）
    /// </summary>
    public int AlarmMin { get; set; }

    /// <summary>
    /// 库存数量（库存堆积）
    /// </summary>
    public int AlarmMax { get; set; }

    /// <summary>
    /// 初始库存数量
    /// </summary>
    public int BaseInventoryCount { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

}
