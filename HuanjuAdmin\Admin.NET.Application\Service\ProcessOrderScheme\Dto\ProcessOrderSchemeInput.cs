﻿using System.ComponentModel.DataAnnotations;
using Admin.NET.Application.Entity;

namespace Admin.NET.Application;

    /// <summary>
    /// 加工单配置管理基础输入参数
    /// </summary>
    public class ProcessOrderSchemeBaseInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public virtual long Id { get; set; }
        
        /// <summary>
        /// 方案编号
        /// </summary>
        public virtual string SchemeNo { get; set; }
        
        /// <summary>
        /// 方案名称
        /// </summary>
        public virtual string SchemeName { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public virtual int? Status { get; set; }

        /// <summary>
        /// 原料仓库
        /// </summary>
        public long? MaterialWarehouseId { get; set; }

        /// <summary>
        /// 成品仓库
        /// </summary>
        public long? ProduceWarehouseId { get; set; }
    }

    /// <summary>
    /// 加工单配置管理分页查询输入参数
    /// </summary>
    public class ProcessOrderSchemeInput : BasePageInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 方案编号
        /// </summary>
        public string SchemeNo { get; set; }
        
        /// <summary>
        /// 方案名称
        /// </summary>
        public string SchemeName { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }
        
    }

    /// <summary>
    /// 加工单配置管理增加输入参数
    /// </summary>
    public class AddProcessOrderSchemeInput : ProcessOrderSchemeBaseInput
    {
        /// <summary>
        /// 加工单配置原料列表
        /// </summary>
        public List<ProcessOrderSchemeMaterial>? MaterialList { get; set; }
        /// <summary>
        /// 加工单配置产出列表
        /// </summary>
        public List<ProcessOrderSchemeProduce>? ProduceList { get; set; }
    }

    /// <summary>
    /// 加工单配置管理删除输入参数
    /// </summary>
    public class DeleteProcessOrderSchemeInput : BaseIdInput
    {
    }

    /// <summary>
    /// 加工单配置管理更新输入参数
    /// </summary>
    public class UpdateProcessOrderSchemeInput : ProcessOrderSchemeBaseInput
    {
        /// <summary>
        /// 加工单配置原料列表
        /// </summary>
        public List<ProcessOrderSchemeMaterial>? MaterialList { get; set; }
        /// <summary>
        /// 加工单配置产出列表
        /// </summary>
        public List<ProcessOrderSchemeProduce>? ProduceList { get; set; }
    }

    /// <summary>
    /// 加工单配置管理主键查询输入参数
    /// </summary>
    public class QueryByIdProcessOrderSchemeInput : DeleteProcessOrderSchemeInput
    {

    }
