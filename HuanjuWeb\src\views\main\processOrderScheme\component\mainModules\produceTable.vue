<template>
    <table-panel :table-data="paginatedData" :columns="tableColumns" :loading="loading" :table-params="localPageParams"
        @selection-change="handleSelectionChange" @row-click="handleRowClick" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" style="height: calc(55vh - var(--el-card-padding) - 15px - var(--el-tabs-header-height)); ">
    </table-panel>
</template>

<script setup lang="ts">
import tablePanel from '/@/components/tablePanel/index.vue';
import { ref, computed, reactive, watch } from 'vue';


// 修改props，接收tableData作为必要参数
const props = defineProps({
    tableData: {
        type: Array,
        required: true,
        default: () => []
    }
});

const loading = ref(false);

// 本地分页参数
const localPageParams = reactive<PageParams>({
    page: 1,
    pageSize: 10,
    total: 0
});

watch(() => props.tableData, () => {
    localPageParams.total = props.tableData.length;
}, { immediate: true });

// 计算属性：根据本地分页参数处理tableData
const paginatedData = computed(() => {
    const { page, pageSize } = localPageParams;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    return props.tableData.slice(start, end);
});

// 表格列配置
const tableColumns: TableColumn[] = [
    { type: 'index', label: '序号' },
    { prop: 'warehousegoods.code', label: '产品编码', showOverflowTooltip: true },
    { prop: 'warehousegoods.name', label: '产品名称', showOverflowTooltip: true },
    { prop: 'warehousegoods.specs', label: '规格型号', showOverflowTooltip: true },
    { prop: 'warehousegoods.warehouseGoodsUnit.name', label: '单位', showOverflowTooltip: true },
    { prop: 'quantity', label: '数量', showOverflowTooltip: true },
    { prop: 'unitPrice', label: '单价', showOverflowTooltip: true }
];

const handleSelectionChange = (selection:any) => {
    console.log('选中项：', selection);
};

const handleRowClick = (row:any) => {
    console.log('点击行：', row);
};

const handleSizeChange = (val:number) => {
    localPageParams.pageSize = val;
    localPageParams.page = 1;
};

const handleCurrentChange = (val:number) => {
    localPageParams.page = val;
};

// 这个函数不再需要，因为tableData直接通过props传入
// const setTableData = (data:any) => {
//     tableData.value = data;
// };

// 不再需要暴露setTableData方法
// defineExpose({
//     setTableData
// });

</script>

<style scoped lang="scss">
:deep(.el-card__body) {
    --el-card-padding: 8px;
}
</style>