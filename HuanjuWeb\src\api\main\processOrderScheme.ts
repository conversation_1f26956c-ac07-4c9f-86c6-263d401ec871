import request from '/@/utils/request';
enum Api {
	PageProcessOrderScheme = '/api/processOrderScheme/page',
	ListProcessOrderScheme = '/api/processOrderScheme/list',
	GetOneProcessOrderScheme = '/api/processOrderScheme/detail',
	AddProcessOrderScheme = '/api/processOrderScheme/add',
	UpdateProcessOrderScheme = '/api/processOrderScheme/update',
	DeleteProcessOrderScheme = '/api/processOrderScheme/delete',
	GetWarehouseList = '/api/processOrderScheme/getWarehouseList',
	GetWarehouseGoodsList = '/api/processOrderScheme/getWarehouseGoodsList',
	GetWarehouseStoreList = '/api/processOrderScheme/getWarehouseStoreList'
}

// 分页查询出入库记录
export const pageProcessOrderScheme = (params?: any) =>
	request({
		url: Api.PageProcessOrderScheme,
		method: 'post',
		data: params,
	});

// 获取单个打印模板
export const getOneProcessOrderScheme = (params?: any) =>
	request({
		url: Api.GetOneProcessOrderScheme,
		method: 'post',
		data: params,
	});

export function addProcessOrderScheme(records: any) {
	return request({
		url: Api.AddProcessOrderScheme,
		method: 'post',
		data: records  
	});
}

export function updateProcessOrderScheme(records: any) {
	return request({
		url: Api.UpdateProcessOrderScheme,
		method: 'post',
		data: records  
	});
}

export function deleteProcessOrderScheme(records: any) {
	return request({
		url: Api.DeleteProcessOrderScheme,
		method: 'post',
		data: records  
	});
}

// 获取仓库列表
export const getWarehouseList = (params?: any) =>
	request({
		url: Api.GetWarehouseList,
		method: 'post',
		data: params,
	});	

// 获取原料列表
export const getWarehouseGoodsList = (params?: any) =>
	request({
		url: Api.GetWarehouseGoodsList,
		method: 'post',
		data: params,
	});	

// 获取原料库存列表
export const getWarehouseStoreList = (params?: any) =>
	request({
		url: Api.GetWarehouseStoreList,
		method: 'post',
		data: params,
	});
