﻿namespace Admin.NET.Application;

/// <summary>
/// 工资条输出参数
/// </summary>
public class SalarySlipDto
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public string SysUserRealName { get; set; }

    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    public long UserID { get; set; }

    /// <summary>
    /// 应勤天数
    /// </summary>
    public decimal? ShouldDays { get; set; }

    /// <summary>
    /// 出勤天数
    /// </summary>
    public decimal? WorkDays { get; set; }

    /// <summary>
    /// 应发工资
    /// </summary>
    public decimal? Should<PERSON><PERSON> { get; set; }

    /// <summary>
    /// 绩效等级
    /// </summary>
    public string? Grading { get; set; }

    /// <summary>
    /// 绩效
    /// </summary>
    public decimal? KpiMoney { get; set; }

    /// <summary>
    /// 提成基数
    /// </summary>
    public decimal? CommissionBase { get; set; }

    /// <summary>
    /// 提成系数
    /// </summary>
    public decimal? CommissionRatio { get; set; }

    /// <summary>
    /// 提成
    /// </summary>
    public decimal? Commission { get; set; }

    /// <summary>
    /// 迟到早退
    /// </summary>
    public int? WorkLateLeveEarly { get; set; }

    /// <summary>
    /// 考勤扣除
    /// </summary>
    public decimal? AttendanceTakeOff { get; set; }

    /// <summary>
    /// 餐补
    /// </summary>
    public decimal? Meals { get; set; }

    /// <summary>
    /// 话费补贴
    /// </summary>
    public decimal? PhoneBill { get; set; }

    /// <summary>
    /// 交通补贴
    /// </summary>
    public decimal? TafficBill { get; set; }

    /// <summary>
    /// 住房补贴
    /// </summary>
    public decimal? HouseBill { get; set; }

    /// <summary>
    /// 全勤奖
    /// </summary>
    public decimal? Attendance { get; set; }

    /// <summary>
    /// 其它奖励
    /// </summary>
    public decimal? OrtheMoney { get; set; }

    /// <summary>
    /// 社保代扣
    /// </summary>
    public decimal? SocialInsurance { get; set; }

    /// <summary>
    /// 公积金代缴
    /// </summary>
    public decimal? AccumulationFund { get; set; }

    /// <summary>
    /// 个税代缴
    /// </summary>
    public decimal? Tax { get; set; }

    /// <summary>
    /// 其它扣款
    /// </summary>
    public decimal? OrtherTakOff { get; set; }

    /// <summary>
    /// 实发
    /// </summary>
    public decimal? ActualMoney { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public SalarySlipStatusEnum Status { get; set; }

}
