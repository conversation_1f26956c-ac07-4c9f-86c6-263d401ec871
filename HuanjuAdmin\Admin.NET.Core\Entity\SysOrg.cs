﻿namespace Admin.NET.Core;

/// <summary>
/// 系统机构表
/// </summary>
[SugarTable(null, "系统机构表")]
[SystemTable]
public class SysOrg : EntityTenant
{
    /// <summary>
    /// 父Id
    /// </summary>
    [SugarColumn(ColumnDescription = "父Id")]
    public long Pid { get; set; }

    /// <summary>
    /// 职位ID
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public long? PosId { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    [Required, MaxLength(64)]
    public virtual string Name { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnDescription = "编码", Length = 64)]
    [MaxLength(64)]
    public string? Code { get; set; }

    /// <summary>
    /// 机构类型
    /// </summary>
    [SugarColumn(ColumnDescription = "机构类型", Length = 64)]
    [MaxLength(64)]
    public string? OrgType { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnDescription = "排序")]
    public int OrderNo { get; set; }


    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 128)]
    [MaxLength(128)]
    public string? Remark { get; set; }
    /// <summary>
    /// 联系人
    /// </summary>
    [SugarColumn(ColumnDescription = "联系人", Length = 255)]
    [MaxLength(255)]
    public string? Contacts { get; set; }
    /// <summary>
    /// 电话
    /// </summary>
    [SugarColumn(ColumnDescription = "电话", Length = 100)]
    [MaxLength(100)]
    public string? Phone { get; set; }
    /// <summary>
    /// 开户行
    /// </summary>
    [SugarColumn(ColumnDescription = "开户行", Length = 100)]
    [MaxLength(100)]
    public string? BankName { get; set; }
    /// <summary>
    /// 银行账号
    /// </summary>
    [SugarColumn(ColumnDescription = "银行账号", Length = 100)]
    [MaxLength(100)]
    public string? BankCode { get; set; }
    /// <summary>
    /// 税务登记号
    /// </summary>
    [SugarColumn(ColumnDescription = "税务登记号", Length = 100)]
    [MaxLength(100)]
    public string? TaxId { get; set; }
    /// <summary>
    /// 地址
    /// </summary>
    [SugarColumn(ColumnDescription = "地址", Length = 255)]
    [MaxLength(255)]
    public string? Address { get; set; }
    /// <summary>
    /// 公司法人
    /// </summary>
    [SugarColumn(ColumnDescription = "公司法人", Length = 64)]
    [MaxLength(64)]
    public string? CompanyLegalRepresentative { get; set; }
    /// <summary>
    /// 公司性质
    /// </summary>
    [SugarColumn(ColumnDescription = "公司性质", Length = 64)]
    [MaxLength(64)]
    public string? CompanyNature { get; set; }
    /// <summary>
    /// 税务类型
    /// </summary>
    [SugarColumn(ColumnDescription = "税务类型")]
    public int? TaxType { get; set; }
    /// <summary>
    /// 开票员
    /// </summary>
    [SugarColumn(ColumnDescription = "开票员", Length = 64)]
    [MaxLength(64)]
    public string? Invoicer { get; set; }
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnDescription = "状态")]
    public StatusEnum Status { get; set; } = StatusEnum.Enable;

    /// <summary>
    /// 机构子项
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<SysOrg> Children { get; set; }

    /// <summary>
    /// 税务区域
    /// </summary>
    [SugarColumn(ColumnDescription = "税务区域", Length = 64)]
    public string Prov { get; set; }

    /// <summary>
    /// 角色ID
    /// </summary>
    [SugarColumn(IsIgnore = true, ColumnDescription = "角色ID")]
    public long? SysRoleId { get; set; }
}