﻿<template>
  <div class="warehousePurchaseMX-container">
    <el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="商品">
          <el-select clearable="" filterable="" v-model="queryParams.goodsId" placeholder="请选择商品">
            <el-option v-for="(item, index) in warehousegoodsDropdownList" :key="index" :value="item.value"
              :label="item.label" />

          </el-select>

        </el-form-item>
        <el-form-item label="供应商">
          <el-select clearable="" filterable="" v-model="queryParams.supplierId" placeholder="请选择供应商">
            <el-option v-for="(item, index) in pubSupplierDropdownList" :key="index" :value="item.value"
              :label="item.label" />

          </el-select>

        </el-form-item>
        <el-form-item>
          <el-button-group>
            <el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'warehousePurchaseMX:page'"> 查询
            </el-button>
            <el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>

          </el-button-group>

        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="ele-Plus" @click="openAddWarehousePurchaseMX"
            v-auth="'warehousePurchaseMX:add'"> 新增 </el-button>

        </el-form-item>

      </el-form>
      
      <el-table :data="tableData" style="width: 100%" v-loading="loading" tooltip-effect="light" row-key="id" border="">
        <el-table-column type="index" label="序号" width="55" align="center" fixed="" />
        <el-table-column prop="goodsId" label="商品" fixed="" show-overflow-tooltip="">
          <template #default="scope">
            <span>{{ scope.row.warehousegoodsName }}</span>

          </template>

        </el-table-column>

        <el-table-column prop="unit" label="单位" fixed="" show-overflow-tooltip="" />
        <el-table-column prop="puchQty" label="采购数量" fixed="" show-overflow-tooltip="" />
        <el-table-column prop="puchPrice" label="采购单价" fixed="" show-overflow-tooltip="" />
        <el-table-column prop="puchAmt" label="采购金额" fixed="" show-overflow-tooltip="" />
        <el-table-column prop="rcvQty" label="入库数量" fixed="" show-overflow-tooltip="" />
        <!-- <el-table-column prop="supplierId" label="供应商" fixed="" show-overflow-tooltip="">
          <template #default="scope">
            <span>{{ scope.row.pubSupplierName }}</span>

          </template>

        </el-table-column> -->
        <el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip=""
          v-if="auth('warehousePurchaseMX:edit') || auth('warehousePurchaseMX:delete')">
          <template #default="scope">
            <el-button icon="ele-Edit" size="small" text="" type="primary" @click="openEditWarehousePurchaseMX(scope.row)"
              v-auth="'warehousePurchaseMX:edit'"> 编辑 </el-button>
            <el-button icon="ele-Delete" size="small" text="" type="primary" @click="delWarehousePurchaseMX(scope.row)"
              v-auth="'warehousePurchaseMX:delete'"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-model:currentPage="tableParams.page" v-model:page-size="tableParams.pageSize"
        :total="tableParams.total" :page-sizes="[10, 20, 50, 100]" small="" background="" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" layout="total, sizes, prev, pager, next, jumper" />
      <editDialog ref="editDialogRef" :title="editWarehousePurchaseMXTitle" @reloadTable="handleQuery" />
    </el-card>
  </div>
</template>

<script lang="ts" setup="" name="warehousePurchaseMX">
import { ref } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { auth } from '/@/utils/authFunction';
//import { formatDate } from '/@/utils/formatTime';

import editDialog from '/@/views/main/warehousePurchaseMX/component/editDialog.vue'
import { pageWarehousePurchaseMX, deleteWarehousePurchaseMX } from '/@/api/main/warehousePurchaseMX';
// import { getWarehousegoodsDropdown } from '/@/api/main/warehousePurchaseMX';
import { WarehousegoodsDropdown } from '/@/api/main/warehousegoods';
// import { getPubSupplierDropdown } from '/@/api/main/warehousePurchaseMX';
import {PubSupplierDropdown } from '/@/api/main/pubSupplier';
import { useGoodsStore } from '/@/stores/goods';


const editDialogRef = ref();
const loading = ref(false);
const tableData = ref<any>
  ([]);
const queryParams = ref<any>
  ({});
const tableParams = ref({
  page: 1,
  pageSize: 10,
  total: 0,
});
const editWarehousePurchaseMXTitle = ref("");

const goodsStore = useGoodsStore();

// 查询操作
const handleQuery = async () => {
  loading.value = true;
  var res = await pageWarehousePurchaseMX(Object.assign(queryParams.value, tableParams.value));
  tableData.value = res.data.result?.items ?? [];
  tableParams.value.total = res.data.result?.total;
  loading.value = false;
};
// 重置查询条件
const resetQuery = () => {
  queryParams.value = {};
  handleQuery();
};
// 打开新增页面
const openAddWarehousePurchaseMX = () => {
  editWarehousePurchaseMXTitle.value = '添加商品采购明细';
  editDialogRef.value.openDialog({});
};

// 打开编辑页面
const openEditWarehousePurchaseMX = (row: any) => {
  editWarehousePurchaseMXTitle.value = '编辑商品采购明细';
  editDialogRef.value.openDialog(row);
};

// 删除
const delWarehousePurchaseMX = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      await deleteWarehousePurchaseMX(row);
      handleQuery();
      ElMessage.success("删除成功");
    })
    .catch(() => { });
};

// 改变页面容量
const handleSizeChange = (val: number) => {
  tableParams.value.pageSize = val;
  handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
  tableParams.value.page = val;
  handleQuery();
};

const warehousegoodsDropdownList = ref<any>([]);
  const getWarehousegoodsDropdownList = async () => {
	await goodsStore.fetchGoodsList();
	warehousegoodsDropdownList.value = goodsStore.dropdownList;
};
getWarehousegoodsDropdownList();

const pubSupplierDropdownList = ref<any>([]);
const getPubSupplierDropdownList = async () => {
  let list = await PubSupplierDropdown();
  pubSupplierDropdownList.value = list.data.result ?? [];
};
getPubSupplierDropdownList();


handleQuery();
</script>


