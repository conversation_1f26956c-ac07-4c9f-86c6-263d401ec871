// 模拟用户信息 - 用于第三方登录演示模式
export const mockUserInfo = {
  id: 1,
  account: 'demo',
  realName: '演示用户',
  phone: '***********',
  idCardNum: '',
  email: '<EMAIL>',
  accountType: 1,
  avatar: '/favicon.ico',
  address: '演示地址',
  signature: '',
  orgId: 1,
  orgName: '演示组织',
  posName: '演示职位',
  roles: ['common'],
  authBtnList: [],
  tenantId: 1,
  prov: '',
  dpPassword: '',
  dpUserName: '',
  nsrsbh: '',
  time: new Date().getTime(),
};

// 检查是否是Mock Token
export function isMockToken(token: string): boolean {
  // Mock Token 的特征
  return !!(token && (
    token.includes('mock_token_string_for_testing_purpose') ||
    token.startsWith('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************')
  ));
} 