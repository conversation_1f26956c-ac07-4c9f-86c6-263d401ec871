﻿namespace Admin.NET.Application;

/// <summary>
/// 入库单输出参数
/// </summary>
public class WarehouseInrecordDto
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 入库单号
    /// </summary>
    public string OrderNumber { get; set; }

    /// <summary>
    /// 采购单号
    /// </summary>
    public string? PurchaseNumber { get; set; }

    /// <summary>
    /// 入库类型
    /// </summary>
    public RcvStatusEnum InhouseType { get; set; }

    /// <summary>
    /// 入库状态
    /// </summary>
    public RcvStatusEnum InhouseStatus { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    /// <summary>
    /// 仓库ID
    /// </summary>
    public string? Warehouseid { get; set; }

}
