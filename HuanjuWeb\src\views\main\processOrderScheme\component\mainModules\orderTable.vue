<template>
    <table-panel ref="tablePanelRef" :table-data="tableData" :columns="tableColumns" :loading="loading"
        :table-params="props.pageParams" @selection-change="handleSelectionChange" @row-click="handleRowClick"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" @row-dblclick="handleRowDblclick"
        @expand-change="handleExpandChange" style="height: 100%; margin-top: 8px;">
        <!-- 自定义插槽示例 -->
        <template #status="{ row }">
            <el-tag type="success" v-if="row.status === 1">启用</el-tag>
            <el-tag type="danger" v-else>停用</el-tag>
        </template>

        <template #operation="{ row }">
            <el-button icon="ele-Edit" size="small" text="" type="primary" @click="editOrder(row)"
                v-auth="'processOrderScheme:edit'"> 编辑 </el-button>
            <el-button icon="ele-Delete" size="small" text="" type="primary" @click="deleteOrder(row)"
                v-auth="'processOrderScheme:delete'"> 删除 </el-button>
        </template>

        <template #expand="{ row }">
            <el-card class="top-Card" shadow="hover" style="height: 55vh;"
                body-style="padding-top: 0px; height: 100%; background-color: #f5f7fa;">
                <el-tabs v-model="activeTab">
                    <el-tab-pane label="原料列表" name="原料列表">
                        <material-table :table-data="row.materialList || []" />
                    </el-tab-pane>
                    <el-tab-pane label="产出列表" name="产出列表">
                        <produce-table :table-data="row.produceList || []" />
                    </el-tab-pane>
                </el-tabs>
            </el-card>
        </template>
    </table-panel>
</template>

<script setup lang="ts">
import tablePanel from '/@/components/tablePanel/index.vue';
import materialTable from '/@/views/main/processOrderScheme/component/mainModules/materialTable.vue';
import produceTable from '/@/views/main/processOrderScheme/component/mainModules/produceTable.vue';
import { ref, PropType, nextTick } from 'vue';
import { OrderTableData } from '/@/views/main/processOrderScheme/types/type';
import { getOneProcessOrderScheme, deleteProcessOrderScheme } from '/@/api/main/processOrderScheme';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['handleQuery', 'update:pageParams', 'handleRowClick', 'editOrder', 'deleteOrder']);

const tableData = ref<OrderTableData[]>([]);
const loading = ref(false);
const tablePanelRef = ref();
const expandedRow = ref<OrderTableData | null>(null);

const props = defineProps({
    pageParams: {
        type: Object as PropType<PageParams>,
        required: true
    }
});

// 表格列配置
const tableColumns: TableColumn[] = [
    { type: 'expand', label: '展开', width: 40, slot: 'expand' },
    { type: 'index', label: '序号' },
    // { prop: 'schemeNo', label: '方案编号', showOverflowTooltip: true },
    { prop: 'schemeName', label: '方案名称', showOverflowTooltip: true },
    // { prop: 'status', label: '状态', slot: 'status', showOverflowTooltip: true },
    { prop: 'materialWarehouse.name', label: '原料仓库', showOverflowTooltip: true },
    { prop: 'produceWarehouse.name', label: '成品仓库', showOverflowTooltip: true },
    { prop: 'createUser.realName', label: '创建人', showOverflowTooltip: true },
    { prop: 'createTime', label: '创建时间', showOverflowTooltip: true },
    { prop: 'updateTime', label: '更新时间', showOverflowTooltip: true },
    { type: 'operation', label: '操作', width: 140, fixed: 'right' }
];

const handleSelectionChange = (selection: any) => {
    console.log('选中项：', selection);
};

const handleRowClick = (row: OrderTableData) => {
    emit('handleRowClick', row);
};

const handleSizeChange = (val: number) => {
    emit('update:pageParams', { page: props.pageParams.page, pageSize: val });
    emit('handleQuery');
};

const handleCurrentChange = (val: number) => {
    emit('update:pageParams', { page: val, pageSize: props.pageParams.pageSize });
    emit('handleQuery');
};

// 处理行展开/收起事件
const handleExpandChange = (row: OrderTableData, expandedRows: OrderTableData[]) => {
    console.log('展开行：', row, expandedRows);
    if (expandedRows.length > 0) {
        // 关闭之前的行
        if (expandedRow.value) {
            tablePanelRef.value.toggleRowExpansion(expandedRow.value, false);
        }

        expandedRow.value = row;
        // 如果展开行没有数据，加载数据
        if (!row.materialList || !row.produceList) {
            loadExpandedRowData(row);
        }

        // 滚动到展开行位置
        nextTick(() => {
            scrollToExpandedRow(row);
        });
    } else {
        expandedRow.value = null;
    }
};

// 加载展开行的数据
const loadExpandedRowData = async (row: OrderTableData) => {
    // 模拟API调用获取原料数据
    const orderInfo = await getOneProcessOrderScheme({ id: row.id }).then((res: any) => {
        return res.data.result;
    });

    // 直接修改行数据，添加materialList和outputList属性
    row.materialList = orderInfo.materialList;
    row.produceList = orderInfo.produceList;

    // 激活"原料列表"选项卡
    activeTab.value = '原料列表';
};

// 滚动到展开行位置
const scrollToExpandedRow = (row: OrderTableData) => {
    if (tablePanelRef.value && tablePanelRef.value.$refs.taskTableRef) {
        const offsetHeight = tablePanelRef.value?.$refs.taskTableRef?.$refs.tableBody.querySelector('tbody').firstElementChild.offsetHeight;
        const index = tableData.value.indexOf(row);
        const targetIndex = index * offsetHeight;
        tablePanelRef.value.$refs.taskTableRef.setScrollTop(targetIndex);
    }
};

// 处理行双击
const handleRowDblclick = (row: OrderTableData) => {
    console.log('行双击：', row);

    // 如果当前展开的是点击的行，则收起
    if (expandedRow.value && expandedRow.value === row) {
        expandedRow.value = null;
        // 通过表格组件的API设置展开行
        if (tablePanelRef.value) {
            tablePanelRef.value.toggleRowExpansion(row, false);
        }
    } else {
        // 否则展开当前行
        // 关闭之前的行
        if (expandedRow.value) {
            tablePanelRef.value.toggleRowExpansion(expandedRow.value, false);
        }

        if (tablePanelRef.value) {
            tablePanelRef.value.toggleRowExpansion(row, true);
            expandedRow.value = row;
        }

        // 如果展开行没有数据，加载数据
        if (!row.materialList || !row.produceList) {
            loadExpandedRowData(row);
        }

        // 滚动到展开行位置
        nextTick(() => {
            scrollToExpandedRow(row);
        });
    }
};

const editOrder = (row: any) => {
    emit('editOrder', row);
};

const deleteOrder = (row: any) => {
    deleteProcessOrderScheme(row).then((res: any) => {
        ElMessage.success('删除成功');
        emit('deleteOrder', row);
    });
};

const setTableData = (data: any) => {
    tableData.value = data;
};

const activeTab = ref('原料列表');

defineExpose({
    setTableData,
    // 暴露toggleRowExpansion方法，方便父组件调用
    toggleRowExpansion: (row?: OrderTableData, expanded?: boolean) => {
        if (tablePanelRef.value) {
            tablePanelRef.value.toggleRowExpansion(row || expandedRow.value, expanded || false);
        }
    }
});
</script>

<style scoped></style>