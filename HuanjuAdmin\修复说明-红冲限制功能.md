# 红冲限制功能实现说明

## 功能概述

根据业务需求，对出入库记录的红冲功能添加了限制，确保：
1. **红冲过的出入库记录不允许再次红冲**
2. **本身是红冲的记录，不允许红冲**

## 主要修改内容

### 1. 数据库结构修改

#### 1.1 实体类修改
**修改文件**：
- `HuanjuAdmin/Admin.NET.Application/Entity/OutboundRecord.cs`
- `HuanjuAdmin/Admin.NET.Application/Entity/InboundRecord.cs`

**新增字段**：
```csharp
/// <summary>
/// 红冲状态：0-正常记录，1-已被红冲，2-红冲记录
/// </summary>
[SugarColumn(ColumnDescription = "红冲状态")]
public int RedInkStatus { get; set; } = 0;

/// <summary>
/// 原始记录ID（红冲记录关联的原始记录）
/// </summary>
[SugarColumn(ColumnDescription = "原始记录ID")]
public long? OriginalRecordId { get; set; }
```

#### 1.2 视图实体修改
**修改文件**：
- `HuanjuAdmin/Admin.NET.Application/Entity/OutInBoundRecord.cs`

**新增字段**：
```csharp
/// <summary>
/// 红冲状态：0-正常记录，1-已被红冲，2-红冲记录
/// </summary>
[SugarColumn(ColumnDescription = "红冲状态")]
public int RedInkStatus { get; set; } = 0;
```

#### 1.3 DTO修改
**修改文件**：
- `HuanjuAdmin/Admin.NET.Application/Service/OutboundRecord/Dto/OutInBoundOutput.cs`

**新增字段**：
```csharp
/// <summary>
/// 红冲状态：0-正常记录，1-已被红冲，2-红冲记录
/// </summary>
public int RedInkStatus { get; set; } = 0;

/// <summary>
/// 红冲状态描述
/// </summary>
public string RedInkStatusDesc => RedInkStatus switch
{
    0 => "正常",
    1 => "已红冲",
    2 => "红冲记录",
    _ => "未知"
};

/// <summary>
/// 是否可以红冲
/// </summary>
public bool CanRedInk => RedInkStatus == 0 && !OrderNum?.Contains("-红冲") == true && OutInCount > 0;
```

### 2. 后端逻辑修改

#### 2.1 红冲验证逻辑
**修改文件**：
- `HuanjuAdmin/Admin.NET.Application/Service/OutboundRecord/OutboundRecordService.cs`

**出库红冲验证**：
```csharp
// 验证红冲限制
// 1. 检查是否已经被红冲过
if (outboundRecord.RedInkStatus == 1)
    throw Oops.Oh("该出库记录已经被红冲过，不允许再次红冲");

// 2. 检查本身是否是红冲记录
if (outboundRecord.RedInkStatus == 2)
    throw Oops.Oh("红冲记录不允许再次红冲");

// 3. 兼容性检查：通过流水号和数量判断（用于没有RedInkStatus字段的旧数据）
if (outboundRecord.OrderNum?.Contains("-红冲") == true)
    throw Oops.Oh("红冲记录不允许再次红冲");

if (outboundRecord.OutBoundCount < 0)
    throw Oops.Oh("红冲记录不允许再次红冲");
```

**入库红冲验证**：
```csharp
// 验证红冲限制
// 1. 检查是否已经被红冲过
if (inboundRecord.RedInkStatus == 1)
    throw Oops.Oh("该入库记录已经被红冲过，不允许再次红冲");

// 2. 检查本身是否是红冲记录
if (inboundRecord.RedInkStatus == 2)
    throw Oops.Oh("红冲记录不允许再次红冲");

// 3. 兼容性检查：通过流水号和数量判断（用于没有RedInkStatus字段的旧数据）
if (inboundRecord.OrderNum?.Contains("-红冲") == true)
    throw Oops.Oh("红冲记录不允许再次红冲");

if (inboundRecord.InBoundCount < 0)
    throw Oops.Oh("红冲记录不允许再次红冲");
```

#### 2.2 红冲状态更新逻辑
**红冲记录创建时**：
```csharp
// 创建红冲记录时设置状态
var redInkRecord = new OutboundRecord
{
    // ... 其他字段
    RedInkStatus = 2, // 标记为红冲记录
    OriginalRecordId = originalRecordId // 关联原始记录ID
};

// 标记原记录为已被红冲
originalRecord.RedInkStatus = 1;
await _rep.UpdateAsync(originalRecord);
```

### 3. 前端界面修改

#### 3.1 表格展示
**修改文件**：
- `HuanjuWeb/src/views/main/OutInBound/index.vue`

**新增红冲状态列**：
```vue
<el-table-column prop="redInkStatus" label="红冲状态" width="100" show-overflow-tooltip>
    <template #default="scope">
        <el-tag 
            :type="getRedInkStatusTagType(scope.row)" 
            size="small">
            {{ getRedInkStatusText(scope.row) }}
        </el-tag>
    </template>
</el-table-column>
```

#### 3.2 红冲按钮验证
**红冲前检查逻辑**：
```javascript
// 检查红冲限制
const invalidRecords = selectedRows.value.filter(row => {
    // 1. 检查是否已经被红冲过
    if (row.redInkStatus === 1) {
        return true;
    }
    // 2. 检查本身是否是红冲记录
    if (row.redInkStatus === 2) {
        return true;
    }
    // 3. 兼容性检查：通过流水号和数量判断
    if (row.orderNum?.includes('-红冲')) {
        return true;
    }
    if (row.outInCount < 0) {
        return true;
    }
    return false;
});

if (invalidRecords.length > 0) {
    const invalidTypes = invalidRecords.map(row => {
        if (row.redInkStatus === 1) return '已被红冲';
        if (row.redInkStatus === 2) return '红冲记录';
        if (row.orderNum?.includes('-红冲')) return '红冲记录';
        if (row.outInCount < 0) return '红冲记录';
        return '不可红冲';
    });
    ElMessage.warning(`选中的记录中包含${invalidTypes.join('、')}的记录，不允许红冲`);
    return;
}
```

#### 3.3 状态显示函数
```javascript
// 获取红冲状态文本
const getRedInkStatusText = (row: any): string => {
    // 优先使用redInkStatus字段
    if (row.redInkStatus !== undefined && row.redInkStatus !== null) {
        switch (row.redInkStatus) {
            case 0: return '正常';
            case 1: return '已红冲';
            case 2: return '红冲记录';
            default: return '未知';
        }
    }
    
    // 兼容性检查：通过流水号和数量判断
    if (row.orderNum?.includes('-红冲')) {
        return '红冲记录';
    }
    if (row.outInCount < 0) {
        return '红冲记录';
    }
    
    return '正常';
};

// 获取红冲状态标签类型
const getRedInkStatusTagType = (row: any): string => {
    const statusText = getRedInkStatusText(row);
    switch (statusText) {
        case '正常': return 'success';
        case '已红冲': return 'warning';
        case '红冲记录': return 'danger';
        default: return 'info';
    }
};
```

### 4. 数据库迁移

#### 4.1 迁移脚本
**文件**：`HuanjuAdmin/Database/Migration/AddRedInkStatusFields.sql`

**主要内容**：
1. 为`outboundrecord`和`inboundrecord`表添加`RedInkStatus`和`OriginalRecordId`字段
2. 更新现有数据的红冲状态（根据流水号和数量判断）
3. 标记被红冲的原始记录
4. 重新创建`View_OutInBound`视图，包含红冲状态字段
5. 验证数据迁移结果

#### 4.2 兼容性处理
- 支持新旧数据的兼容性
- 对于没有`RedInkStatus`字段的旧数据，通过流水号和数量进行判断
- 确保现有功能不受影响

## 红冲状态说明

### 状态值定义
- **0 - 正常记录**：可以进行红冲操作的正常出入库记录
- **1 - 已被红冲**：已经被红冲过的原始记录，不允许再次红冲
- **2 - 红冲记录**：红冲操作产生的记录，不允许再次红冲

### 业务流程
1. **正常出入库**：创建记录时`RedInkStatus = 0`
2. **执行红冲**：
   - 验证原记录是否可以红冲（`RedInkStatus = 0`）
   - 创建红冲记录（`RedInkStatus = 2`，`OriginalRecordId`指向原记录）
   - 更新原记录状态（`RedInkStatus = 1`）
3. **再次红冲**：系统拒绝，提示相应错误信息

### 界面展示
- **正常**：绿色标签，表示可以红冲
- **已红冲**：橙色标签，表示已被红冲，不可再次红冲
- **红冲记录**：红色标签，表示本身是红冲记录，不可红冲

## 测试建议

### 1. 功能测试
1. 测试正常记录的红冲操作
2. 测试已被红冲记录的再次红冲（应被阻止）
3. 测试红冲记录的红冲操作（应被阻止）
4. 测试前端界面的状态显示

### 2. 兼容性测试
1. 测试现有旧数据的显示和操作
2. 测试升级后的数据一致性
3. 测试新旧数据混合的场景

### 3. 边界测试
1. 测试流水号包含"-红冲"的记录
2. 测试负数数量的记录
3. 测试空值和异常数据的处理

## 部署说明

1. **数据库迁移**：执行`AddRedInkStatusFields.sql`脚本
2. **代码部署**：部署后端和前端代码
3. **数据验证**：检查现有数据的红冲状态是否正确
4. **功能验证**：测试红冲限制功能是否正常工作

## 影响评估

### 正面影响
- ✅ 防止重复红冲，保证数据准确性
- ✅ 清晰的状态显示，提升用户体验
- ✅ 完善的业务逻辑，符合财务规范

### 风险控制
- ✅ 兼容性处理，不影响现有功能
- ✅ 渐进式验证，确保数据安全
- ✅ 详细的错误提示，便于用户理解

## 编译状态
✅ **编译成功** - 所有修改已通过编译验证

## 功能状态
✅ **功能完成** - 红冲限制功能已完整实现并可投入使用 