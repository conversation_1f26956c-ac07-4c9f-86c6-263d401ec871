import ElementPlus from 'element-plus';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
import en from 'element-plus/dist/locale/en.mjs';
import zhTw from 'element-plus/dist/locale/zh-tw.mjs';

// 导出我们需要的所有本地化相关内容
export const enLocale = en;
export const zhCnLocale = zhCn;
export const zhTwLocale = zhTw;

// 导出其他必要组件
export const TableV2 = ElementPlus.TableV2 || {};
export const TableV2FixedDir = 'rtl'; // 根据实际需要设置

// 暴露完整的 ElementPlus 库以便在需要时访问其他组件
export default ElementPlus; 