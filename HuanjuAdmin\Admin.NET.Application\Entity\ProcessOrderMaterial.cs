﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("processordermaterial","")]
    [Tenant("1300000000001")]
    public class ProcessOrderMaterial  : EntityBaseId
    {
        /// <summary>
        /// 加工单Id
        /// </summary>
        [SugarColumn(ColumnDescription = "加工单Id")]
        public long? ProcessOrderId { get; set; }
        /// <summary>
        /// 物料Id
        /// </summary>
        [SugarColumn(ColumnDescription = "物料Id")]
        public long? WarehousegoodsId { get; set; }

        /// <summary>
        /// 商品
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(WarehousegoodsId))]
        public Warehousegoods Warehousegoods { get; set; }

        /// <summary>
        /// 基准数量
        /// </summary>
        [SugarColumn(ColumnDescription = "基准数量")]
        public decimal? BaseQuantity { get; set; }
        /// <summary>
        /// 实际数量
        /// </summary>
        [SugarColumn(ColumnDescription = "实际数量")]
        public decimal? ActQuantity { get; set; }
        /// <summary>
        /// 原料单价
        /// </summary>
        [SugarColumn(ColumnDescription = "原料单价")]
        public decimal? UnitPrice { get; set; }
        /// <summary>
        /// 原料总价
        /// </summary>
        [SugarColumn(ColumnDescription = "原料总价")]
        public decimal? TotalPrice { get; set; }
    }
}