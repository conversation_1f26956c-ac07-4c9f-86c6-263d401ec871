﻿<template>
	<div class="salesContract-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="1200" draggable="" :close-on-click-modal="false">
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" :rules="rules">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="7" :md="7" :lg="7" :xl="7" class="mb20">
						<el-form-item label="销售人" prop="salesperson">
							<el-select class="col_width" clearable filterable v-model="ruleForm.salesperson"
								placeholder="请选择销售人" :disabled="ruleForm.id > 0">
								<el-option v-for="item in employeeList" :key="item.id" :label="item.realName"
									:value="item.realName" />
							</el-select>
							<!-- <el-input v-model="ruleForm.salesperson" placeholder="请输入销售人" clearable /> -->
						</el-form-item>
					</el-col>
					<!-- <el-col :xs="24" :sm="7" :md="7" :lg="7" :xl="7" class="mb20" v-if="props.title == '编辑销售合约'">
						<el-form-item label="销售单号" prop="salesOrder">
							<el-input v-model="ruleForm.salesOrder" placeholder="请输入销售单号" disabled="true" clearable />
						</el-form-item>
					</el-col> -->
					<el-col :sm="7" :md="7" :lg="7" :xl="7" class="mb20">
						<el-form-item label="合同编码" prop="contractCode">
							<el-input v-model="ruleForm.contractCode" placeholder="请输入合同编码" clearable />
						</el-form-item>
					</el-col>
					<!-- <el-col :xs="24" :sm="10" :md="10" :lg="10" :xl="10" class="mb20">
						<el-form-item label="合同金额" prop="contractAmount">
							<el-input v-model="ruleForm.contractAmount" placeholder="请输入合同金额" clearable disabled />
						</el-form-item>
					</el-col> -->
					<el-col :xs="24" :sm="7" :md="7" :lg="7" :xl="7" class="mb20">
						<el-form-item label="客户名称" prop="customerName">
							<el-select class="col_width" v-model="ruleForm.customerNameId"
								@change="getcustomerName(ruleForm, ruleForm.customerNameId)" placeholder="请选择客户">
								<el-option v-for="(item, index) in PubcustomList" :key="index" :value="item.value"
									:label="item.label" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="7" :md="7" :lg="7" :xl="7" class="mb20">
						<el-form-item label="联系人" prop="contacts">
							<el-input v-model="ruleForm.contacts" placeholder="请输入联系人" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="7" :md="7" :lg="7" :xl="7" class="mb20">
						<el-form-item label="电话" prop="tel">
							<el-input v-model="ruleForm.tel" placeholder="请输入电话" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="7" :md="7" :lg="7" :xl="7" class="mb20">
						<el-form-item label="地址" prop="address">
							<el-input v-model="ruleForm.address" placeholder="请输入地址" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="7" :md="7" :lg="7" :xl="7" class="mb20">
						<el-form-item label="备注" prop="notes">
							<el-input v-model="ruleForm.notes" placeholder="请输入备注" clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>

			<!-- 修改按钮和金额显示的布局 -->
			<div class="mb10 flex justify-between items-center">
				<div class="flex items-center gap-4">
					<!-- 左侧放置按钮 -->
					<el-button type="primary" icon="ele-Plus" @click="addrkdDetail"> 新增商品明细 </el-button>
				</div>
				<div class="flex items-center gap-6">
					<!-- 右侧放置金额信息 -->
					<div class="amount-item">
						<span class="amount-label">总金额：</span>
						<span class="amount-value">{{ (ruleForm.totalAmt || 0).toFixed(2) }}</span>
					</div>
					<div class="amount-item">
						<span class="amount-label">优惠金额：</span>
						<el-input-number v-model="ruleForm.discountAmt" :min="0" :precision="2"
							:max="ruleForm.totalAmt || 0" @change="updateActualAmount" :controls="false"
							class="compact-input" />
					</div>
					<div class="amount-item">
						<span class="amount-label">合同金额：</span>
						<el-input-number v-model="ruleForm.contractAmount" :min="0" :precision="2"
							:max="ruleForm.totalAmt || 0" @change="updateDiscountAmount" :controls="false"
							class="compact-input" />
					</div>
				</div>
			</div>

			<el-table :data="tableData.filter(row => !row.isDelete)" style="width: 100%" tooltip-effect="light">
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="goodsId" label="商品" width="150" show-overflow-tooltip="">
					<template #default="scope">
						<el-select clearable filterable v-model="scope.row.goodsId" placeholder="请选择商品"
							@change="getGoodsDetail(scope.row, scope.row.goodsId)">
							<el-option v-for="(item, index) in warehousegoodsDropdownList" :key="index"
								:value="item.value" :label="item.label" />
						</el-select>
					</template>
				</el-table-column>
				<el-table-column prop="productCode" label="商品编码" show-overflow-tooltip="" width="109" />
				<el-table-column prop="brandName" label="品牌" show-overflow-tooltip="" width="109" />
				<el-table-column prop="specsName" label="规格" show-overflow-tooltip="" width="109" />
				<el-table-column prop="unit" label="单位" show-overflow-tooltip="">
					<template #default="scope">
						<el-select clearable filterable disabled="" v-model="scope.row.unit" placeholder=" ">
							<el-option v-for="(item, index) in WarehouseUnit" :key="index" :value="item.value"
								:label="item.label" />
						</el-select>
					</template>
				</el-table-column>

				<el-table-column prop="puchQty" label="数量" show-overflow-tooltip="" width="138">
					<template #default="scope">
						<el-input-number v-model="scope.row.puchQty" placeholder="" clearable :min="0"
							@change="updateRowTotal(scope.row, 'puchQty')" class="w-full" />
					</template>
				</el-table-column>
				<el-table-column prop="puchPrice" label="单价" width="138" show-overflow-tooltip="">
					<template #default="scope">
						<el-input-number v-model="scope.row.puchPrice" placeholder="" type="number" :min="0"
							@change="updateRowTotal(scope.row, 'puchPrice')" class="w-full" />
					</template>
				</el-table-column>
				<el-table-column prop="puchAmt" label="销售金额" width="138" show-overflow-tooltip="">
					<template #default="scope">
						<el-input-number v-model="scope.row.puchAmt" placeholder="" :min="0" :precision="2"
							@change="updateRowTotal(scope.row, 'puchAmt')" class="w-full" />
					</template>
				</el-table-column>

				<el-table-column label="操作" width="70" align="center" fixed="right" show-overflow-tooltip="">
					<template #default="scope">
						<el-button icon="ele-Delete" size="small" text="" type="primary"
							@click="deletePurchaseDetail(scope.$index)" v-auth="'warehousePurchaseMX:delete'"> 删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { useGoodsStore } from '/@/stores/goods';
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormRules } from 'element-plus';
import { addSalesContract, updateSalesContract } from '/@/api/main/salesContract';

import { pageSaleOfGoods, addSaleOfGoods, updateSaleOfGoods } from '/@/api/main/saleOfGoods';
import { Pubcustom } from '/@/api/main/pubcustom';
import { WarehousegoodsDropdown } from '/@/api/main/warehousegoods';
import { WarehouseGoodsUnit } from '/@/api/main/warehouseInrecord';
import { getUserListApi } from '/@/api/main/org';
import { debug } from 'console';

const tableData = ref<any>([[]]);
const tableDeleted = ref<any>([[]]);
const queryParams = ref<any>({});
const tableParams = ref({
	page: 1,
	pageSize: 10,
	total: 0,
});
const tableData1 = ref<any>({});
//父级传递来的函数，用于回调
const emit = defineEmits(['reloadTable']);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const loading = ref(false);
const ruleForm = ref<any>({
	discountAmt: 0, // 优惠金额
	contractAmount: 0, // 实际金额
	totalAmt: 0, // 总金额
});
//自行添加其他规则
const rules = ref<FormRules>({
	salesperson: [{ required: true, message: '请选择销售人', trigger: 'change' }],
	customerName: [{ required: true, message: '请选择客户名称', trigger: 'change' }],
});

//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: '',
	},
});

// 修改 updateTotalAmount 函数
const updateTotalAmount = () => {
	const total = tableData.value.reduce((sum, row) => sum + (parseFloat(row.puchAmt) || 0), 0);
	ruleForm.value.totalAmt = total;
	// 更新实际金额（总金额 - 优惠金额）
	ruleForm.value.contractAmount = Math.max(0, total - (ruleForm.value.discountAmt || 0));
};

// 添加优惠金额变化时的处理函数
const updateActualAmount = (value: number) => {
	ruleForm.value.discountAmt = value;
	ruleForm.value.contractAmount = Math.max(0, ruleForm.value.totalAmt - value);
};

// 添加实际金额变化时的处理函数
const updateDiscountAmount = (value: number) => {
	ruleForm.value.contractAmount = value;
	ruleForm.value.discountAmt = Math.max(0, ruleForm.value.totalAmt - value);
};

watch(
	tableData,
	() => {
		updateGoodsInfo();
	},
	{ deep: true }
);

const updateRowTotal = (row: any, field: 'puchQty' | 'puchPrice' | 'puchAmt') => {
	// 将字符串转换为数字
	const puchQty = parseFloat(row.puchQty) || 0;
	const puchPrice = parseFloat(row.puchPrice) || 0;
	const puchAmt = parseFloat(row.puchAmt) || 0;

	switch (field) {
		case 'puchQty': // 修改数量
		case 'puchPrice': // 修改单价
			// 数量和单价变化时,计算合计
			row.puchAmt = (puchQty * puchPrice).toFixed(2);
			break;

		case 'puchAmt': // 修改合计
			// 只在数量存在时,根据合计反推单价
			if (puchQty !== 0) {
				row.puchPrice = (puchAmt / puchQty).toFixed(2);
			}
			// 如果数量为0,不做任何计算,保持当前状态
			break;
	}
	updateGoodsInfo();
	updateTotalAmount();
};

// 更新商品信息到表单
const updateGoodsInfo = () => {
	ruleForm.value.goodsInfo = tableData.value
		.filter((item: any) => !item.isDelete)
		.map((item: any) => {
			const goods = warehousegoodsDropdownList.value.find((g: any) => g.value === item.goodsId);
			return goods ? goods.label : '';
		})
		.filter(Boolean)
		.join(',');
};

const goodsStore = useGoodsStore();
const addrkdDetail = () => {
	//debugger;
	tableData.value.push({
		// id: null,
		// TradeName: null,
		// ContractNum: null,
		id: null,
		goodsId: null, // or appropriate initial value
		productCode: null,
		brandName: null,
		unit: null,
		specsName: null,
		puchQty: 0,
		puchPrice: 0,
		puchAmt: '0.00',
	});
	updateTotalAmount();
};

// 打开弹窗
const openDialog = async (row: any, date: string) => {
	tableData.value = [];
	tableParams.value.total = 0;
	debugger;
	ruleForm.value = JSON.parse(JSON.stringify(row));

	if (ruleForm.value.id != null) {
		queryParams.value.salesOrder = ruleForm.value.id;
		var res = await pageSaleOfGoods(Object.assign(queryParams.value, tableParams.value));
		tableData.value = res.data.result?.items ?? [];
		tableParams.value.total = res.data.result?.total;
		updateTotalAmount();
	}
	if (date == '复制') {
		delete ruleForm.value.id;
		//row.id="";
		ruleForm.value.salesOrder ==
			'XS' + new Date().getFullYear() + (new Date().getMonth() + 1) + new Date().getDate() + Math.floor(Math.random() * 10) + Math.floor(Math.random() * 10) + Math.floor(Math.random() * 10);
	}
	if (JSON.stringify(ruleForm.value) == '{}') {
		ruleForm.value.salesOrder =
			'XS' + new Date().getFullYear() + (new Date().getMonth() + 1) + new Date().getDate() + Math.floor(Math.random() * 10) + Math.floor(Math.random() * 10) + Math.floor(Math.random() * 10);
	}
	isShowDialog.value = true;
	getYgList();
};
// const getGoodsDetail = (row: any, val: any) => {
// 	debugger;
// 	let obj = counterStore.goodsList.find((v: { value: any; }) => {
// 		return v.value == val
// 	})
// /* 	row.tradename = obj.label || ""
// 	row.brand = obj.brand
// 	row.barcode = obj.id
// 	row.productcode = obj.code
// 	row.specifications = obj.specs  */

// }
const getcustomerName = (row: any, val: any) => {
	console.log(row);
	let obj = PubcustomList.value.find((v: { value: any }) => {
		return v.value == val;
	});
	console.log(obj);
	ruleForm.value.customerNameId = obj.value;
	ruleForm.value.customerName = obj.label;
	ruleForm.value.contacts = obj.Contacts;
	ruleForm.value.tel = obj.phone;
	ruleForm.value.address = obj.address;
};
const PubcustomList = ref<any>([]); //出库类型
const getPubcustomList = async () => {
	debugger;
	var obj = await Pubcustom({});
	obj.data.result.forEach((item: any) => {
		PubcustomList.value.push({
			label: item.name,
			value: item.id,
			Contacts: item.contacts,
			Type: item.type,
			phone: item.phone,
			BankName: item.BankName,
			address: item.address,
		});
	});

	debugger;
	var a = '';
};
const employeeList = ref([]);
// 获取员工列表
const getYgList = async () => {
	const res = await getUserListApi();
	employeeList.value = res.data.result;
	console.log(res);
};
// 关闭弹窗
const closeDialog = () => {
	const currentId = ruleForm.value.id;
	emit('reloadTable', currentId);
	isShowDialog.value = false;
	setTimeout(() => {
		loading.value = false;
	}, 500);
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};

const contractStatus = ref([
	{
		id: 0,
		name: '洽谈',
	},
	{
		id: 1,
		name: '已签约',
	},
	{
		id: 2,
		name: '履约中',
	},
	{
		id: 3,
		name: '已完成',
	},
	{
		id: 4,
		name: '中止',
	},
]);

// 修改提交函数
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			loading.value = true;
			let values = ruleForm.value;

			try {
				// 处理表格数据，确保 id 不是 null 并正确处理删除的行
				const processedTableData = tableData.value
					.filter(item => {
						// 保留未删除的行或标记为删除但已存在于数据库的行
						return !item.isDelete || (item.isDelete && item.id > 0);
					})
					.map(item => {
						// 创建一个新对象，避免修改原始对象
						const newItem = { ...item };
						
						// 如果 id 是 null，则不发送此字段
						if (newItem.id === null) {
							delete newItem.id;
						}
						
						return newItem;
					});
				
				let params = {
					addSalesContractBaseInput: values,
					listMx: processedTableData
				};
				
				// 创建新合同
				await addSalesContract(params);
				
				closeDialog();
				ElMessage({
					message: ruleForm.value.id ? '更新成功' : '添加成功',
					type: 'success'
				});
			} catch (error) {
				console.error('提交失败:', error);
				ElMessage({
					message: `提交失败: ${error}`,
					type: 'error'
				});
			} finally {
				loading.value = false;
			}
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: 'error',
			});
		}
	});
};

const WarehouseUnit = ref<any>([]);
const WareUnit = async () => {
	debugger;
	var res = await WarehouseGoodsUnit();
	WarehouseUnit.value = res.data.result ?? [];
};
WareUnit();
// 商品信息
const warehousegoodsDropdownList = ref<any>([]);
const getWarehousegoodsDropdownList = async () => {
	await goodsStore.fetchGoodsList();
	warehousegoodsDropdownList.value = goodsStore.dropdownList;
};
getWarehousegoodsDropdownList();

const getGoodsDetail = (row: any, val: any) => {
	let obj = goodsStore.goodsList.find((v: { value: any }) => {
		return v.value == val;
	});
	row.tradename = obj?.label || '';
	row.brandName = obj?.brand;
	row.barcode = obj?.id;
	row.productCode = obj?.code;
	row.specsName = obj?.specs;
	row.unit = obj?.unit || '';
	row.auxiliaryunit = obj?.auxiliaryunit || '';
	updateGoodsInfo();
};
// const recalculateTotalAmt = () => {
// 	ruleForm.value.contractAmount = tableData.value.reduce((total: number, row: any) => total + (row.puchAmt || 0), 0);
// };

// const recalculateAmount = (row: any) => {
// 	row.puchPrice = row.puchAmt / row.puchQty;
// 	updateTotalAmount();
// };
const deletePurchaseDetail = (index: number) => {
	const deleteRow = tableData.value[index];
  
	if (deleteRow.id > 0) {
		// 已存在的记录，仅标记为删除但不从数组中移除
		deleteRow.isDelete = true;
		// 可以选择在UI中隐藏这一行
	} else {
		// 新增的记录，直接从数组中移除
		tableData.value.splice(index, 1);
	}
  
	updateTotalAmount();
};
// 页面加载时
onMounted(async () => {
	await goodsStore.fetchGoodsList();
	getPubcustomList();
});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>



<style lang="scss" scoped>
.el-form-item--default {
	margin-bottom: 10px;
}

.flex {
	display: flex;
}

.justify-between {
	justify-content: space-between;
}

.items-center {
	align-items: center;
}

.text-lg {
	font-size: 1.125rem;
}

.font-bold {
	font-weight: 700;
}

.amount-item {
	display: flex;
	align-items: center;

	.amount-label {
		white-space: nowrap;
		color: #606266;
		margin-right: 8px;
	}

	.amount-value {
		font-size: 1.125rem;
		font-weight: 600;
		color: #409eff;
	}
}

.compact-input {
	width: 120px;

	:deep(.el-input-number__decrease),
	:deep(.el-input-number__increase) {
		display: none;
	}

	:deep(.el-input__wrapper) {
		padding: 0 8px;
	}

	:deep(.el-input__inner) {
		text-align: right;
	}
}

.gap-4 {
	gap: 1rem;
}

.gap-6 {
	gap: 1.5rem;
}

.col_width {
	width: 100%;
}
</style>
