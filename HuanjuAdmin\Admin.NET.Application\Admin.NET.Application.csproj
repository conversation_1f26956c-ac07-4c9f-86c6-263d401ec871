﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <NoWarn>1701;1702;1591;8632</NoWarn>
    <DocumentationFile></DocumentationFile>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Service\SysUser\**" />
    <EmbeddedResource Remove="Service\SysUser\**" />
    <None Remove="Service\SysUser\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Admin.NET.Application.xml" />
    <None Remove="Configuration\Limit.json" />
    <None Remove="Configuration\Logging.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Configuration\Logging.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="Configuration\Limit.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Admin.NET.Core\Admin.NET.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
	  <None Update="Configuration\APIJSON.json">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	  <None Update="Configuration\App.json">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
    <None Update="Configuration\Cache.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Configuration\Enum.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Configuration\CodeGen.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Configuration\Database.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Configuration\Email.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Configuration\JWT.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Configuration\OAuth.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Configuration\OSS.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Configuration\Swagger.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Configuration\Wechat.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="ImportTemplate\供应商信息v100.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="ImportTemplate\客户信息v100.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="ImportTemplate\商品信息v100.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
