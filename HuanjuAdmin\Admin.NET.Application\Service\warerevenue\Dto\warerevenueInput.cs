﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 收支明细基础输入参数
    /// </summary>
    public class warerevenueBaseInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public virtual long Id { get; set; }
        
        /// <summary>
        /// 收支时间
        /// </summary>
        public virtual DateTime? revenueTime { get; set; }
        
        /// <summary>
        /// 来往单位
        /// </summary>
        public virtual long? contactunits { get; set; }
        
        /// <summary>
        /// 关联单号
        /// </summary>
        public virtual long? ordernumber { get; set; }
        
        /// <summary>
        /// 收支科目
        /// </summary>
        public virtual string? subject { get; set; }
        
        /// <summary>
        /// 二级科目
        /// </summary>
        public virtual string? levelsubject { get; set; }
        
        /// <summary>
        /// 收入金额
        /// </summary>
        public virtual decimal? Incomeamount { get; set; }
        
        /// <summary>
        /// 支出金额
        /// </summary>
        public virtual decimal? expenditureamount { get; set; }
        
        /// <summary>
        /// 经办人
        /// </summary>
        public virtual long? handledby { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public virtual string? notes { get; set; }
        
    }

    /// <summary>
    /// 收支明细分页查询输入参数
    /// </summary>
    public class warerevenueInput : BasePageInput
    {
        /// <summary>
        /// 收支时间
        /// </summary>
        public DateTime? revenueTime { get; set; }

        /// <summary>
        /// 收支时间范围
        /// </summary>
        public List<DateTime?> revenueTimeRange { get; set; }
        /// <summary>
        /// 来往单位
        /// </summary>
        public string contactunits { get; set; }

        /// <summary>
        /// 经办人
        public string handledbyName { get; set; }
        /// <summary>
        /// 付款类型 1收入，2支出
        /// </summary>
        public int? revenueType { get; set; }

    }

    /// <summary>
    /// 收支明细增加输入参数
    /// </summary>
    public class AddwarerevenueInput : warerevenueBaseInput
    {
    }

    /// <summary>
    /// 收支明细删除输入参数
    /// </summary>
    public class DeletewarerevenueInput : BaseIdInput
    {
    }

    /// <summary>
    /// 收支明细更新输入参数
    /// </summary>
    public class UpdatewarerevenueInput : warerevenueBaseInput
    {
    }

    /// <summary>
    /// 收支明细主键查询输入参数
    /// </summary>
    public class QueryByIdwarerevenueInput : DeletewarerevenueInput
    {

    }
