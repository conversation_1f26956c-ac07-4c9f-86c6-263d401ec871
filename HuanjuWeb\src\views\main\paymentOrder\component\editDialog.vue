﻿<template>
	<div class="paymentOrder-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="700" draggable="" :close-on-click-modal="false">
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" :rules="rules">
				<!-- <el-row>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="摘要" prop="abstract">
							<el-input v-model="ruleForm.abstract" placeholder="请输入摘要信息" clearable />

						</el-form-item>

					</el-col>
				</el-row> -->
				<el-row :gutter="35">
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="付款单号" prop="receiptNo">
							<el-input v-model="ruleForm.receiptNo" placeholder="请输入收款单号" clearable />
							
						</el-form-item>
						
					</el-col> -->
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="摘要" prop="abstract">
							<el-input v-model="ruleForm.abstract" placeholder="请输入摘要信息" clearable />

						</el-form-item>

					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="供应商" prop="unitName">
							<div style="display: flex;">
								<el-select clearable filterable v-model="ruleForm.supplierId" placeholder="">
									<el-option v-for="(item, index) in pubSupplierDropdownList" :key="index"
										:value="item.value" :label="item.label" />
								</el-select>
								<el-button class="supplier" type="primary" icon="el-icon-plus" @click="openAddPubSupplier">
								</el-button>
							</div>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="摘要" prop="abstract">
							<el-input v-model="ruleForm.abstract" placeholder="请输入摘要信息" clearable />

						</el-form-item>

					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="交易账户" prop="trading">
								<el-select clearable filterable v-model="ruleForm.trading" placeholder="请选择交易账户">
									<el-option v-for="(item, index) in tradingAccountList" :key="index"
										:value="item.value" :label="item.label" />
								</el-select>
						</el-form-item>
					</el-col>
					<!-- 					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="销售单号" prop="salesOrder">
							<el-input v-model="ruleForm.salesOrder" placeholder="请输入销售单号" clearable />
							
						</el-form-item>
						
					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="单据金额" prop="documentAmount">
							<el-input v-model="ruleForm.documentAmount" placeholder="请输入单据金额" clearable />

						</el-form-item>

					</el-col>
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="已付金额" prop="amountPaid">
							<el-input v-model="ruleForm.amountPaid" placeholder="请输入已收金额" clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="已收票金额" prop="receivedAmount">
							<el-input v-model="ruleForm.receivedAmount" placeholder="请输入已收票金额" clearable
								:disabled="!ruleForm.invoiceStatus" />

						</el-form-item>

					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="科目代码" prop="secondaryAccount">
							<div style="display: flex;">
								<el-select clearable filterable v-model="ruleForm.secondaryAccount" placeholder="">
									<el-option v-for="(item, index) in getSubjectCodeList" :key="index" :value="item.subjectCode"
									:label="item.subjectCode"></el-option>
								</el-select>
								
							</div>
						</el-form-item>
					</el-col>
					
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="科目名称">
							<el-select clearable v-model="ruleForm.expenditureCategory" placeholder="">
								<el-option v-for="(item, index) in  inhouseTypeList" :key="index" :value="item.value"
									:label="item.label"></el-option>
							</el-select>

						</el-form-item>

					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="科目名称" prop="expenditureCategory">
							<el-input v-model="ruleForm.expenditureCategory" placeholder="请输入科目名称" clearable readonly/>

						</el-form-item>

					</el-col>
					<!-- 					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="收入类型" prop="incomeType">
							<el-input-number v-model="ruleForm.incomeType" placeholder="请输入收入类型" clearable />
							
						</el-form-item>
						
					</el-col> -->
					<!-- 					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="支出名目" prop="incomeCategory">
							<el-input-number v-model="ruleForm.incomeCategory" placeholder="请输入收入名目" clearable />
							
						</el-form-item>
						
					</el-col> -->
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="科目代码">
							<el-select clearable v-model="ruleForm.secondaryAccount" placeholder="请选择科目代码">
								<el-option v-for="(item, index) in  inhouseCategory" :key="index" :value="item.value"
									:label="item.label"></el-option>

							</el-select>

						</el-form-item>

					</el-col> -->
					<!-- 					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="交易方式" prop="trading">
							<el-input-number v-model="ruleForm.trading" placeholder="请输入交易方式" clearable />
							
						</el-form-item>
						
					</el-col> -->

					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="交易账户">
							<el-select clearable v-model="ruleForm.trading" placeholder="请选择交易方式">
								<el-option v-for="(item, index) in  inhousetrading" :key="index" :value="item.value"
									:label="item.label"></el-option>

							</el-select>

						</el-form-item>

					</el-col> -->

					<!-- 					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="发票状态" prop="invoiceStatus">
							<el-input-number v-model="ruleForm.invoiceStatus" placeholder="请输入发票状态" clearable />
							
						</el-form-item>
						
					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="发票状态">
							<!-- <el-select clearable v-model="ruleForm.invoiceStatus" placeholder="请选择发票状态">
								<el-option v-for="(item, index) in  invoiceStatus" :key="index" :value="item.value"
									:label="item.label"></el-option>
							</el-select> -->
							<el-switch v-model="ruleForm.invoiceStatus"></el-switch>
						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="备注" prop="notes">
							<el-input v-model="ruleForm.notes" placeholder="请输入备注" clearable />

						</el-form-item>

					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
		<PubSupplier ref="editPubSupplierRef" :title="editPubSupplierTitle" @reloadTable="getPubSupplierDropdownList">
		</PubSupplier>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted,watch } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { addPaymentOrder, updatePaymentOrder } from "/@/api/main/paymentOrder";
import { PubSupplierDropdown } from '/@/api/main/pubSupplier';
import { SubjectList, tradingAccountDropdown } from '/@/api/main/tradingAccounts';
import PubSupplier from '/@/views/main/pubSupplier/component/editDialog.vue'
//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: "",
	},
});
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const loading = ref(false);
const editPubSupplierTitle = ref('');
const editPubSupplierRef = ref();
const ruleForm = ref<any>({});
//自行添加其他规则
const rules = ref<FormRules>({
});

// const inhouseTypeList = [//入库类型
// 	{ label: '营业成本', value: 0 },
// 	{ label: '采购成本', value: 1 },
// 	{ label: '销售成本', value: 2 },
// 	{ label: '交付成本', value: 3 },
// 	{ label: '财务成本', value: 4 },
// ]
// 科目编码
const getSubjectCodeList = ref<any>([]);
const getSubjectCodeListJk = async () => {
	let list = await SubjectList();
	getSubjectCodeList.value = list.data.result ?? [];
	console.log('科目编码----', list)
};
getSubjectCodeListJk();

watch(() => ruleForm.value.secondaryAccount, (newVal) => {
  if (newVal) {
    const subject = getSubjectCodeList.value.find((item: { subjectCode: any; }) => item.subjectCode === newVal);
    if (subject) {
      ruleForm.value.expenditureCategory = subject.subjectName;
    }
  }
});
// const inhouseType = [//入库类型
// 	{ label: '主营业务收入', value: 0 },
// 	{ label: '其他业务收入', value: 1 },
// ]

const inhouseCategory = [//入库类型
]
// const inhousetrading = [//入库类型
// 	{ label: '现金	', value: 0 },
// 	{ label: '对公', value: 1 },
// 	{ label: '支付宝', value: 2 },
// 	{ label: '微信', value: 3 },
// ]
// 交易账户
const tradingAccountList = ref<any>([]);
const getTradingAccountList = async () => {
	let list = await tradingAccountDropdown();
	tradingAccountList.value = list.data.result ?? [];
};
getTradingAccountList();

const invoiceStatus = [//入库类型
	{ label: '未开票	', value: 0 },
	{ label: '已开票', value: 1 },
]
// 供应商
const pubSupplierDropdownList = ref<any>([]);
const getPubSupplierDropdownList = async () => {
	let list = await PubSupplierDropdown();
	pubSupplierDropdownList.value = list.data.result ?? [];
	console.log('供应商----', list)
};
getPubSupplierDropdownList();
// 打开弹窗
const openDialog = (row: any) => {
	ruleForm.value = JSON.parse(JSON.stringify(row));
	if (ruleForm.value.invoiceStatus === 0) {
		ruleForm.value.invoiceStatus = false
	} else {
		ruleForm.value.invoiceStatus = true
	}
	isShowDialog.value = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	isShowDialog.value = false;
	setTimeout(() => {
		loading.value = false;
	},500)
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			loading.value = true;
			let values = ruleForm.value;
			if (ruleForm.value.invoiceStatus === false) {
				ruleForm.value.invoiceStatus = 0
			} else {
				ruleForm.value.invoiceStatus = 1
			}
			if (ruleForm.value.id != undefined && ruleForm.value.id > 0) {
				await updatePaymentOrder(values);
			} else {
				await addPaymentOrder(values);
			}
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};





// 页面加载时
onMounted(async () => {
});
// 打开新增供应商页面
const openAddPubSupplier = () => {
	editPubSupplierTitle.value = '添加供应商';
	editPubSupplierRef.value.openDialogPub({});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<style scoped lang="scss">
.supplier {
	width: 30px;
	height: 30px;
	margin-left: 10px;
	padding-left: 20px;
}

.el-select {
	width: 210px;
}
</style>




