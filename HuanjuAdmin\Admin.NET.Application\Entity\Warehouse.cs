﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 仓库信息表
    /// </summary>
    [SugarTable("warehouse","仓库信息表")]
    [Tenant("1300000000001")]
    public class Warehouse  : EntityTenant
    {
        /// <summary>
        /// 仓库名称
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "仓库名称", Length = 64)]
        public string Name { get; set; }
        /// <summary>
        /// 仓库编码
        /// </summary>
        [SugarColumn(ColumnDescription = "仓库编码", Length = 32)]
        public string? Code { get; set; }
        /// <summary>
        /// 是否默认仓库
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "是否默认仓库")]
        public int IsDefault { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "状态")]
        public int Status { get; set; }
        /// <summary>
        /// 管理员
        /// </summary>
        [SugarColumn(ColumnDescription = "管理员", Length = 32)]
        public string? Manager { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        [SugarColumn(ColumnDescription = "地址", Length = 255)]
        public string? Address { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        [SugarColumn(ColumnDescription = "排序")]
        public int? OrderNo { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 255)]
        public string? Remark { get; set; }
    }
}