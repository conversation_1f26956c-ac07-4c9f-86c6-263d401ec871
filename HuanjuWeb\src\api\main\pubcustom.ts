﻿import request from '/@/utils/request';
enum Api {
	AddPubcustom = '/api/pubcustom/add',
	DeletePubcustom = '/api/pubcustom/delete',
	UpdatePubcustom = '/api/pubcustom/update',
	PagePubcustom = '/api/pubcustom/page',
	Pubcustom = '/api/pubcustom/list',
	ImportPubcustom = '/api/pubcustom/Import',
	SubjectList = '/api/Subject/List',
}

// 增加客户信息
export const addPubcustom = (params?: any) =>
	request({
		url: Api.AddPubcustom,
		method: 'post',
		data: params,
	});

// 删除客户信息
export const deletePubcustom = (params?: any) =>
	request({
		url: Api.DeletePubcustom,
		method: 'post',
		data: params,
	});

// 编辑客户信息
export const updatePubcustom = (params?: any) =>
	request({
		url: Api.UpdatePubcustom,
		method: 'post',
		data: params,
	});

// 分页查询客户信息
export const pagePubcustom = (params?: any) =>
	request({
		url: Api.PagePubcustom,
		method: 'post',
		data: params,
	});
// 分页查询客户信息
export const Pubcustom = (params?: any) =>
	request({
		url: Api.Pubcustom,
		method: 'get',
		data: params,
	});

// 导入客户信息
export const ImportPubcustom = (params?: any) =>
	request({
		url: Api.ImportPubcustom,
		method: 'post',
		data: params,
		headers: { 'Content-Type': 'multipart/form-data' },
		// 添加这行来防止浏览器自动设置 boundary
		transformRequest: [(data) => data]
	});
// 分页查询科目信息
export const SubjectList = (params?: any) =>
	request({
		url: Api.SubjectList,
		method: 'get',
		data: params,
	});