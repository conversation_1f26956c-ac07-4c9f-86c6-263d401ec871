/**
 * 消息通知
 * @returns 返回模拟数据
 */
export const newsInfoList = [
	{
		title: '[发布] 2021年02月28日发布基于 vue3.x + vite v1.0.0 版本',
		date: '02/28',
		link: 'https://gitee.com/lyt-top/vue-next-admin',
	},
	{
		title: '[发布] 2021年04月15日发布 vue2.x + webpack 重构版本',
		date: '04/15',
		link: 'https://gitee.com/lyt-top/vue-next-admin/tree/vue-prev-admin/',
	},
	{
		title: '[重构] 2021年04月10日 重构 vue2.x + webpack v1.0.0 版本',
		date: '04/10',
		link: 'https://gitee.com/lyt-top/vue-next-admin/tree/vue-prev-admin/',
	},
	{
		title: '[预览] 2020年12月08日，基于 vue3.x 版本后台模板的预览',
		date: '12/08',
		link: 'http://lyt-top.gitee.io/vue-next-admin-preview/#/login',
	},
	{
		title: '[预览] 2020年11月15日，基于 vue2.x 版本后台模板的预览',
		date: '11/15',
		link: 'https://lyt-top.gitee.io/vue-prev-admin-preview/#/login',
	},
    
];

/**
 * 营销推荐
 * @returns 返回模拟数据
 */
export const recommendList = [
	{
		title: '签约金额（元）',
		type: 'normal',
		month: '153,234',
		year: '1,772,344',
		icon: 'ele-Food',
		bg: '#48D18D',
		iconColor: '#64d89d',
	},
	{
		title: '回款金额（元）',
		type: 'normal',
		month: '126,788',
		year: '1,313,842',
		icon: 'ele-ShoppingCart',
		bg: '#8595F4',
		iconColor: '#92A1F4',
	},
	{
		title: '待回款金额（元）',
		type: 'amount',
		total: '1,873,842',
		icon: 'ele-School',
		bg: '#F95959',
		iconColor: '#F86C6B',
	},
	
];