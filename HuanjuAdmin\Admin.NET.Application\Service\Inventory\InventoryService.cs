﻿// MIT License
//
// Copyright (c) 2021-present zuohuaijun, Daming Co.,Ltd and Contributors
//
// 电话/微信：18020030720 QQ群1：87333204 QQ群2：252381476

using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Application.Service.Inventory.Dto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.Inventory;

[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class InventoryService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<Salescontract> _rep;
    private readonly SqlSugarRepository<WarehouseoutMX> _reps;
    public InventoryService(SqlSugarRepository<Salescontract> rep, SqlSugarRepository<WarehouseoutMX> reps)
    {
        _rep = rep;
        _reps = reps;
    }
    /// <summary>
    /// 分页查询销售合约
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<List<InventoryResDto>> Page(InventoryReqDto input)
    {
        var query = _reps.AsQueryable()
                    //.WhereIF(!string.IsNullOrWhiteSpace(input.id), u => u.Productcode == input.id)
                    .Select(x => new InventoryResDto { TradeName = x.Warehousegoods.Name, OutboundNum = 0, OutOfStock = x.Vacancy, ContractNum = 0 }) ;
        var a =await _reps.AsQueryable()
                    //.WhereIF(!string.IsNullOrWhiteSpace(input.id), u => u.Productcode == input.id)
                    .Select(x => new InventoryResDto { TradeName = x.Warehousegoods.Name, OutboundNum = 0, OutOfStock = x.Vacancy, ContractNum = 0 }).ToListAsync();
        //query = query.OrderBuilder(input);
        //return await query.ToPagedListAsync(input.Page, input.PageSize);
        return a;
    }

}
