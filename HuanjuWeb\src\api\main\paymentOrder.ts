﻿import request from '/@/utils/request';
enum Api {
  AddPaymentOrder = '/api/paymentOrder/add',
  DeletePaymentOrder = '/api/paymentOrder/delete',
  UpdatePaymentOrder = '/api/paymentOrder/update',
  PagePaymentOrder = '/api/paymentOrder/page',
  Import = '/api/paymentOrder/Import',
  Submit = 'api/paymentOrder/submit', 
  Retract = 'api/paymentOrder/retract',
  Suspend = '/api/paymentOrder/Suspend',
  InvoiceRecieve = '/api/paymentOrder/InvoiceRecieve',
}

// 增加付款单
export const addPaymentOrder = (params?: any) =>
	request({
		url: Api.AddPaymentOrder,
		method: 'post',
		data: params,
	});

// 删除付款单
export const deletePaymentOrder = (params?: any) => 
	request({
		url: Api.DeletePaymentOrder,
		method: 'post',
		data: params,
	});

// 编辑付款单
export const updatePaymentOrder = (params?: any) => 
	request({
		url: Api.UpdatePaymentOrder,
		method: 'post',
		data: params,
	});

// 分页查询付款单
export const pagePaymentOrder = (params?: any) => 
	request({
		url: Api.PagePaymentOrder,
		method: 'post',
		data: params,
	});

// 分页查询付款单
export const Import = (params?: any) => 
    request({
		url: Api.Import,
		method: 'post',
		data: params,
		responseType: 'blob'
	});
// 提交
export const Submit = (params?: any) => 
    request({
		url: Api.Submit,
		method: 'post',
		data: params
    });
// 撤回
export const Retract = (params?: any) => 
    request({
		url: Api.Retract,
		method: 'post',
		data: params
    });
// 中止
export const Suspend = (params?: any) =>
	request({
		url: Api.Suspend,
		method: 'post',
		data: params,
	});
// 付款单收票
export const InvoiceRecieve = (params?: any) => 
	request({
		url: Api.InvoiceRecieve,
		method: 'post',
		data: params,
	});
