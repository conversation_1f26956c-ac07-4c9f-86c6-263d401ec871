﻿<template>
	<div>
		<el-card class="query-form" :body-style="{ paddingBottom: '0' }">
			<el-form :model="queryParams" ref="queryForm" :inline="true">
				<el-form-item label="采购单号">
					<el-input v-model="queryParams.orderNumber" clearable="" placeholder="请输入采购单号" />
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'warehousePurchase:page'"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="ele-Plus" @click="openAddWarehousePurchase" v-auth="'warehousePurchase:add'"> 新增 </el-button>
					<el-button type="primary" icon="ele-Finished" @click="commitButtonClick" v-auth="'warehousePurchase:commit'"> 提交 </el-button>
					<el-button type="primary" icon="ele-Back" @click="revocationButtonClick" v-auth="'warehousePurchase:revocation'"> 撤回 </el-button>
					<el-button type="primary" icon="ele-Printer" @click="printButtonClick" v-auth="'warehousePurchase:print'"> 打印 </el-button>
				</el-form-item>
			</el-form>
		</el-card>
		<splitpanes class="default-theme" horizontal style="height: 85vh" @resized="handleTz">
			<pane :size="topSize" ref="topCardRef">
				<el-card shadow="hover" :body-style="{ paddingBottom: '0' }" class="topCard">
					<el-table
						:data="tableData"
						class="toptable"
						:row-class-name="rowClassName"
						:height="tableHeight"
						v-loading="loading"
						tooltip-effect="light"
						row-key="id"
						border=""
						@row-click="handleRowClick"
						highlight-current-row
						v-model:selection="selectedRows"
						@selection-change="handleSelectionChange"
						stripe
					>
						<!-- <el-table :data="tableData" class="toptable" :height="tableHeight" v-loading="loading"
						tooltip-effect="light" row-key="id" border="" @row-click="handleRowClick" highlight-current-row
						v-model:selection="selectedRows"
						@selection-change="(selection: Array<any>) => selectedRows = selection" stripe>-->
						<el-table-column type="selection" width="40" fixed="" />
						<el-table-column type="index" label="序号" width="45" align="center" fixed="" />
						<el-table-column prop="orderNumber" label="采购单号" width="100" fixed="" show-overflow-tooltip="" />
						<el-table-column prop="goodsInfo" label="商品信息" width="150" show-overflow-tooltip="" />
						<el-table-column prop="supplierId" label="供应商" show-overflow-tooltip="">
							<template #default="scope">
								<span>{{ scope.row.supplierName }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="warehouseId" label="仓库" show-overflow-tooltip="">
							<template #default="scope">
								<span>{{ scope.row.warehouseName }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="documenntStatus" label="单据状态" width="90" show-overflow-tooltip="">
							<template #default="scope">
								<el-tag type="info" v-if="scope.row.documenntStatus === 0">已创建</el-tag>
								<el-tag type="warning" v-else-if="scope.row.documenntStatus === 1">待审核</el-tag>
								<el-tag type="success" v-else-if="scope.row.documenntStatus === 2">审核通过</el-tag>
								<el-tag type="Primary" v-else-if="scope.row.documenntStatus === 3">已完成</el-tag>
								<el-tag type="danger" v-else-if="scope.row.documenntStatus === -1">已退回</el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="totalAmt" label="总金额" width="90" show-overflow-tooltip="" />
						<el-table-column prop="discountAmt" label="优惠金额" width="90" show-overflow-tooltip="" />
						<el-table-column prop="actualAmt" label="实际金额" width="90" show-overflow-tooltip="" />
						<el-table-column prop="createUserName" label="创建人" show-overflow-tooltip="" />
						<el-table-column prop="createTime" label="创建时间" show-overflow-tooltip="" />
						<el-table-column prop="remark" label="备注" show-overflow-tooltip="" />
						<!-- <el-table-column label="修改记录" width="65" align="center" fixed="right" show-overflow-tooltip>
							<template #default="scope">
								<ModifyRecord :data="scope.row" />
							</template>
						</el-table-column> -->
						<el-table-column label="操作" width="130" align="center" fixed="right" show-overflow-tooltip="" v-if="auth('warehousePurchase:edit') || auth('warehousePurchase:delete')">
							<template #default="scope">
								<el-button
									icon="ele-Edit"
									size="small"
									text=""
									type="primary"
									@click="openEditWarehousePurchase(scope.row)"
									v-auth="'warehousePurchase:edit'"
									:disabled="scope.row.documenntStatus === 0 ? false : true"
								>
									编辑
								</el-button>
								<el-button
									icon="ele-Delete"
									size="small"
									text=""
									type="primary"
									@click="delWarehousePurchase(scope.row)"
									v-auth="'warehousePurchase:delete'"
									:disabled="scope.row.documenntStatus === 0 ? false : true"
								>
									删除
								</el-button>
							</template>
						</el-table-column>
					</el-table>
					<el-pagination
						v-model:currentPage="tableParams.page"
						v-model:page-size="tableParams.pageSize"
						:total="tableParams.total"
						:page-sizes="[10, 20, 50, 100]"
						small=""
						background=""
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
						layout="total, sizes, prev, pager, next, jumper"
					/>
					<editDialog ref="editDialogRef" :title="editWarehousePurchaseTitle" @reloadTable="handleQuery" />
				</el-card>
			</pane>
			<pane :size="BomSize" ref="bomCardRef">
				<el-card shadow="hover" :body-style="{ paddingBottom: '0' }" class="bomCard">
					<span class="text-lg font-bold">采购单号：{{ tableDataRow.orderNumber }}</span>
					<el-table :data="tableDataMX" class="bomTable" tooltip-effect="light" row-key="id" border="" stripe :height="bomHeight">
						<el-table-column type="index" label="序号" width="55" align="center" fixed="" />
						<el-table-column prop="goodsId" label="商品" fixed="" show-overflow-tooltip="">
							<template #default="scope">
								<span>{{ scope.row.warehousegoodsName }}</span>
							</template>
						</el-table-column>
						<el-table-column prop="productCode" label="商品编码" width="90" fixed="" show-overflow-tooltip="" />
						<el-table-column prop="brandName" label="品牌" width="90" fixed="" show-overflow-tooltip="" />
						<el-table-column prop="specsName" label="规格" width="90" fixed="" show-overflow-tooltip="" />
						<el-table-column prop="unitName" label="单位" width="65" fixed="" show-overflow-tooltip="" />
						<el-table-column prop="puchPrice" label="单价" width="75" fixed="" show-overflow-tooltip="" />
						<el-table-column prop="puchQty" label="数量" width="65" fixed="" show-overflow-tooltip="" />
						<el-table-column prop="puchAmt" label="采购金额" width="90" fixed="" show-overflow-tooltip="" />-->
						<!-- <el-table-column prop="supplierId" label="供应商" fixed="" show-overflow-tooltip="">
							<template #default="scope">
								<span>{{ scope.row.pubSupplierName }}</span>
							</template>
						</el-table-column> -->
					</el-table>
					<el-pagination
						v-model:currentPage="tableParamsMx.page"
						v-model:page-size="tableParamsMx.pageSize"
						:total="tableParamsMx.total"
						:page-sizes="[10, 20, 50, 100]"
						small=""
						background=""
						@size-change="handleSizeChangeMx"
						@current-change="handleCurrentChangeMx"
						layout="total, sizes, prev, pager, next, jumper"
					/>
					<editDialogMX ref="editDialogRefMX" :title="editWarehousePurchaseMXTitle" @reloadTable="refershDataMX" />
				</el-card>
			</pane>
		</splitpanes>
	</div>
</template>

<script lang="ts" setup="" name="warehousePurchase">
import { ref, watch, getCurrentInstance } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { auth } from '/@/utils/authFunction';
import editDialog from '/@/views/main/warehousePurchase/component/editDialog.vue';
import { pageWarehousePurchase, deleteWarehousePurchase, commitWarehousePurchase, revocationWarehousePurchase } from '/@/api/main/warehousePurchase';

import editDialogMX from '/@/views/main/warehousePurchaseMX/component/editDialog.vue';
import { listWarehousePurchaseMX, deleteWarehousePurchaseMX } from '/@/api/main/warehousePurchaseMX';
import { openWindow } from '/@/utils/download';
import { Splitpanes, Pane } from 'splitpanes';
// import ModifyRecord from '/@/components/table/modifyRecord.vue';
import 'splitpanes/dist/splitpanes.css';

const editDialogRef = ref();
const loading = ref(false);
const tableData = ref<any>([]);
const queryParams = ref<any>({});
const tableParams = ref({
	page: 1,
	pageSize: 10,
	total: 0,
});
const tableParamsMx = ref({
	page: 1,
	pageSize: 10,
	total: 0,
});
const topSize = ref('56%');
const BomSize = ref('44%');
const tableHeight = ref('370');
const bomHeight = ref('250');
const pagesInstance = getCurrentInstance();
const topCardRef = ref(null);
const editWarehousePurchaseTitle = ref('');
const selectedRows = ref<any[]>([]);
const editDialogRefMX = ref();
const tableDataMX = ref<any>([]);
const editWarehousePurchaseMXTitle = ref('');
let tableDataRow = ref<any>([]);

watch(
	tableData,
	() => {
		for (let item of tableData.value) {
			if (item.id === tableDataRow.value.id) {
				tableDataRow.value = item;
				break;
			}
		}
	},
	{ deep: true }
);

watch(
	tableDataMX,
	() => {
		let newTotalAmt = tableDataMX.value.reduce((total: number, row: any) => total + row.puchAmt, 0);
		updateTotalAmtInTableData(newTotalAmt);
	},
	{ deep: true }
);

// 更新 tableData 中的 totalAmt
const updateTotalAmtInTableData = (newTotalAmt: number) => {
	// 更新tableData中相关记录的totalAmt
	for (let item of tableData.value) {
		if (item.id === tableDataRow.value.id) {
			item.totalAmt = newTotalAmt;
			break;
		}
	}
};
// 拖动改变表格高度
const handleTz = (value) => {
	console.log(value, 'value');
	topSize.value = value[0].size;
	BomSize.value = value[1].size;
	const height = window.innerHeight - 150;
	console.log('浏览器高度', height);
	tableHeight.value = (parseFloat(pagesInstance.refs.topCardRef.style.height) / 100) * height - 90;
	console.log(tableHeight.value);
	bomHeight.value = (parseFloat(pagesInstance?.refs.bomCardRef.style.height) / 100) * height - 110;
	console.log(bomHeight.value);
};

const handleRowClick = async (row: any) => {
	tableDataRow.value = row;
	await refershDataMX();
};

const refershDataMX = async () => {
	// loading.value = true;
	// Assuming the left-side row has an ID field.
	var res = await listWarehousePurchaseMX(tableDataRow.value.id);
	tableDataMX.value = res.data.result ?? [];
	// alert(tableDataMX.value);
	// loading.value = false;
};

// 打开新增页面
const openAddWarehousePurchaseMX = () => {
	editWarehousePurchaseMXTitle.value = '添加商品采购明细';
	editDialogRefMX.value.openDialogAdd(tableDataRow.value.id);
};

// 打开编辑页面
const openEditWarehousePurchaseMX = (row: any) => {
	editWarehousePurchaseMXTitle.value = '编辑商品采购明细';
	editDialogRefMX.value.openDialog(row);
};

const handleSelectionChange = (selection: TableItem[]) => {
	selectedRows.value = selection;
	for (const row of tableData.value) {
		row.isSelected = false;
	}
	for (const selectedRow of selection) {
		const foundRow = tableData.value.find((row) => row.id === selectedRow.id);
		if (foundRow) {
			foundRow.isSelected = true;
		}
	}
};
const rowClassName = (row: TableItem) => {
	console.log(row.row);
	return row.row.isSelected ? 'current-row' : '';
};

// 删除
const delWarehousePurchaseMX = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await deleteWarehousePurchaseMX(row);
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 查询操作
const handleQuery = async () => {
	loading.value = true;
	var res = await pageWarehousePurchase(Object.assign(queryParams.value, tableParams.value));
	tableData.value = res.data.result?.items ?? [];
	tableParams.value.total = res.data.result?.total;
	loading.value = false;
};
// 重置查询条件
const resetQuery = () => {
	queryParams.value = {};
	handleQuery();
};
// 打开新增页面
const openAddWarehousePurchase = () => {
	editWarehousePurchaseTitle.value = '添加商品采购';
	editDialogRef.value.openDialog({});
};

// 提交商品采购
const commitButtonClick = () => {
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		if (item.documenntStatus == 0 || item.documenntStatus == -1) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.orderNumber + ' 状态不正确，无法提交');
			return;
		}
	}
	if (listPurchaseIds.value.length == 0) {
		ElMessage.warning('请先选中要提交的记录');
		return;
	}
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要提交吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await commitWarehousePurchase(listPurchaseIds.value);
			handleQuery();
			ElMessage.success('提交成功');
		})
		.catch(() => {});
};

// 撤回商品采购
const revocationButtonClick = () => {
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		if (item.documenntStatus == 2) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.orderNumber + ' 状态不正确，无法撤回');
			return;
		}
	}
	if (listPurchaseIds.value.length == 0) {
		ElMessage.warning('请先选中要撤回的记录');
		return;
	}
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要撤回吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await revocationWarehousePurchase(listPurchaseIds.value);
			handleQuery();
			ElMessage.success('撤回成功');
		})
		.catch(() => {});
};

//打印
const printButtonClick = () => {
	ElMessageBox.confirm('打印功能正在开发中...');
};

// 打开编辑页面
const openEditWarehousePurchase = async (row: any) => {
	// alert(1);
	await handleRowClick(row);
	// alert(2);
	editWarehousePurchaseTitle.value = '编辑商品采购';
	editDialogRef.value.openDialog(row, tableDataMX.value);
};

// 删除
const delWarehousePurchase = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await deleteWarehousePurchase(row);
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	tableParams.value.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	tableParams.value.page = val;
	handleQuery();
};

// 改边明细页面容量
const handleSizeChangeMx = (val: number) => {
	tableParamsMx.value.pageSize = val;
	refershDataMX();
};

// 改变明细页码序号
const handleCurrentChangeMx = (val: number) => {
	tableParamsMx.value.page = val;
	refershDataMX();
};

handleQuery();
</script>

<style lang="scss" scoped>
.topCard {
	margin-top: 8px;
	height: 100%;
}

.toptable {
	width: 100%;
}

.bomCard {
	height: 100%;

	.bomTable {
		margin-top: 6px;
	}
}

:deep(.splitpanes.default-theme .splitpanes__splitter) {
	background: #f0f0f0;
}

@media (max-height: 920px) {
	.default-theme {
		height: calc(85vh - 55px) !important;
	}
}

@media (max-height: 640px) {
	.default-theme {
		height: calc(85vh - 78px) !important;
	}
}

.el-form-item--default {
	margin-bottom: 6px;
}

.flex {
	display: flex;
}

.justify-between {
	justify-content: space-between;
}

.items-center {
	align-items: center;
}

.text-lg {
	font-size: 1.125rem;
}

.font-bold {
	font-weight: 700;
}
</style>


