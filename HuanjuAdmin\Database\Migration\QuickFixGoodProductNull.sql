-- 快速修复GoodProduct字段的null值
-- 将所有null值设置为1（良品）

-- 1. 修复出库记录表中的null值
UPDATE outboundrecord 
SET GoodProduct = 1 
WHERE GoodProduct IS NULL;

-- 2. 修复入库记录表中的null值  
UPDATE inboundrecord 
SET GoodProduct = 1 
WHERE GoodProduct IS NULL;

-- 3. 检查修复结果
SELECT '修复后的数据统计:' as message;

SELECT 
    'outboundrecord' as table_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN GoodProduct = 1 THEN 1 ELSE 0 END) as good_products,
    SUM(CASE WHEN GoodProduct = 0 THEN 1 ELSE 0 END) as defective_products,
    SUM(CASE WHEN GoodProduct IS NULL THEN 1 ELSE 0 END) as null_values
FROM outboundrecord

UNION ALL

SELECT 
    'inboundrecord' as table_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN GoodProduct = 1 THEN 1 ELSE 0 END) as good_products,
    SUM(CASE WHEN GoodProduct = 0 THEN 1 ELSE 0 END) as defective_products,
    SUM(CASE WHEN GoodProduct IS NULL THEN 1 ELSE 0 END) as null_values
FROM inboundrecord;

SELECT 'GoodProduct字段null值修复完成！现在所有null值都已设置为良品(1)' as result; 