<template>
    <el-collapse-item name="produceList">
        <template #title>
            <div style="width: 100%; display: flex; justify-content: space-between; align-items: center;" >
                <el-text size="large">产出列表</el-text>
                <el-button icon="ele-Plus" :loading="loading" text="" type="primary" @click.stop="addOutput">新增</el-button>
            </div>
        </template>

        <editable-table
            ref="tableRef"
            :table-data="props.tableData"
            @update:table-data="handleUpdate"
            :columns="tableColumns"
            :loading="loading"
            :show-pagination="false"
            :required-fields="['warehouseGoodsId']"
            validation-message="产品名称不能为空"
            style="height: calc(30vh - var(--el-collapse-header-height));"
        >
            <template #productId-edit="{ row }">
                <el-select v-model="row.warehouseGoodsId" size="small" style="width: 100%" filterable
                    @change="(val: any) => handleProductIdChange(row, val)">
                    <el-option v-for="item in warehouseGoodsList" :key="item.warehouseGoodsId" :label="item.warehousegoods.name" :value="item.warehouseGoodsId">
                    </el-option>
                </el-select>
            </template>

            <template #productId="{ row }">
                {{ row.warehousegoods.name }}
            </template>

            <template #quantity-edit="{ row }">
                <el-input-number v-model="row.quantity" :min="0" controls-position="right" :precision="2" size="small" style="width: 100%" />
            </template>
            <template #unitPrice-edit="{ row }">
                <el-input-number v-model="row.unitPrice" :min="0" controls-position="right" :precision="2" size="small" style="width: 100%" />
            </template>
        </editable-table>
    </el-collapse-item>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import EditableTable from '/@/components/editableTable/index.vue';
import { getWarehouseGoodsList } from '/@/api/main/processOrderScheme';

const props = defineProps({
    tableData: {
        type: Array,
        required: true,
        default: () => []
    },
    activeItems: {
        type: Array,
        required: true,
        default: () => []
    },
    warehouseId: {
        type: String,
        required: true,
        default: ''
    }
});

const emit = defineEmits(['update:tableData']);

const loading = ref(false);
const tableRef = ref();
const warehouseGoodsList = ref<any[]>([]);

// 表格列配置
const tableColumns = [
    { type: 'index', label: '序号' },
    { prop: 'warehouseGoodsId', label: '产品名称', slot: 'productId', showOverflowTooltip: true, editable: true },
    { prop: 'warehousegoods.code', label: '产品编码',  showOverflowTooltip: true, editable: false },
    { prop: 'warehousegoods.specs', label: '规格型号',  showOverflowTooltip: true, editable: false },
    { prop: 'warehousegoods.warehouseGoodsUnit.name', label: '单位',  editable: false },
    { prop: 'quantity', label: '数量', slot: 'quantity',width: 110, editable: true },
    { prop: 'unitPrice', label: '单价', slot: 'unitPrice',width: 110, editable: true },
    { type: 'operation', label: '操作', width: 140, fixed: 'right' }
];

const getWarehouseGoodsOptions = () => {
    getWarehouseGoodsList({}).then((res: any) => {
        warehouseGoodsList.value = res.data.result;
    });
};

const handleProductIdChange = (row: any, val: any) => {
    setProductInfo(row, val);
};

const setProductInfo = (row: any, val: any) => {
    if (val>0) {
        const product = warehouseGoodsList.value.find((item: any) => item.warehouseGoodsId === val);
        if (product) {
            row.warehousegoods = product.warehousegoods;
        }
    }
};


onMounted(() => {
    getWarehouseGoodsOptions();
});

// 添加新的产出行
const addOutput = () => {
    if (!props.activeItems.includes('produceList')) {
        // eslint-disable-next-line vue/no-mutating-props
        props.activeItems.push('produceList');
    }
    
    // 使用通用组件的 addItem 方法
    tableRef.value.addItem({
        productCode: '',
        productName: '',
        specification: '',
        unit: '个',
        quantity: 0
    });
};

// 检查表格有效性
const valid = () => {
    return tableRef.value.valid();
};

// 重置编辑状态
const resetEditStatus = () => {
    if (tableRef.value) {
        tableRef.value.resetEditStatus();
    }
};

const handleUpdate = (val:any) => {
    emit('update:tableData', val);
}

// 暴露方法
defineExpose({
    loading,
    valid,
    resetEditStatus
});
</script>

<style scoped>
/* 确保编辑中的输入框宽度合适 */
.el-input, .el-select, .el-input-number {
    width: 100%;
}

/* 隐藏分页组件 */
:deep(.output-table .el-pagination) {
    display: none !important;
}
</style>