﻿<template>
	<!-- 出库单弹窗 -->
	<div class="warehouseout-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="1210" draggable="" :close-on-click-modal="false">
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" class="mb10" :rules="rules">
				<el-row>
					<el-form-item label="客户" prop="customId">
						<el-select clearable filterable v-model="ruleForm.customId" placeholder=" " class="w160">
							<el-option v-for="(item, index) in PubcustomList" :key="index" :value="item.value" :label="item.label" />
						</el-select>
					</el-form-item>
					<!-- 					<el-form-item label="客户" prop="customId">
						<el-input v-model="ruleForm.customId" placeholder="请输入客户" clearable class="w198" />
					</el-form-item> -->
					<el-form-item label="仓库" prop="warehouseId">
						<el-select clearable filterable v-model="ruleForm.warehouseId" placeholder="请选择仓库" class="w160">
							<el-option v-for="(item, index) in warehouseDropdownList" :key="index" :value="item.value" :label="item.label" />
						</el-select>
					</el-form-item>
					<el-form-item label="出库类型" prop="outboundtype">
						<el-select clearable filterable v-model="ruleForm.outboundtype" placeholder="请选择出库类型" class="w160">
							<el-option v-for="(item, index) in counterStore.outboundStatusList" :key="index" :value="item.value" :label="item.label" />
						</el-select>
					</el-form-item>
					<el-form-item label="备注" prop="remark">
						<el-input v-model="ruleForm.remark" placeholder="请输入备注" clearable class="w160" />
					</el-form-item>
				</el-row>
			</el-form>

			<!-- 修改按钮和金额显示的布局 -->
			<div class="mb10 flex justify-between items-center">
				<div class="flex items-center gap-4">
					<!-- 左侧放置按钮 -->
					<el-button type="primary" icon="ele-Plus" @click="addckdDetail">新增出库单明细</el-button>
				</div>
				<div class="flex items-center gap-6">
					<!-- 右侧放置金额信息 -->
					<div class="amount-item">
						<span class="amount-label">总金额：</span>
						<span class="amount-value">{{ (ruleForm.totalAmt || 0).toFixed(2) }}</span>
					</div>
					<div class="amount-item">
						<span class="amount-label">优惠金额：</span>
						<el-input-number v-model="ruleForm.discountAmt" :min="0" :precision="2" :max="ruleForm.totalAmt || 0" @change="updateActualAmount" :controls="false" class="compact-input" />
					</div>
					<div class="amount-item">
						<span class="amount-label">实际金额：</span>
						<el-input-number v-model="ruleForm.actualAmt" :min="0" :precision="2" :max="ruleForm.totalAmt || 0" @change="updateDiscountAmount" :controls="false" class="compact-input" />
					</div>
				</div>
			</div>

			<el-table :data="tableData" tooltip-effect="light" row-key="id" border="" class="tcTable">
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="goodsId" label="商品名称" show-overflow-tooltip="" width="150">
					<template #default="scope">
						<el-select filterable v-model="scope.row.goodsId" placeholder=" " class="w185" @change="getGoodsDetail(scope.row, scope.row.goodsId)">
							<el-option v-for="(item, index) in warehousegoodsDropdownList" :key="index" :value="item.value" :label="item.label" />
						</el-select>
					</template>
				</el-table-column>
				<el-table-column prop="productcode" label="商品编码" show-overflow-tooltip="" width="90" />
				<el-table-column prop="brand" label="品牌" show-overflow-tooltip="" width="90" />
				<el-table-column prop="specifications" label="规格" show-overflow-tooltip="" width="90" />
				<el-table-column prop="unit" label="单位" width="75">
					<template #default="scope">
						<el-select clearable filterable disabled="" v-model="scope.row.unit" placeholder=" ">
							<el-option v-for="(item, index) in WarehouseUnit" :key="index" :value="item.value" :label="item.label" />
						</el-select>
					</template>
				</el-table-column>
				<el-table-column prop="goodProduct" label="是否良品" show-overflow-tooltip="" width="65">
					<template #default="scope">
						<el-switch v-model="scope.row.goodProduct" active-color="#13ce66"></el-switch>
					</template>
				</el-table-column>
				<el-table-column prop="OutCount" label="出库数量" show-overflow-tooltip="" width="138">
					<template #default="scope">
						<el-input-number v-model="scope.row.outCount" placeholder="" clearable :min="0" @change="updateRowTotal(scope.row, 'outCount')" class="w-full" />
					</template>
				</el-table-column>
				<el-table-column prop="unitprice" label="单价" width="138" show-overflow-tooltip="">
					<template #default="scope">
						<el-input-number v-model="scope.row.unitprice" placeholder="" type="number" :min="0" @change="updateRowTotal(scope.row, 'unitprice')" class="w-full" />
					</template>
				</el-table-column>
				<el-table-column prop="totalAmt" label="合计" width="138" show-overflow-tooltip="">
					<template #default="scope">
						<el-input-number v-model="scope.row.totalAmt" placeholder="" :min="0" :precision="2" @change="updateRowTotal(scope.row, 'totalAmt')" class="w-full" />
					</template>
				</el-table-column>
				<!-- <el-table-column prop="auxiliaryOutCount" label="辅助单位数量" show-overflow-tooltip="" width="109">
					<template #default="scope">
						<el-input-number v-model="scope.row.auxiliaryOutCount" placeholder="" clearable class="w85" />
					</template>
				</el-table-column>
				<el-table-column prop="auxiliaryunit" label="辅助单位" width="70" show-overflow-tooltip="">
					<template #default="scope">
						<el-select clearable filterable v-model="scope.row.auxiliaryunit" placeholder=" ">
							<el-option v-for=" (item, index) in WarehouseUnit" :key="index" :value="item.value"
								:label="item.label" />
						</el-select>
					</template>
				</el-table-column>


				<el-table-column prop="vacancy" label="是否缺货" show-overflow-tooltip="" width="109">
					<template #default="scope">
						<el-select clearable filterable v-model="scope.row.vacancy" placeholder=" " class="w85">
							<el-option v-for="(item, index) in counterStore.hasList" :key="index" :value="item.value"
								:label="item.label" />
						</el-select>
					</template>
				</el-table-column>
				<el-table-column prop="notes" label="备注" show-overflow-tooltip="" width="109">
					<template #default="scope">
						<el-input v-model="scope.row.notes" placeholder="" clearable class="w85" />
					</template>
				</el-table-column> -->
				<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip="">
					<template #default="scope">
						<el-button icon="ele-Delete" size="small" text="" type="primary" @click="deleteCkdDetail(scope.$index)"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { useGoodsStore } from '/@/stores/goods';
import useCounterStore from '/@/stores/counter';
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormRules } from 'element-plus';
import { addWarehouseout, updateWarehouseout } from '/@/api/main/warehouseout';
import { Pubcustom } from '/@/api/main/pubcustom';
import { WarehouseGoodsUnit } from '/@/api/main/warehouseInrecord';
import { getWarehouseDropdown } from '/@/api/main/warehousePurchase';
import { WarehousegoodsDropdown } from '/@/api/main/warehousegoods';
const tableData = ref<any>([]);
const value = ref(true);
let tableMxDeleted = ref<any>([]);
const warehouseDropdownList = ref<any>([]);
const goodsStore = useGoodsStore();
const counterStore = useCounterStore();
const addckdDetail = () => {
	tableData.value.push({
		warehouseId: null,
		tradename: null,
		barcode: null,
		productcode: null,
		brand: null,
		specifications: null,
		goodProduct: true,
		unit: null,
		vacancy: false,
		customerName: null,
		contacts: null,
		phone: null,
		address: null,
		outboundtype: null,
		deliverytime: null,
		notes: null,
		goodsId: null,
		OutId: ruleForm.value.id,
		outCount: 0,
		unitprice: 0,
		totalAmt: '0.00',
	});
	updateTotalAmount();
};

// 修改 updateTotalAmount 函数
const updateTotalAmount = () => {
	const total = tableData.value.reduce((sum, row) => sum + (parseFloat(row.totalAmt) || 0), 0);
	ruleForm.value.totalAmt = total;
	// 更新实际金额（总金额 - 优惠金额）
	ruleForm.value.actualAmt = Math.max(0, total - (ruleForm.value.discountAmt || 0));
};

// 添加优惠金额变化时的处理函数
const updateActualAmount = (value: number) => {
	ruleForm.value.discountAmt = value;
	ruleForm.value.actualAmt = Math.max(0, ruleForm.value.totalAmt - value);
};

// 添加实际金额变化时的处理函数
const updateDiscountAmount = (value: number) => {
	ruleForm.value.actualAmt = value;
	ruleForm.value.discountAmt = Math.max(0, ruleForm.value.totalAmt - value);
};

watch(
	tableData,
	() => {
		updateGoodsInfo();
	},
	{ deep: true }
);

const updateRowTotal = (row: any, field: 'outCount' | 'unitprice' | 'totalAmt') => {
	// 将字符串转换为数字
	const outCount = parseFloat(row.outCount) || 0;
	const unitprice = parseFloat(row.unitprice) || 0;
	const totalAmt = parseFloat(row.totalAmt) || 0;

	switch (field) {
		case 'outCount': // 修改数量
		case 'unitprice': // 修改单价
			// 数量和单价变化时,计算合计
			row.totalAmt = (outCount * unitprice).toFixed(2);
			break;

		case 'totalAmt': // 修改合计
			// 只在数量存在时,根据合计反推单价
			if (outCount !== 0) {
				row.unitprice = (totalAmt / outCount).toFixed(2);
			}
			// 如果数量为0,不做任何计算,保持当前状态
			break;
	}

	updateGoodsInfo();
	updateTotalAmount();
};

const updateGoodsInfo = () => {
	ruleForm.value.goodsInfo = tableData.value
		.map((row) => row.tradename)
		.filter(Boolean)
		.join(',');
};

const getWarehouseDropdownList = async () => {
	let list = await getWarehouseDropdown();
	warehouseDropdownList.value = list.data.result ?? [];
};
getWarehouseDropdownList();

const PubcustomList = ref<any>([]); //出库类型
const getPubcustomList = async () => {
	var obj = await Pubcustom({});
	obj.data.result.forEach((item: any) => {
		PubcustomList.value.push({
			label: item.name,
			value: item.id,
		});
	});
	var a = '';
};

//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: '',
	},
});
const WarehouseUnit = ref<any>([]);
const WareUnit = async () => {
	var res = await WarehouseGoodsUnit();
	WarehouseUnit.value = res.data.result ?? [];
};
//父级传递来的函数，用于回调
const emit = defineEmits(['reloadTable']);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const loading = ref(false);
const ruleForm = ref<any>({
	discountAmt: 0, // 优惠金额
	actualAmt: 0, // 实际金额
	totalAmt: 0, // 总金额
});
// 修改表单规则定义，在 rules 对象中集中定义所有规则
const rules = ref<FormRules>({
	// 添加仓库必填规则
	warehouseId: [
		{ required: true, message: '仓库不能为空', trigger: 'change' }
	],
	// 添加出库类型必填规则
	outboundtype: [
		{ required: true, message: '出库类型不能为空', trigger: 'change' }
	]
});
// 获取商品详情
const getGoodsDetail = (row: any, val: any) => {
	const goodsInfo = goodsStore.getGoodsDetail(val);

	if (goodsInfo) {
		row.tradename = goodsInfo.name || goodsInfo.label || '';
		row.brand = goodsInfo.brand;
		row.barcode = goodsInfo.id;
		row.productcode = goodsInfo.code;
		row.specifications = goodsInfo.specs;
		row.unit = goodsInfo.unit || '';
		row.auxiliaryunit = goodsInfo.auxiliaryunit || '';
		updateGoodsInfo();
	}
};
// 商品
const warehousegoodsDropdownList = ref<any>([]);
const getWarehousegoodsDropdownList = async () => {
	await goodsStore.fetchGoodsList();
	warehousegoodsDropdownList.value = goodsStore.dropdownList;
};
getWarehousegoodsDropdownList();
// 弹窗里删除
const deleteCkdDetail = (index: number) => {
	var deleteRow = tableData.value[index];
	if (deleteRow.id > 0) {
		deleteRow.isDelete = true;
		tableMxDeleted.value.push(deleteRow);
	}
	tableData.value.splice(index, 1);
	updateTotalAmount();
};
// 打开弹窗
const openDialog = (row: any, data: any) => {
	ruleForm.value = JSON.parse(JSON.stringify(row));
	ruleForm.value.totalAmt = ruleForm.value.totalAmt || 0; // 确保 totalAmt 有一个初始值
	tableData.value = data;
	updateTotalAmount(); // 初始化总金额
	isShowDialog.value = true;
};

WareUnit();
// 关闭弹窗
const closeDialog = () => {
	emit('reloadTable', 'current', ruleForm.value);
	isShowDialog.value = false;
	setTimeout(() => {
		loading.value = false;
	}, 500);
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (valid: boolean) => {
		if (!valid) return false;
		try {
			loading.value = true;
			let values = ruleForm.value;
			let params = {
				AddWarehouseoutInput: values,
				listMx: tableData.value,
			};
			if (tableMxDeleted.value.length > 0) {
				params.listMx = params.listMx.concat(tableMxDeleted.value);
				tableMxDeleted.value = [];
			}
			console.log(params);
			await addWarehouseout(params);
			
			// 清除相关缓存，确保明细数据能够刷新
			if (values.id) {
				// 导入缓存工具
				const { requestCache } = await import('/@/utils/request-cache');
				// 清除出库单明细的缓存
				requestCache.delete('warehouseoutMX/page', {
					OutId: values.id,
					page: 1,
					pageSize: 1000
				});
			}
			
			closeDialog();
		} finally {
		}
	});
};

// 页面加载时
onMounted(async () => {
	try {
		// 添加 goodsStore 初始化
		await goodsStore.fetchGoodsList();

		// 其他初始化
		await Promise.all([getPubcustomList(), getWarehousegoodsDropdownList(), WareUnit()]);
	} catch (error) {
		console.error('初始化数据失败:', error);
		ElMessage.error('初始化数据失败');
	}
});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<style lang="scss" scoped>
.el-form-item--default {
	margin-bottom: 10px;
}

.flex {
	display: flex;
}

.justify-between {
	justify-content: space-between;
}

.items-center {
	align-items: center;
}

.text-lg {
	font-size: 1.125rem;
}

.font-bold {
	font-weight: 700;
}

.amount-item {
	display: flex;
	align-items: center;

	.amount-label {
		white-space: nowrap;
		color: #606266;
		margin-right: 8px;
	}

	.amount-value {
		font-size: 1.125rem;
		font-weight: 600;
		color: #409eff;
	}
}

.compact-input {
	width: 120px;

	:deep(.el-input-number__decrease),
	:deep(.el-input-number__increase) {
		display: none;
	}

	:deep(.el-input__wrapper) {
		padding: 0 8px;
	}

	:deep(.el-input__inner) {
		text-align: right;
	}
}

.gap-4 {
	gap: 1rem;
}

.gap-6 {
	gap: 1.5rem;
}
</style>




