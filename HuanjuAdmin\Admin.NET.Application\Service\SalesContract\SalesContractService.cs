﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Application.Enum;
using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Nest;
using SqlSugar;
using System;
using System.Linq;

namespace Admin.NET.Application;
/// <summary>
/// 销售合约服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class SalesContractService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<Salescontract> _rep;
    private readonly SalesperFormanceplanService _salesperFormanceplanService;
    private readonly SqlSugarRepository<SalesperFormanceplan> _repSalesperFormanceplan;
    private readonly SqlSugarRepository<Pubcustom> _repPubcustom;
    UserManager _userManager;
    public SalesContractService(SqlSugarRepository<Salescontract> rep,
        SalesperFormanceplanService salesperFormanceplanService,
        SqlSugarRepository<SalesperFormanceplan> salesperFormancepla,
        SqlSugarRepository<Pubcustom> pubcustom,
        UserManager userManager)
    {
        _rep = rep;
        _userManager = userManager;
        _salesperFormanceplanService = salesperFormanceplanService;
        _repSalesperFormanceplan = salesperFormancepla;
        _repPubcustom = pubcustom;
    }

    /// <summary>
    /// 分页查询销售合约
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<SalesContractOutput>> Page(SalesContractInput input)
    {
        var checkStatus = !string.IsNullOrWhiteSpace(input.ContractStatus) ? input.ContractStatus.Split(',') : null;
        //如果input.ContractStatus != null 并且包含1，则
        bool addCondition = false, reAddCondition = false;
        if (checkStatus != null && checkStatus.Any() && checkStatus.Contains("1"))
        {
            //移除input.ContractStatus中的1
            checkStatus = checkStatus.Where(x => x != "1").ToArray();
            addCondition = true;
        }
        if (checkStatus != null && checkStatus.Any()) reAddCondition = true;

        var today = DateTime.Now.Date;

        var query = _rep.AsQueryable();
        if (addCondition)
        {
            query = query.Where(x => x.ContractStatus == 2 || x.ContractStatus == 3)
                        .Where(x => SqlFunc.Subqueryable<SalesperFormanceplan>()
                            .Where(p => p.SalesOrder == x.Id.ToString() && p.Status == 0 && p.PlanTime.ToDateTime().Date < today)
                            .Any());
        }
        if (reAddCondition)
        {
            query = query.WhereIF(checkStatus?.Any() == true,
                x => checkStatus.Contains(x.ContractStatus.ToString()));
        }

        query.Where(u => u.TenantId == _userManager.TenantId)
                 .WhereIF(!string.IsNullOrWhiteSpace(input.Salesperson), u => u.Salesperson.Contains(input.Salesperson.Trim()))
                 .WhereIF(!string.IsNullOrWhiteSpace(input.SalesOrder), u => u.SalesOrder.Contains(input.SalesOrder.Trim()))
                 .WhereIF(!string.IsNullOrWhiteSpace(input.ContractCode), u => u.ContractCode.Contains(input.ContractCode.Trim()))
                 .WhereIF(!string.IsNullOrWhiteSpace(input.CustomerName), u => u.CustomerName.Contains(input.CustomerName.Trim()))
                 .Select<SalesContractOutput>();

        if (input.SigningTimeRange != null && input.SigningTimeRange.Count > 0)
        {
            DateTime? start = input.SigningTimeRange.FirstOrDefault();
            if (start.HasValue)
            {
                query = query.Where(u => u.SigningTime >= start.Value);

                // 如果有结束日期，安全地检查第二个元素
                if (input.SigningTimeRange.Count > 1 && input.SigningTimeRange[1].HasValue)
                {
                    DateTime end = input.SigningTimeRange[1].Value;
                    query = query.Where(u => u.SigningTime < end.AddDays(1));
                }
            }
        }
        query = query.OrderBuilder(input);
        return await query.Select<SalesContractOutput>().ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加销售合约
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddSalesContractInput input)
    {
        var entity = input.addSalesContractBaseInput.Adapt<Salescontract>();
        var listMx = input.listMx.Adapt<List<SaleOfGoods>>();
        if (!string.IsNullOrEmpty(entity.CustomerName))
        {
            var result = await _repPubcustom.GetFirstAsync(u => u.Name == entity.CustomerName);
            if (result != null)
            {
                entity.CustomerId = result.Id;
            }
        }
        if (entity.Id > 0)
        {
            await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        }
        else
        {
            var orderNumber = await App.GetRequiredService<PubOrderService>().GetNewOrder("XS");
            if (orderNumber.IsNullOrEmpty())
            {
                throw Oops.Oh(ErrorCodeEnum.GY1001);
            }
            entity.SalesOrder = orderNumber;
            var addSuccess = await _rep.AsInsertable(entity).ExecuteCommandIdentityIntoEntityAsync();
        }

        listMx.ForEach(u => u.SalesOrder = entity.Id);

        if (listMx != null)
        {
            await App.GetRequiredService<SaleOfGoodsService>().AddOrUpdate(listMx);
        }

        //var entity = input.Adapt<Salescontract>();
        //await _rep.InsertAsync(entity);

    }

    /// <summary>
    /// 删除销售合约
    /// </summary>
    /// <param name = "input" ></ param >
    /// < returns ></ returns >
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteSalesContractInput input)
    {
        var entity = input.Adapt<Salescontract>();
        await _rep.DeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新销售合约
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(List<UpdateSalesContractInput> input)
    {
        List<Salescontract> ls = new List<Salescontract>();
        foreach (var item in input)
        {
            item.ContractStatus = 5;
            ls.Add(item.Adapt<Salescontract>());
        }


        await _rep.UpdateRangeAsync(ls);
    }

    /// <summary>
    /// 继承销售合约
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Inherit")]
    public async Task Inherit(List<UpdateSalesContractInput> input)
    {
        foreach (var item in input)
        {
            var query = await _rep.AsQueryable().Where(x => x.Id == item.Id).FirstAsync();
            query.Salesperson = item.Salesperson;
            //var entity = input.Adapt<Salescontract>();
            await _rep.UpdateAsync(query); //AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        }
    }


    /// <summary>
    /// 中止
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Discontinue")]
    public async Task Discontinue(List<UpdateSalesContractInput> input)
    {
        // var query = _rep.AsQueryable().WhereIF(!string.IsNullOrWhiteSpace(input.id), u => u.Salesperson.Contains(input.Salesperson.Trim()))
        foreach (var item in input)
        {
            var entity = input.Adapt<Salescontract>();
            await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 获取销售合约
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpGet]
    //[ApiDescriptionSettings(Name = "Detail")]
    //public async Task<Salescontract> Get([FromQuery] QueryByIdSalesContractInput input)
    //{
    //}

    /// <summary>
    /// 提交销售合约
    /// </summary>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Commit")]
    public async Task Commit(List<SalesContractBaseInput> input)//long Id
    {
        foreach (var item in input)
        {
            var Id = item.Id;
            if (Id > 0)
            {
                var listPurchase = await _rep.AsQueryable().Where(u => u.IsDelete == false && u.Id == Id).FirstAsync();
                //var errorList = listPurchase.FindAll(u => u.DocumenntStatus != OrderStatusEnum.Create && u.DocumenntStatus != OrderStatusEnum.Refunded);
                if (listPurchase == null)
                {
                    throw Oops.Oh("以下销售合约无法提交，请刷新核对后重试：");
                }
                if (listPurchase.ContractStatus >= 2)
                {
                    throw Oops.Oh("已签约后的销售合约无法提交，请确认!");
                }
                //默认生成三个计划
                await _salesperFormanceplanService.Add(new AddSalesperFormanceplanInput()
                {
                    PlanTime = DateTime.Now.Date,
                    Plan = SalesScheduleEnmu.Incomplete,
                    Type = SalesTypeEnmu.SendOut,
                    SalesOrder = item.Id + "",
                    Status = 0
                });
                await _salesperFormanceplanService.Add(new AddSalesperFormanceplanInput()
                {
                    PlanTime = DateTime.Now.Date,
                    Plan = SalesScheduleEnmu.Incomplete,
                    Type = SalesTypeEnmu.Collection,
                    SalesOrder = item.Id + "",
                    Status = 0
                });
                await _salesperFormanceplanService.Add(new AddSalesperFormanceplanInput()
                {
                    PlanTime = DateTime.Now.Date,
                    Plan = SalesScheduleEnmu.Incomplete,
                    Type = SalesTypeEnmu.Invoicing,
                    SalesOrder = item.Id + "",
                    Status = 0
                });
                var incordMxService = App.GetService<Salescontract>();
                //foreach (var purchase in listPurchase)
                //{
                var apprNo = "";
                //判断是否开启流程，如果开启则创建采购单审批流程，否则直接变为已审核并生成出库单。
                var rs = await App.GetService<WorkflowReviewService>().GetNewOrder("销售合约审批", "CG");
                if (rs.Item1 < 0)
                {
                    throw Oops.Oh(rs.Item2);
                }

                if (rs.Item1 == 0)
                {
                    listPurchase.ContractStatus = 2;
                    listPurchase.SigningTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd"));
                    await _rep.UpdateAsync(listPurchase);
                }
                else if (rs.Item1 == 1)
                {
                    //apprNo = rs.Item2;
                    listPurchase.ContractStatus = 1;
                    await _rep.UpdateAsync(listPurchase);
                    //purchase.DocumenntStatus = OrderStatusEnum.NotApproved;
                }
                //  }

                await _rep.AsUpdateable(listPurchase).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
            }
        }
    }

    /// <summary>
    /// 获取销售合约列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<SalesContractOutput>> List([FromQuery] SalesContractInput input)
    {
        return await _rep.AsQueryable().Select<SalesContractOutput>().ToListAsync();
    }


    /// <summary>
    /// 撤销销售合约
    /// </summary>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Revocation")]
    public async Task Revocation(List<long> salesContractIds)//long Id
    {
        if (salesContractIds != null && salesContractIds.Count > 0)
        {
            var salesContractList = _rep.GetList(u => salesContractIds.Contains(u.Id)).ToList();
            //获取履约计划
            List<string> ids = new List<string>();
            salesContractIds.ForEach(u => { ids.Add(u.ToString()); });
            var formancePlans = _repSalesperFormanceplan.GetList(u => ids.Contains(u.SalesOrder)).ToList();
            var errorList = formancePlans.FindAll(u => u.IsDelete == false && u.Status == SalesStatusEnmu.InConfirm).ToList();
            if (errorList != null && errorList.Count > 0)
            {
                var errorSalesIds = errorList.Select(u => u.Id).ToList();
                var errorSalesList = salesContractList.Where(u => errorSalesIds.Contains(u.Id)).ToList();
                throw Oops.Oh("以下履约计划有未关闭状态，请刷新核对后重试：" + string.Join(",", errorSalesList.Select(u => u.SalesOrder)));
            }
            else
            {

                salesContractList.ForEach(u => u.ContractStatus = 0);
                salesContractList.ForEach(u => u.SigningTime = null);
                //await _rep.AsUpdateable(salesContractList).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
                await _rep.AsUpdateable(salesContractList).ExecuteCommandAsync();
                await _repSalesperFormanceplan.DeleteAsync(formancePlans);
            }
        }
    }



}

