﻿using System;

namespace Admin.NET.Application;

    /// <summary>
    /// 固资采购输出参数
    /// </summary>
    public class AssetPurchaseOutput
    {
       /// <summary>
       /// Id
       /// </summary>
       public long Id { get; set; }
    
       /// <summary>
       /// 固资ID
       /// </summary>
       public long AssetId { get; set; } 
       
       /// <summary>
       /// 固资ID
       /// </summary>
       public string AssetInventoryName { get; set; } 
    
       /// <summary>
       /// 单价
       /// </summary>
       public decimal? UnitPrice { get; set; }
    
       /// <summary>
       /// 采购数量
       /// </summary>
       public int PurchaseCount { get; set; }
    
       /// <summary>
       /// 入库数量
       /// </summary>
       public int InCount { get; set; }
    
       /// <summary>
       /// 总价
       /// </summary>
       public decimal? TotalPrice { get; set; }
    
       /// <summary>
       /// 供应商ID
       /// </summary>
       public long? SupplierId { get; set; } 
       
       /// <summary>
       /// 供应商ID
       /// </summary>
       public string PubSupplierName { get; set; } 
    
       /// <summary>
       /// 采购时间
       /// </summary>
       public DateTime? PurchaseTime { get; set; }
    
       /// <summary>
       /// 备注
       /// </summary>
       public string? Remark { get; set; }
    
    }
 

