﻿<template>
	<!-- -----------------入库单上------------- -->
	<div class="warehouseInrecord-container">
		<el-card class="query-form">
			<el-form :model="queryParams" ref="queryForm" :inline="true">
				<el-form-item label="入库单号：">
					<el-input v-model="queryParams.orderNumber" clearable="" placeholder="请输入入库单号" />
				</el-form-item>
				<el-form-item label="上级单号：">
					<el-input v-model="queryParams.purchaseNumber" clearable="" placeholder="请输入上级单号" />
				</el-form-item>
				<el-form-item label="商品名称：">
					<el-input v-model="queryParams.tradeName" clearable="" placeholder="请输入商品名称" />
				</el-form-item>
				<el-form-item label="入库状态：">
					<el-select v-model="queryParams.inhouseStatus" multiple collapse-tags placeholder="请选择入库状态">
						<el-option v-for="item in inhouseStatusList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
				</el-form-item>
			</el-form>
			<div style="margin-top: -10px">
				<el-button-group>
					<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'warehouseInrecord:page'"> 查询</el-button>
					<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
				</el-button-group>
				<!-- <el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'warehouseInrecord:page'"> 查询</el-button> -->
				<el-button type="primary" icon="ele-Plus" @click="openAddWarehouseInrecord" v-auth="'warehouseInrecord:add'" style="margin-left: 20px"> 新增 </el-button>
				<el-button type="primary" icon="ele-Finished" @click="submitOrder"> 提交 </el-button>
				<el-button type="primary" icon="ele-Back" @click="withdraws"> 撤回 </el-button>
				<el-button type="primary" icon="ele-Finished" @click="suspend"> 中止 </el-button>
			</div>
		</el-card>
		<splitpanes class="default-theme" horizontal style="height: 80vh" @resized="handleTz">
			<pane :size="topSize" ref="topCardRef">
				<el-card class="topCard" shadow="hover">
					<div class="topTabDiv">
						<el-table
							:data="tableData"
							class="toptable"
							:row-class-name="rowClassName"
							v-loading="loading"
							ref="inTable"
							tooltip-effect="light"
							row-key="id"
							border=""
							@row-click="handleRowClick"
							highlight-current-row
							:height="tableHeight"
							@selection-change="handleSelectionChange"
						>
							<el-table-column type="selection" width="40" align="center" />
							<el-table-column type="index" label="序号" width="45" align="center" />
							<el-table-column prop="orderNumber" label="入库单号" width="100" show-overflow-tooltip="" />
							<el-table-column prop="goodsInfo" label="商品信息" width="170" show-overflow-tooltip="" />
							<el-table-column prop="totalAmt" label="总金额" width="90" show-overflow-tooltip="" />
							<el-table-column prop="discountAmt" label="优惠金额" width="90" show-overflow-tooltip="" />
							<el-table-column prop="actualAmt" label="实际金额" width="90" show-overflow-tooltip="" />
							<el-table-column prop="purchaseNumber" label="上级单号" width="100" show-overflow-tooltip="" />
							<el-table-column prop="supplierId" label="供应商" show-overflow-tooltip="">
								<template #default="scope">
									{{ filterSlots(scope.row.supplierId, counterStore.supplierList) }}
								</template>
							</el-table-column>
							<el-table-column prop="warehouseid" label="仓库" show-overflow-tooltip="">
								<template #default="scope">
									{{ filterSlots(scope.row.warehouseid, counterStore.warehouseList) }}
								</template>
							</el-table-column>
							<el-table-column prop="inhouseType" label="入库类型" width="90" align="center" show-overflow-tooltip>
								<template #default="scope">
									<el-tag :type="scope.row.inhouseType === 1 ? 'success' : 'danger'">{{ filterSlots(scope.row.inhouseType, inhouseTypeList) }}</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="inhouseStatus" label="入库状态" width="90" show-overflow-tooltip="">
								<template #default="scope">
									<el-tag type="danger" v-if="scope.row.inhouseStatus === 0">待提交</el-tag>
									<el-tag type="danger" v-if="scope.row.inhouseStatus === 1">待入库</el-tag>
									<el-tag type="danger" v-if="scope.row.inhouseStatus === 2">部分入库</el-tag>
									<el-tag type="success" v-if="scope.row.inhouseStatus === 3">已入库</el-tag>
									<el-tag type="success" v-if="scope.row.inhouseStatus === 4">已中止</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="createUserName" label="创建人" width="140" show-overflow-tooltip="" />
							<el-table-column prop="createTime" label="创建时间" width="140" show-overflow-tooltip="" />
							<el-table-column prop="remark" label="备注" show-overflow-tooltip="" />
							<!-- <el-table-column label="修改记录" width="65" align="center" fixed="right" show-overflow-tooltip>
								<template #default="scope">
									<ModifyRecord :data="scope.row" />
								</template>
							</el-table-column> -->
							<el-table-column label="操作" width="130" align="center" fixed="right" show-overflow-tooltip="" v-if="auth('warehouseInrecord:edit') || auth('warehouseInrecord:delete')">
								<template #default="scope">
									<el-button
										icon="ele-Edit"
										size="small"
										text=""
										type="primary"
										@click="(event) => { 
											event.stopPropagation(); 
											try {
												const isSelected = selectedRows.value.some(item => item.id === scope.row.id);
												if (!isSelected) {
													programmaticSelection.value = true;
													inTable.value.toggleRowSelection(scope.row, true);
												}
											} catch (err) {
												console.error('选中行出错:', err);
											}
											openEditWarehouseInrecord(scope.row);
										}"
										v-auth="'warehouseInrecord:edit'"
										:disabled="scope.row.inhouseStatus === 0 ? false : true"
									>
										编辑
									</el-button>
									<el-button
										icon="ele-Delete"
										size="small"
										text=""
										type="primary"
										@click="(event) => { 
											event.stopPropagation(); 
											try {
												const isSelected = selectedRows.value.some(item => item.id === scope.row.id);
												if (!isSelected) {
													programmaticSelection.value = true;
													inTable.value.toggleRowSelection(scope.row, true);
												}
											} catch (err) {
												console.error('选中行出错:', err);
											}
											delWarehouseInrecord(scope.row);
										}"
										v-auth="'warehouseInrecord:delete'"
										:disabled="scope.row.inhouseStatus === 0 ? false : true"
									>
										删除
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>

					<el-pagination
						v-model:currentPage="tableParams.page"
						v-model:page-size="tableParams.pageSize"
						:total="tableParams.total"
						:page-sizes="[10, 20, 50, 100]"
						small=""
						background=""
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
						layout="total, sizes, prev, pager, next, jumper"
						class="pagination"
					/>
					<editDialog ref="editDialogRef" :title="editWarehouseInrecordTitle" @reloadTable="handleQuery" />
				</el-card>
			</pane>
			<pane :size="BomSize" ref="bomCardRef">
				<warehouseInrecordMX
					:orderDetail="orderDetail"
					ref="table2"
					:orderNumber="orderNumber"
					:orderInhouseStatus="orderInhouseStatus"
					:bomHeight="bomHeight"
					:detailsData="currentDetailsData"
					@reloadTable="handleQuery('current', tableRow)"
				>
				</warehouseInrecordMX>
			</pane>
		</splitpanes>
	</div>
</template>

<script lang="ts" setup="" name="warehouseInrecord">
import { reactive, ref, getCurrentInstance, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { auth } from '/@/utils/authFunction';
import useCounter from '/@/stores/counter';
import editDialog from '/@/views/main/warehouseInrecord/component/editDialog.vue';
import warehouseInrecordMX from '/@/views/main/warehouseInrecordMX/index.vue';
import { pageWarehouseInrecord, deleteWarehouseInrecord, Suspend, Submit, Withdraw } from '/@/api/main/warehouseInrecord';
import { pageWarehouseInrecordMX } from '/@/api/main/warehouseInrecordMX';
import { Splitpanes, Pane } from 'splitpanes';
// import ModifyRecord from '/@/components/table/modifyRecord.vue';
import 'splitpanes/dist/splitpanes.css';
import { useRoute } from 'vue-router';
import { cachedRequest } from '/@/utils/request-cache';

const route = useRoute();

const editDialogRef = ref();
const pagesInstance = getCurrentInstance();
const topCardRef = ref(null);
const inhouseTypeList = reactive([
	{ label: '采购入库', value: 0 },
	{ label: '生产入库', value: 1 },
	{ label: '退货入库', value: 2 },
	{ label: '换货入库', value: 3 },
	{ label: '其他入库', value: 4 },
]);
const counterStore = useCounter();
const filterSlots = (prop: any, list: any) => {
	if (!list.length) {
		return prop;
	}
	return list.find((item: { value: any }) => item.value === prop)?.label || prop;
};
const submit = ref(true);
const loading = ref(false);
const tableData = ref<any>([]);
const selectedRows = ref<any>([]); // 保存选中的行数据


// 在 setup 中初始化 queryParams，并设置默认选中的入库状态项
const queryParams = ref<{
	inhouseStatus: number[];
	[key: string]: any;
}>({
	inhouseStatus: [],
});


let initialized = false;

//加初始化方法
const initializeFilters = () => {
	const statusFromRoute = route.query.inhouseStatus;
	if (statusFromRoute) {
		queryParams.value.inhouseStatus = Array.isArray(statusFromRoute) 
			? statusFromRoute.map(Number)
			: [Number(statusFromRoute)];
	} 
	handleQuery();
	initialized = true;

};
// 在组件挂载时初始化
onMounted(() => {
	initializeFilters();
});

const tableParams = ref({
	page: 1,
	pageSize: 20,
	total: 0,
});
const topSize = ref('58%');
const BomSize = ref('42%');
const tableHeight = ref('340');
const bomHeight = ref('220');
const submitBtnStatus = ref(true); //提交按钮是否可用
const withdrawBtnStatus = ref(true); //撤回按钮是否可用
const discontinueBtnStatus = ref(true); //中止按钮是否可用
const inhouseStatusList = ref([
	{
		label: '待提交',
		value: 0,
	},
	{
		label: '待入库',
		value: 1,
	},
	{
		label: '部分入库',
		value: 2,
	},
	{
		label: '已入库',
		value: 3,
	},
	{
		label: '已中止',
		value: 4,
	},
]); //入库状态列表
const editWarehouseInrecordTitle = ref('');
const orderDetail = ref('');
const orderNumber = ref('');
const rkdMX = ref<any>([]);
const tableRow = ref('');
const table2 = ref();
const getMx = ref([]);
const orderInhouseStatus = ref(0); // 订单状态
const programmaticSelection = ref(false);

// 添加防抖函数和批量处理逻辑
const debounceTimer = ref(null);
const pendingDetailQueries = ref(new Set());

// 防抖的明细查询函数
const debouncedDetailQuery = (rowIds: number[]) => {
	// 清除之前的定时器
	if (debounceTimer.value) {
		clearTimeout(debounceTimer.value);
	}
	
	// 添加到待查询队列
	rowIds.forEach(id => pendingDetailQueries.value.add(id));
	
	// 设置新的定时器，300ms后执行批量查询
	debounceTimer.value = setTimeout(async () => {
		const idsToQuery = Array.from(pendingDetailQueries.value);
		pendingDetailQueries.value.clear();
		
		if (idsToQuery.length > 0) {
			await batchQueryDetails(idsToQuery);
		}
	}, 300);
};

// 批量查询明细数据
const batchQueryDetails = async (rowIds: number[]) => {
	try {
		const rows = tableData.value.filter(row => rowIds.includes(row.id));
		
		// 分批处理，每批最多5个请求
		const batchSize = 5;
		for (let i = 0; i < rows.length; i += batchSize) {
			const batch = rows.slice(i, i + batchSize);
			
			// 并发执行当前批次的查询
			const promises = batch.map(async (row) => {
				try {
					let params = {
						inrecordId: row.id,
						page: 1,
						pageSize: 1000
					};
					
					// 使用缓存请求
					const res = await cachedRequest(
						'warehouseInrecordMX/page',
						params,
						() => pageWarehouseInrecordMX(params),
						2 * 60 * 1000 // 2分钟缓存
					);
					
					const details = res.data.result?.items ?? [];
					
					return {
						rowId: row.id,
						orderNumber: row.orderNumber,
						details: details.map(item => ({
							...item,
							incordNumber: row.orderNumber,
							orderNumber: row.orderNumber,
							inrecordId: row.id
						}))
					};
				} catch (error) {
					console.error(`查询明细失败 - 入库单ID: ${row.id}`, error);
					return { rowId: row.id, orderNumber: row.orderNumber, details: [] };
				}
			});
			
			const batchResults = await Promise.all(promises);
			
			// 更新明细数据
			batchResults.forEach(result => {
				if (result.details.length > 0) {
					// 检查是否已存在相同ID的明细数据，避免重复添加
					const existingDetailIds = currentDetailsData.value.map(item => item.id);
					const newDetails = result.details.filter(item => !existingDetailIds.includes(item.id));
					
					if (newDetails.length > 0) {
						currentDetailsData.value = [...currentDetailsData.value, ...newDetails];
						console.log(`批量查询: 为入库单 ${result.orderNumber} 添加 ${newDetails.length} 条明细数据`);
					}
				}
			});
			
			// 批次间添加小延迟，避免过于频繁的请求
			if (i + batchSize < rows.length) {
				await new Promise(resolve => setTimeout(resolve, 100));
			}
		}
		
		console.log(`批量查询完成，当前明细表总数据: ${currentDetailsData.value.length} 条`);
	} catch (error) {
		console.error('批量查询明细数据失败:', error);
		ElMessage.error('查询明细数据失败，请重试');
	}
};

const handleSelectionChange = (selection: any[]) => {
	if (programmaticSelection.value) {
		programmaticSelection.value = false;
		selectedRows.value = selection;
		
		// 找出新增的选中行ID
		const previousSelectedIds = selectedRows.value.map(row => row.id);
		const currentSelectedIds = selection.map(row => row.id);
		const newlySelectedIds = currentSelectedIds.filter(id => !previousSelectedIds.includes(id));
		
		// 使用防抖批量查询
		if (newlySelectedIds.length > 0) {
			debouncedDetailQuery(newlySelectedIds);
		}
		return;
	}
	
	// 保存之前选中的行ID
	const previousSelectedIds = selectedRows.value.map(row => row.id);
	
	// 更新选中行
	selectedRows.value = selection;
	
	// 找出新增的选中行ID
	const currentSelectedIds = selection.map(row => row.id);
	const newlySelectedIds = currentSelectedIds.filter(id => !previousSelectedIds.includes(id));
	
	// 找出被取消选中的行ID
	const deselectedIds = previousSelectedIds.filter(id => !currentSelectedIds.includes(id));
	
	console.log(`选择变化: 新增${newlySelectedIds.length}行, 取消${deselectedIds.length}行`);
	
	// 使用防抖批量查询新增的行
	if (newlySelectedIds.length > 0) {
		debouncedDetailQuery(newlySelectedIds);
	}
	
	// 处理取消选中的行
	if (deselectedIds.length > 0) {
		// 从明细中移除这些行的明细数据
		currentDetailsData.value = currentDetailsData.value.filter(
			item => !deselectedIds.includes(item.inrecordId)
		);
		console.log(`取消勾选: 移除对应明细数据后剩余 ${currentDetailsData.value.length} 条`);
	}
};

const rowClassName = (row: TableItem) => {
	console.log(row.row);
	return row.row.isSelected ? 'current-row' : '';
};
const handleRowClick = async (row: any) => {
	// 保存当前行数据
	tableRow.value = row;
	orderDetail.value = row.id;
	orderNumber.value = row.orderNumber;
	orderInhouseStatus.value = row.inhouseStatus;
	
	// 切换当前行的选中状态
	const isCurrentlySelected = selectedRows.value.some(item => item.id === row.id);
	inTable.value.toggleRowSelection(row, !isCurrentlySelected);
	
	// 查询当前行的明细数据
	await rkdDetailQuery(row);
	
	if (!isCurrentlySelected) {
		// 检查是否已存在相同ID的明细数据，避免重复添加
		const existingDetailIds = currentDetailsData.value.map(item => item.id);
		
		// 只添加不存在的明细
		const newDetails = rkdMX.value.filter(item => !existingDetailIds.includes(item.id))
			.map(item => ({
				...item,
				incordNumber: row.orderNumber,
				orderNumber: row.orderNumber,
				inrecordId: row.id
			}));
		
		console.log(`添加 ${newDetails.length} 条新明细数据`);
		
		// 将过滤后的明细添加到已有明细中
		currentDetailsData.value = [...currentDetailsData.value, ...newDetails];
	} else {
		// 如果取消选中，从明细中移除该行的明细数据
		currentDetailsData.value = currentDetailsData.value.filter(
			item => item.inrecordId !== row.id
		);
	}
	
	console.log(`当前明细表总数据: ${currentDetailsData.value.length} 条`);
	
	// 设置按钮状态
	if (row.inhouseStatus == 3) {
		submit.value = false;
	} else {
		submit.value = true;
	}
	
	// 设置按钮状态的其他逻辑保持不变
	if (row.inhouseStatus == 0) {
		submitBtnStatus.value = false;
		withdrawBtnStatus.value = true;
		discontinueBtnStatus.value = true;
	} else if (row.inhouseStatus == 1) {
		withdrawBtnStatus.value = false;
		discontinueBtnStatus.value = true;
		submitBtnStatus.value = true;
	} else if (row.inhouseStatus == 2) {
		discontinueBtnStatus.value = false;
		submitBtnStatus.value = true;
		withdrawBtnStatus.value = true;
	} else {
		submitBtnStatus.value = true;
		discontinueBtnStatus.value = true;
		withdrawBtnStatus.value = true;
	}
};
const inTable = ref();

const handleQuery = async (type?: string, row?: any) => {
	loading.value = true;
	tableData.value = [];
	
	// 清空明细数据
	if (type !== 'current' && type !== 'preserve') {
		currentDetailsData.value = [];
		if (inTable.value) {
			inTable.value.clearSelection();
		}
	}
	
	let inhouseStatusParam = '';
	if (queryParams.value && Array.isArray(queryParams.value.inhouseStatus) && queryParams.value.inhouseStatus.length > 0) {
		inhouseStatusParam = queryParams.value.inhouseStatus.join(',');
	}

	const params = {
		...queryParams.value,
		inhouseStatus: inhouseStatusParam,
	};

	var res = await pageWarehouseInrecord(params);
	tableData.value = res.data.result?.items ?? [];
	tableParams.value.total = res.data.result?.total;
	loading.value = false;

	// 处理不同类型的查询结果
	if (type === 'default') {
		// 对于默认情况，选中第一行
		if (tableData.value.length > 0) {
			inTable.value.setCurrentRow(tableData.value[0]);
			handleRowClick(tableData.value[0]);
		} else {
			// 如果没有数据，清空详情区域
			orderDetail.value = '';
			orderNumber.value = '';
			orderInhouseStatus.value = 0;
		}
	} else if (type === 'current' || type === 'preserve') {
		// 对于编辑、入库操作或保留当前行的操作，选中指定行
		if (row) {
			const targetRow = tableData.value.find((r) => r.id === row.id);
			if (targetRow) {
				inTable.value.setCurrentRow(targetRow);
				handleRowClick(targetRow);
			}
		}
	}
};

// 重置查询条件
const resetQuery = () => {
	// 清空 queryParams 对象
	queryParams.value = {};
	handleQuery();
};
// 打开新增页面
const openAddWarehouseInrecord = () => {
	editWarehouseInrecordTitle.value = '添加入库单';
	editDialogRef.value.openDialog({}, []);
};

const withdraws = () => {
	if (selectedRows.value.length == 0) {
		ElMessage.warning('请先选中要撤回的记录');
		return;
	}
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		if (item.inhouseStatus == 1 || item.inhouseStatus == 2) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.orderNumber + ' 状态不正确，无法撤回');
			return;
		}
	}
	
	// 记住当前选中行的ID
	const currentRowId = tableRow.value?.id;
	
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要撤回吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await Withdraw(listPurchaseIds.value);
			
			// 清空明细表
			currentDetailsData.value = [];
			
			// 完成后重新查询但不自动选中任何行
			await handleQuery('noselect');
			
			ElMessage.success('撤回成功');
		})
		.catch(() => {});
};
const suspend = () => {
	if (selectedRows.value.length == 0) {
		ElMessage.warning('请先选中要中止的记录');
		return;
	}
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		if (item.inhouseStatus == 2) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.orderNumber + ' 状态不正确，无法中止');
			return;
		}
	}
	
	// 记住当前选中行的ID
	const currentRowId = tableRow.value?.id;
	
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要中止吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await Suspend(listPurchaseIds.value);
			
			// 清空明细表
			currentDetailsData.value = [];
			
			// 完成后重新查询但不自动选中任何行
			await handleQuery('noselect');
			
			ElMessage.success('已中止');
		})
		.catch(() => {});
};

const handleTz = (value) => {
	topSize.value = value[0].size;
	BomSize.value = value[1].size;
	const height = window.innerHeight - 200;
	tableHeight.value = (parseFloat(pagesInstance.refs.topCardRef.style.height) / 100) * height - 80;
	bomHeight.value = (parseFloat(pagesInstance?.refs.bomCardRef.style.height) / 100) * height - 110;
};

// 打开编辑页面
const openEditWarehouseInrecord = async (row: any) => {
	// debugger;
	await rkdDetailQuery(row);
	editWarehouseInrecordTitle.value = '编辑入库单';
	editDialogRef.value.openDialog(row, rkdMX.value);
};
// 查询入库单明细数据
const rkdDetailQuery = async (row: any) => {
	let params = {
		inrecordId: row.id,
		page: 1,
		pageSize: 1000 // 确保获取所有数据
	};
	
	// 使用缓存请求
	const res = await cachedRequest(
		'warehouseInrecordMX/page',
		params,
		() => pageWarehouseInrecordMX(params),
		2 * 60 * 1000 // 2分钟缓存
	);
	
	rkdMX.value = res.data.result?.items ?? [];
	
	// 确保每条数据都有入库单号
	rkdMX.value = rkdMX.value.map(item => ({
		...item,
		incordNumber: row.orderNumber,
		orderNumber: row.orderNumber,
		inrecordId: row.id
	}));
	
	console.log(`查询到明细数据 ${rkdMX.value.length} 条`);
};
// 删除
const delWarehouseInrecord = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await deleteWarehouseInrecord(row);
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	tableParams.value.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	tableParams.value.page = val;
	handleQuery();
};
const submitOrder = async () => {
	if (selectedRows.value.length == 0) {
		ElMessage.warning('请先选中要提交的记录');
		return;
	}
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		if (item.inhouseStatus == 0 || item.inhouseStatus == -1) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.orderNumber + ' 状态不正确，无法提交');
			return;
		}
	}
	
	// 记住当前选中行的ID
	const currentRowId = tableRow.value?.id;
	
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要提交吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await Submit(listPurchaseIds.value);
			
			// 清空明细表
			currentDetailsData.value = [];
			
			// 完全重新加载数据 - 但不自动选中任何行
			await handleQuery('noselect');
			
			ElMessage.success('提交成功');
		})
		.catch(() => {});
};
// handleQuery();

// 添加一个新的响应式引用
const currentDetailsData = ref([]);
</script>
<style lang="scss" scoped>
.topCard {
	margin-top: 8px;
	height: 100%;
	position: relative;
	top: 0;
}

.toptable {
	width: 100%;
}

.el-form--inline .el-form-item {
	margin-right: 10px;
}

//必须设置这样，否则滚动的话，会连tabs的标题栏一起滚动
:deep(.el-tabs__content) {
	position: relative;
	height: 100%;
	padding: 0px;
}

// .pagination{
// 	position: absolute;
// 	bottom:27px;
// }
:deep(.splitpanes.default-theme .splitpanes__splitter) {
	background: #f0f0f0;
}

@media (max-height: 920px) {
	.default-theme {
		height: calc(80vh - 50px) !important;
	}
}

@media (max-height: 640px) {
	.default-theme {
		height: calc(80vh - 120px) !important;
	}
}
</style>
