﻿using Admin.NET.Application.Enum;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 付款单基础输入参数
/// </summary>
public class PaymentOrderBaseInput
{
    /// <summary>
    /// 付款单号
    /// </summary>
    public virtual string? PaymentNo { get; set; }
    /// <summary>
    /// 供应商ID
    /// </summary>
    public virtual long? SupplierId { get; set; }
    /// <summary>
    /// 合同编号
    /// </summary>
    public virtual string? ContractNum { get; set; }
    /// <summary>
    /// 单据金额
    /// </summary>
    public virtual decimal? DocumentAmount { get; set; }
    /// <summary>
    /// 已付金额
    /// </summary>
    public virtual decimal? AmountPaid { get; set; }
    /// <summary>
    /// 已收票金额
    /// </summary>
    public virtual decimal? ReceivedAmount { get; set; }
    /// <summary>
    /// 成本类型
    /// </summary>
    public virtual int? CostType { get; set; }
    /// <summary>
    /// 支出名目
    /// </summary>
    public virtual string? ExpenditureCategory { get; set; }
    /// <summary>
    /// 二级科目
    /// </summary>
    public virtual string? SecondaryAccount { get; set; }
    /// <summary>
    /// 付款状态
    /// </summary>
    public virtual PaymentStatusEnum PaymentStatus { get; set; }
    /// <summary>
    /// 交易账户
    /// </summary>
    public virtual long? Trading { get; set; }
    /// <summary>
    /// 发票状态
    /// </summary>
    public virtual int? InvoiceStatus { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Notes { get; set; }
    /// <summary>
    /// 摘要
    /// </summary>
    public virtual string? Abstract { get; set; }

    public virtual string? InvoiceNo { get; set; }
}

/// <summary>
/// 付款单分页查询输入参数
/// </summary>
public class PaymentOrderInput : BasePageInput
{
    /// <summary>
    /// 单位名称
    /// </summary>
    public string? UnitName { get; set; }

    /// <summary>
    /// 收款状态
    /// </summary>
    public string? PaymentStatus { get; set; }

    /// <summary>
    /// 发票状态
    /// </summary>
    public int? InvoiceStatus { get; set; }

}

/// <summary>
/// 付款单增加输入参数
/// </summary>
public class AddPaymentOrderInput : PaymentOrderBaseInput
{
}

/// <summary>
/// 付款单删除输入参数
/// </summary>
public class DeletePaymentOrderInput : BaseIdInput
{
}

/// <summary>
/// 付款单更新输入参数
/// </summary>
public class UpdatePaymentOrderInput : PaymentOrderBaseInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 付款单主键查询输入参数
/// </summary>
public class QueryByIdPaymentOrderInput : DeletePaymentOrderInput
{

}
