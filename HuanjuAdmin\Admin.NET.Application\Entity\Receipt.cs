﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("receipt","")]
    [Tenant("1300000000001")]
    public class Receipt  : EntityTenant
    {
        /// <summary>
        /// 收款单号
        /// </summary>
        [SugarColumn(ColumnDescription = "收款单号", Length = 50)]
        public string? ReceiptNo { get; set; }
        /// <summary>
        /// 单位名称
        /// </summary>
        [SugarColumn(ColumnDescription = "单位名称", Length = 50)]
        public string? UnitName { get; set; }
        /// <summary>
        /// 合同编号
        /// </summary>
        [SugarColumn(ColumnDescription = "合同编号", Length = 50)]
        public string? ContractNum { get; set; }
        /// <summary>
        /// 上级单号
        /// </summary>
        [SugarColumn(ColumnDescription = "上级单号", Length = 50)]
        public string? SuperiorOrder { get; set; }
        /// <summary>
        /// 单据金额
        /// </summary>
        [SugarColumn(ColumnDescription = "单据金额")]
        public decimal? DocumentAmount { get; set; }
        /// <summary>
        /// 已收金额
        /// </summary>
        [SugarColumn(ColumnDescription = "已收金额")]
        public decimal? AmountReceived { get; set; }
        /// <summary>
        /// 已开票金额
        /// </summary>
        [SugarColumn(ColumnDescription = "已开票金额")]
        public decimal? InvoicedAmount { get; set; }
        /// <summary>
        /// 收入类型
        /// </summary>
        [SugarColumn(ColumnDescription = "收入类型")]
        public string? IncomeType { get; set; }
        /// <summary>
        /// 收入名目
        /// </summary>
        [SugarColumn(ColumnDescription = "收入名目")]
        public string? IncomeCategory { get; set; }
        /// <summary>
        /// 收款状态
        /// </summary>
        [SugarColumn(ColumnDescription = "收款状态")]
        public int? PaymentStatus { get; set; }
        /// <summary>
        /// 交易方式
        /// </summary>
        [SugarColumn(ColumnDescription = "交易账户")]
        public long? Trading { get; set; }
        /// <summary>
        /// 发票状态
        /// </summary>
        [SugarColumn(ColumnDescription = "发票状态")]
        public int? InvoiceStatus { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 500)]
        public string? Notes { get; set; }
        /// <summary>
        /// 摘要
        /// </summary>
        [SugarColumn(ColumnDescription = "摘要", Length = 500)]
        public string? Abstract { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        [SugarColumn(ColumnDescription = "摘要", Length = 100)]
        public string? InvoiceNo { get; set; }
    }
}