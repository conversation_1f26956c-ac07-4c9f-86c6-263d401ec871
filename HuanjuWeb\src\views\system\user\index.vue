<template>
	<div class="sys-user-container">
		<el-row :gutter="8" style="width: 100%">
			<!-- <el-col :span="4" :xs="24">
				<OrgTree ref="orgTreeRef" @node-click="nodeClick" />
			</el-col> -->
			<el-col :span="24" :xs="24">
				<el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
					<el-form :model="state.queryParams" ref="queryForm" :inline="true">
						<el-form-item label="账号" prop="account">
							<el-input placeholder="账号" clearable @keyup.enter="handleQuery" v-model="state.queryParams.account" />
						</el-form-item>
						<el-form-item label="姓名" prop="realName">
							<el-input placeholder="姓名" clearable @keyup.enter="handleQuery" v-model="state.queryParams.realName" />
						</el-form-item>
						<el-form-item label="手机号码" prop="phone">
							<el-input placeholder="手机号码" clearable @keyup.enter="handleQuery" v-model="state.queryParams.phone" />
						</el-form-item>
						<el-form-item>
							<el-button-group>
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'sysUser:page'"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
							</el-button-group>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" icon="ele-Plus" @click="openAddUser" v-auth="'sysUser:add'"> 新增 </el-button>
						</el-form-item>
					</el-form>
				</el-card>

				<el-card class="full-table" shadow="hover" style="margin-top: 8px">
					<el-table :data="state.userData" style="width: 100%" v-loading="state.loading" border>
						<el-table-column type="index" label="序号" width="55" align="center" fixed />
						<el-table-column prop="account" label="账号" width="120" fixed show-overflow-tooltip />
						<!-- <el-table-column prop="nickName" label="昵称" width="120" show-overflow-tooltip />
						<el-table-column label="头像" width="80" align="center" show-overflow-tooltip>
							<template #default="scope">
								<el-avatar :src="scope.row.avatar" size="small">{{ scope.row.nickName?.slice(0, 1) ??
									scope.row.realName?.slice(0, 1) }} </el-avatar>
							</template>
						</el-table-column> -->
						<el-table-column prop="realName" label="姓名" width="120" show-overflow-tooltip />
						<!-- <el-table-column label="出生日期" width="100" align="center" show-overflow-tooltip>
							<template #default="scope">
								{{ formatDate(new Date(scope.row.birthday), 'YYYY-mm-dd') }}
							</template>
						</el-table-column>
						<el-table-column label="性别" width="70" align="center" show-overflow-tooltip>
							<template #default="scope">
								<el-tag type="success" v-if="scope.row.sex === 1"> 男 </el-tag>
								<el-tag type="danger" v-else> 女 </el-tag>
							</template>
						</el-table-column> -->
						<el-table-column prop="phone" label="手机号码" width="120" align="center" show-overflow-tooltip />
						<el-table-column prop="posname" label="职位" width="120" align="center" show-overflow-tooltip />
						<el-table-column label="状态" width="70" align="center" show-overflow-tooltip>
							<template #default="scope">
								<el-switch v-model="scope.row.status" :active-value="1" :inactive-value="2" size="small" @change="changeStatus(scope.row)" v-auth="'sysUser:setStatus'" />
							</template>
						</el-table-column>
						<el-table-column prop="orderNo" label="排序" width="70" align="center" show-overflow-tooltip />
						<el-table-column prop="createUserName" label="创建人" width="160" show-overflow-tooltip />
						<el-table-column prop="createTime" label="创建时间" width="160" show-overflow-tooltip />
						<el-table-column label="到期时间" width="160" show-overflow-tooltip />
						<el-table-column prop="remark" label="备注" show-overflow-tooltip />
						<el-table-column label="操作" width="110" align="center" fixed="right" show-overflow-tooltip>
							<template #default="scope">
								<!-- 只有当不是管理员时才显示操作按钮 -->
								<template v-if="scope.row.accountType !== 4 && scope.row.accountType !== 999">
									<el-button icon="ele-Edit" size="small" text type="primary" @click="openEditUser(scope.row)" v-auth="'sysUser:update'"> 编辑 </el-button>
									<el-dropdown>
										<el-button icon="ele-MoreFilled" size="small" text type="primary" style="padding-left: 12px" />
										<template #dropdown>
											<el-dropdown-menu>
												<el-dropdown-item icon="ele-RefreshLeft" @click="resetUserPwd(scope.row)" :disabled="!auth('sysUser:resetPwd')"> 重置密码 </el-dropdown-item>
												<el-dropdown-item icon="ele-Delete" @click="delUser(scope.row)" divided :disabled="!auth('sysUser:delete')"> 删除账号 </el-dropdown-item>
											</el-dropdown-menu>
										</template>
									</el-dropdown>
								</template>
								<!-- 如果是管理员，显示标记 -->
								<template v-else>
									<el-tag size="small" type="info">管理员账号</el-tag>
								</template>
							</template>
						</el-table-column>
					</el-table>
					<el-pagination
						v-model:currentPage="state.tableParams.page"
						v-model:page-size="state.tableParams.pageSize"
						:total="state.tableParams.total"
						:page-sizes="[10, 20, 50, 100]"
						small
						background
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
						layout="total, sizes, prev, pager, next, jumper"
					/>
				</el-card>
			</el-col>
		</el-row>

		<EditUser ref="editUserRef" :title="state.editUserTitle" @handleQuery="handleQuery" />
	</div>
</template>

<script lang="ts" setup name="sysUser">
import { onMounted, reactive, ref, watch, onActivated } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessageBox, ElMessage } from 'element-plus';
import { formatDate } from '/@/utils/formatTime';
import { auth } from '/@/utils/authFunction';
import OrgTree from '/@/views/system/org/component/orgTree.vue';
import EditUser from '/@/views/system/user/component/editUser.vue';

import { getAPI } from '/@/utils/axios-utils';
import { SysUserApi, SysOrgApi } from '/@/api-services/api';
import { SysUser, SysOrg } from '/@/api-services/models';
import { open } from 'fs/promises';
// import { watch } from 'fs';
const isOpen = ref('false');
const route = useRoute();

const orgTreeRef = ref<InstanceType<typeof OrgTree>>();
const editUserRef = ref<InstanceType<typeof EditUser>>();
const state = reactive({
	loading: false,
	userData: [] as Array<SysUser>,
	orgTreeData: [] as Array<SysOrg>,
	queryParams: {
		orgId: -1,
		account: undefined,
		realName: undefined,
		phone: undefined,
	},
	tableParams: {
		page: 1,
		pageSize: 10,
		total: 0 as any,
	},
	editUserTitle: '',
});

onMounted(() => {
	loadOrgData();
	handleQuery();
	isOpenShow();
});

// 查询机构数据
const loadOrgData = async () => {
	state.loading = true;
	var res = await getAPI(SysOrgApi).apiSysOrgListGet(0);
	state.orgTreeData = res.data.result ?? [];
	state.loading = false;
};

// 查询操作
const handleQuery = async () => {
	state.loading = true;
	let params = Object.assign(state.queryParams, state.tableParams);
	var res = await getAPI(SysUserApi).apiSysUserPagePost(params);
	state.userData = res.data.result?.items ?? [];
	state.tableParams.total = res.data.result?.total;
	state.loading = false;
};

handleQuery();
// 重置操作
const resetQuery = () => {
	state.queryParams.orgId = -1;
	state.queryParams.account = undefined;
	state.queryParams.realName = undefined;
	state.queryParams.phone = undefined;
	handleQuery();
};

// 打开新增页面
const openAddUser = () => {
	state.editUserTitle = '添加账号';
	editUserRef.value?.openDialog({});
};
// 打开新增页面
const openAddUserGet = (row: any) => {
	state.editUserTitle = '添加账号';
	editUserRef.value?.openDialog({ row });
};

// 打开编辑页面
const openEditUser = (row: any) => {
    if (row.accountType === 4 || row.accountType === 999) {
        ElMessage.warning('管理员账号不允许编辑');
        return;
    }
    state.editUserTitle = '编辑账号';
    console.log('编辑数据', row);
    editUserRef.value?.openDialog(row);
};

// 删除
const delUser = (row: any) => {
    if (row.accountType === 4 || row.accountType === 999) {
        ElMessage.warning('管理员账号不允许删除');
        return;
    }
    ElMessageBox.confirm(`确定删除账号：【${row.account}】?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
            await getAPI(SysUserApi).apiSysUserDeletePost({ id: row.id });
            handleQuery();
            ElMessage.success('删除成功');
        })
        .catch(() => { });
};

// 重置密码
const resetUserPwd = async (row: any) => {
    if (row.accountType === 4 || row.accountType === 999) {
        ElMessage.warning('管理员账号不允许重置密码');
        return;
    }
    ElMessageBox.confirm(`确定重置密码：【${row.account}】?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
            await getAPI(SysUserApi).apiSysUserResetPwdPost({ id: row.id });
            ElMessage.success('密码重置成功：123456');
        })
        .catch(() => { });
};

// 修改状态
const changeStatus = (row: any) => {
    if (row.accountType === 4 || row.accountType === 999) {
        ElMessage.warning('管理员账号不允许修改状态');
        row.status = row.status === 1 ? 2 : 1; // 恢复开关状态
        return;
    }
    getAPI(SysUserApi)
        .apiSysUserSetStatusPost({ id: row.id, status: row.status })
        .then(() => {
            ElMessage.success('账号状态设置成功');
        })
        .catch(() => {
            row.status = row.status === 1 ? 2 : 1;
        });
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	state.tableParams.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	state.tableParams.page = val;
	handleQuery();
};

// 树组件点击
const nodeClick = async (node: any) => {
	state.queryParams.orgId = node.id;
	state.queryParams.account = undefined;
	state.queryParams.realName = undefined;
	state.queryParams.phone = undefined;
	handleQuery();
};
onActivated(() => {
	isOpenShow();
});
const isOpenShow = () => {
	if (history.state.realName) {
		let list = {
			realName: history.state.realName,
			phone: history.state.phone,
			orderNo: history.state.orderNo,
			posId: history.state.posId,
			account: '',
		};
		openAddUserGet(list);
	} else {
	}
};
</script>
