﻿using Microsoft.AspNetCore.Components.Web.Virtualization;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 转换记录基础输入参数
/// </summary>
public class WarehouseconvertrecordBaseInput
{
    /// <summary>
    /// 序号
    /// </summary>
    public virtual int Order { get; set; }

    /// <summary>
    /// 商品
    /// </summary>
    public virtual string? Commodity { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public virtual int? Num { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Notes { get; set; }
    /// <summary>
    /// 转换类型
    /// </summary>
    public virtual int? Type { get; set; }
    /// <summary>
    /// 仓库
    /// </summary>
    public virtual string? Warehouse { get; set; }
    /// <summary>
    /// 唯一码
    /// </summary>
    public virtual string? UniqueCode { get; set; }
    /// <summary>
    /// 批次号
    /// </summary>
    public virtual string? BatchNumber { get; set; }

}

    /// <summary>
    /// 转换记录分页查询输入参数
    /// </summary>
    public class WarehouseconvertrecordInput : BasePageInput
    {
        /// <summary>
        /// 商品
        /// </summary>
        public string? Commodity { get; set; }
        
        /// <summary>
        /// 数量
        /// </summary>
        public int Num { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }
        
    }

    /// <summary>
    /// 转换记录增加输入参数
    /// </summary>
    public class AddWarehouseconvertrecordInput : WarehouseconvertrecordBaseInput
    {
    }

    /// <summary>
    /// 转换记录删除输入参数
    /// </summary>
    public class DeleteWarehouseconvertrecordInput : BaseIdInput
    {
    }

    /// <summary>
    /// 转换记录更新输入参数
    /// </summary>
    public class UpdateWarehouseconvertrecordInput : WarehouseconvertrecordBaseInput
    {
    }

    /// <summary>
    /// 转换记录主键查询输入参数
    /// </summary>
    public class QueryByIdWarehouseconvertrecordInput : DeleteWarehouseconvertrecordInput
    {

    }
