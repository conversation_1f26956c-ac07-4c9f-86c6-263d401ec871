import { defineStore } from 'pinia';
import { GetCommodity } from '/@/api/main/warehousegoods';
import mitt from '/@/utils/mitt';

interface GoodsItem {
    value: string | number;
    label: string;
    brand?: string;
    code?: string;
    specs?: string;
    unit?: string;
    id?: string | number;
    auxiliaryunit?: string;
    name?: string;
}

export const useGoodsStore = defineStore('goods', {
    state: () => ({
        goodsList: [] as GoodsItem[],
        lastUpdateTime: null as number | null,
        isLoading: false,
        cacheTimeout: 1000 * 60 * 60 * 4 // 4小时缓存
    }),

    getters: {
        isExpired(): boolean {
            if (!this.lastUpdateTime) return true;
            return Date.now() - this.lastUpdateTime > this.cacheTimeout;
        },

        // 获取下拉列表数据
        dropdownList(): GoodsItem[] {
            return this.goodsList
                .filter(item => item.id != null)  // 过滤掉 id 为 null 或 undefined 的项
                .map(item => ({
                    value: item.id!,  // 使用非空断言，因为我们已经过滤掉了 null/undefined
                    label: item.name || item.label,
                    id: item.id!
                }));
        }
    },

    actions: {
        // 获取并缓存所有商品信息
        async fetchGoodsList(forceRefresh = false) {
            if (this.isLoading) {
                await new Promise(resolve => {
                    const checkLoading = setInterval(() => {
                        if (!this.isLoading) {
                            clearInterval(checkLoading);
                            resolve(true);
                        }
                    }, 100);
                });
                return this.goodsList;
            }

            if (!this.isExpired && !forceRefresh && this.goodsList.length > 0) {
                return this.goodsList;
            }

            try {
                this.isLoading = true;
                const res = await GetCommodity();
                if (res.data.result) {
                    this.goodsList = res.data.result.map((item: any) => ({
                        id: item.id,
                        value: item.id,
                        label: item.name,
                        name: item.name,
                        brand: item.brand,
                        code: item.code,
                        specs: item.specs,
                        unit: item.unit,
                        auxiliaryunit: item.auxiliaryunit
                    }));
                    this.lastUpdateTime = Date.now();
                    localStorage.setItem('goodsList', JSON.stringify({
                        data: this.goodsList,
                        timestamp: this.lastUpdateTime
                    }));
                }
                return this.goodsList;
            } catch (error) {
                console.error('获取商品列表失败:', error);
                const cached = localStorage.getItem('goodsList');
                if (cached) {
                    const { data, timestamp } = JSON.parse(cached);
                    if (Date.now() - timestamp <= this.cacheTimeout) {
                        this.goodsList = data;
                        this.lastUpdateTime = timestamp;
                        return this.goodsList;
                    }
                }
                return [];
            } finally {
                this.isLoading = false;
            }
        },

        // 根据ID获取商品详情
        getGoodsDetail(id: string | number): GoodsItem | undefined {
            return this.goodsList.find(item => item.id === id || item.value === id);
        },

        // 强制刷新缓存
        async refreshGoodsList() {
            return this.fetchGoodsList(true);
        },

        // 清除所有缓存
        clearCache() {
            this.goodsList = [];
            this.lastUpdateTime = null;
            localStorage.removeItem('goodsList');
        },

        // 添加一个初始化函数，用于设置事件监听
        init() {
            mitt.on('goodsUpdated', () => {
                this.refreshGoodsList();
            });
        },

        // 添加一个触发更新的方法
        async triggerUpdate() {
            await this.refreshGoodsList();
            mitt.emit('goodsUpdated');
        }
    }
}); 