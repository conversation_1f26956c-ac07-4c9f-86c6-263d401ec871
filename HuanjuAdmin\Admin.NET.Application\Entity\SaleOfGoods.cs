﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("SaleOfGoods","")]
    [Tenant("1300000000001")]
    public class SaleOfGoods  : EntityBase
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        [SugarColumn(ColumnDescription = "商品ID")]
        public long goodsId { get; set; }
        /// <summary>
        /// 商品
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(goodsId))]
        public Warehousegoods Warehousegoods { get; set; }
        /// <summary>
        /// 商品名称
        /// </summary>
        [SugarColumn(ColumnDescription = "商品名称", Length = 50)]
        public string? TradeName { get; set; }
        /// <summary>
        /// 合同数量
        /// </summary>
        [SugarColumn(ColumnDescription = "合同数量")]
        public int? puchQty { get; set; }
        /// <summary>
        /// 销售单号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "销售Id", Length = 50)]
        public long SalesOrder { get; set; }
        [Required]
        [SugarColumn(ColumnDescription = "单价")]
        public decimal? puchPrice { get; set; }

        [Required]
        [SugarColumn(ColumnDescription = "金额")]
        public decimal? puchAmt { get; set; }
    }
}