<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Admin.NET.Core</name>
    </assembly>
    <members>
        <member name="T:Admin.NET.Core.ConstAttribute">
            <summary>
            常量特性
            </summary>
        </member>
        <member name="T:Admin.NET.Core.CustomUnifyResultAttribute">
            <summary>
            自定义规范化结果特性
            </summary>
        </member>
        <member name="T:Admin.NET.Core.IgnoreUpdateAttribute">
            <summary>
            忽略更新种子数据特性
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysTableAttribute">
            <summary>
            系统表特性
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SystemTableAttribute">
            <summary>
            系统表特性
            </summary>
        </member>
        <member name="M:Admin.NET.Core.CacheSetup.AddCache(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            缓存注册（新生命Redis组件）
            </summary>
            <param name="services"></param>
        </member>
        <member name="T:Admin.NET.Core.SqlSugarCache">
            <summary>
            SqlSugar二级缓存
            </summary>
        </member>
        <member name="M:Admin.NET.Core.LazyCaptchaSetup.AddLazyCaptcha(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            验证码初始化
            </summary>
            <param name="services"></param>
        </member>
        <member name="T:Admin.NET.Core.RandomCaptcha">
            <summary>
            随机验证码
            </summary>
        </member>
        <member name="M:Admin.NET.Core.RandomCaptcha.ChangeOptions(Lazy.Captcha.Core.CaptchaOptions)">
            <summary>
            更新选项
            </summary>
            <param name="options"></param>
        </member>
        <member name="T:Admin.NET.Core.CacheConst">
            <summary>
            缓存相关常量
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CacheConst.KeyUser">
            <summary>
            用户缓存
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CacheConst.KeyMenu">
            <summary>
            菜单缓存
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CacheConst.KeyPermission">
            <summary>
            权限缓存（按钮集合）
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CacheConst.KeyOrgIdList">
            <summary>
            机构Id集合缓存
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CacheConst.KeyMaxDataScope">
            <summary>
            角色最大数据范围缓存
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CacheConst.KeyVerCode">
            <summary>
            验证码缓存
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CacheConst.KeyAll">
            <summary>
            所有缓存关键字集合
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CacheConst.KeyTimer">
            <summary>
            定时任务缓存
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CacheConst.KeyOnlineUser">
            <summary>
            在线用户缓存
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CacheConst.KeyConst">
            <summary>
            常量下拉框
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CacheConst.SwaggerLogin">
            <summary>
            swagger登录缓存
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CacheConst.KeyTenant">
            <summary>
            租户缓存
            </summary>
        </member>
        <member name="T:Admin.NET.Core.ClaimConst">
            <summary>
            Claim相关常量
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ClaimConst.UserId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ClaimConst.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ClaimConst.RealName">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ClaimConst.NickName">
            <summary>
            昵称
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ClaimConst.AccountType">
            <summary>
            账号类型
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ClaimConst.TenantId">
            <summary>
            租户Id
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ClaimConst.OrgId">
            <summary>
            组织机构Id
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ClaimConst.OrgName">
            <summary>
            组织机构名称
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ClaimConst.OpenId">
            <summary>
            微信OpenId
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ClaimConst.LoginMode">
            <summary>
            登录模式PC、APP
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ClaimConst.zyczydm">
            <summary>
            操作员代码
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ClaimConst.zyid">
            <summary>
            id
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ClaimConst.nsrsbh">
            <summary>
            纳税人识别号
            </summary>
        </member>
        <member name="T:Admin.NET.Core.CommonConst">
            <summary>
            通用常量
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CommonConst.SysDemoEnv">
            <summary>
            演示环境开关
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CommonConst.SysPassword">
            <summary>
            默认密码
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CommonConst.SysSecondVer">
            <summary>
            登录二次验证
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CommonConst.SysCaptcha">
            <summary>
            开启图形验证码
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CommonConst.SysWatermark">
            <summary>
            开启水印
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CommonConst.SysOpLog">
            <summary>
            开启操作日志
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CommonConst.SysTokenExpire">
            <summary>
            Token过期时间
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CommonConst.SysRefreshTokenExpire">
            <summary>
            RefreshToken过期时间
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CommonConst.SysSingleLogin">
            <summary>
            单用户登录
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CommonConst.SysAdminRole">
            <summary>
            系统管理员角色编码
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CommonConst.SysSensitiveDetection">
            <summary>
            开启全局脱敏处理（默认不开启）
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SqlSugarConst">
            <summary>
            SqlSugar相关常量
            </summary>
        </member>
        <member name="F:Admin.NET.Core.SqlSugarConst.ConfigId">
            <summary>
            默认数据库标识（默认租户）
            </summary>
        </member>
        <member name="F:Admin.NET.Core.SqlSugarConst.PrimaryKey">
            <summary>
            默认表主键
            </summary>
        </member>
        <member name="F:Admin.NET.Core.SqlSugarConst.HuanJuId">
            <summary>
            智云平台标识（默认租户）
            </summary>
        </member>
        <member name="T:Admin.NET.Core.EntityBaseId">
            <summary>
            框架实体基类Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EntityBaseId.Id">
            <summary>
            雪花Id
            </summary>
        </member>
        <member name="T:Admin.NET.Core.EntityBase">
            <summary>
            框架实体基类
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EntityBase.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EntityBase.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EntityBase.CreateUserId">
            <summary>
            创建者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EntityBase.UpdateUserId">
            <summary>
            修改者Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EntityBase.IsDelete">
            <summary>
            软删除
            </summary>
        </member>
        <member name="T:Admin.NET.Core.EntityBaseData">
            <summary>
            业务数据实体基类(数据权限)
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EntityBaseData.CreateOrgId">
            <summary>
            创建者部门Id
            </summary>
        </member>
        <member name="T:Admin.NET.Core.EntityTenant">
            <summary>
            租户基类实体
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EntityTenant.TenantId">
            <summary>
            租户Id
            </summary>
        </member>
        <member name="T:Admin.NET.Core.EntityTenantId">
            <summary>
            租户基类实体Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EntityTenantId.TenantId">
            <summary>
            租户Id
            </summary>
        </member>
        <member name="T:Admin.NET.Core.IDeletedFilter">
            <summary>
            假删除接口过滤器
            </summary>
        </member>
        <member name="P:Admin.NET.Core.IDeletedFilter.IsDelete">
            <summary>
            软删除
            </summary>
        </member>
        <member name="T:Admin.NET.Core.ITenantIdFilter">
            <summary>
            租户Id接口过滤器
            </summary>
        </member>
        <member name="P:Admin.NET.Core.ITenantIdFilter.TenantId">
            <summary>
            租户Id
            </summary>
        </member>
        <member name="T:Admin.NET.Core.IOrgIdFilter">
            <summary>
            机构Id接口过滤器
            </summary>
        </member>
        <member name="P:Admin.NET.Core.IOrgIdFilter.CreateOrgId">
            <summary>
            创建者部门Id
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysCodeGen">
            <summary>
            代码生成表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGen.AuthorName">
            <summary>
            作者姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGen.TablePrefix">
            <summary>
            是否移除表前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGen.GenerateType">
            <summary>
            生成方式
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGen.ConfigId">
            <summary>
            库定位器名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGen.DbName">
            <summary>
            数据库名(保留字段)
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGen.DbType">
            <summary>
            数据库类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGen.ConnectionString">
            <summary>
            数据库链接
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGen.TableName">
            <summary>
            数据库表名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGen.NameSpace">
            <summary>
            命名空间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGen.BusName">
            <summary>
            业务名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGen.MenuPid">
            <summary>
            菜单编码
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysCodeGenConfig">
            <summary>
            代码生成字段配置表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.CodeGenId">
            <summary>
            代码生成主表Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.ColumnName">
            <summary>
            数据库字段名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.ColumnComment">
            <summary>
            字段描述
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.NetType">
            <summary>
            .NET数据类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.EffectType">
            <summary>
            作用类型（字典）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.FkEntityName">
            <summary>
            外键实体名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.FkTableName">
            <summary>
            外键表名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.FkColumnName">
            <summary>
            外键显示字段
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.FkColumnNetType">
            <summary>
            外键显示字段.NET类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.DictTypeCode">
            <summary>
            字典编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.WhetherRetract">
            <summary>
            列表是否缩进（字典）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.WhetherRequired">
            <summary>
            是否必填（字典）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.QueryWhether">
            <summary>
            是否是查询条件
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.QueryType">
            <summary>
            查询方式
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.WhetherTable">
            <summary>
            列表显示
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.WhetherAddUpdate">
            <summary>
            增改
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.ColumnKey">
            <summary>
            主键
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.DataType">
            <summary>
            数据库中类型（物理类型）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.WhetherCommon">
            <summary>
            是否通用字段
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.DisplayColumn">
            <summary>
            显示文本字段
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.ValueColumn">
            <summary>
            选中值字段
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysCodeGenConfig.PidColumn">
            <summary>
            父级字段
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysConfig">
            <summary>
            系统参数配置表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysConfig.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysConfig.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysConfig.Value">
            <summary>
            属性值
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysConfig.SysFlag">
            <summary>
            是否是内置参数（Y-是，N-否）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysConfig.GroupCode">
            <summary>
            分组编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysConfig.OrderNo">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysConfig.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysDictData">
            <summary>
            系统字典值表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictData.DictTypeId">
            <summary>
            字典类型Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictData.DictType">
            <summary>
            字典类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictData.Value">
            <summary>
            值
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictData.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictData.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictData.TagType">
            <summary>
            显示样式-标签颜色
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictData.StyleSetting">
            <summary>
            显示样式-Style(控制显示样式)
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictData.ClassSetting">
            <summary>
            显示样式-Class(控制显示样式)
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictData.OrderNo">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictData.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictData.ExtData">
            <summary>
            拓展数据(保存业务功能的配置项)
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictData.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysDictType">
            <summary>
            系统字典类型表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictType.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictType.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictType.OrderNo">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictType.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictType.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysDictType.Children">
            <summary>
            字典值集合
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysFile">
            <summary>
            系统文件表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysFile.Provider">
            <summary>
            提供者
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysFile.BucketName">
            <summary>
            仓储名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysFile.FileName">
            <summary>
            文件名称（上传时名称）
            </summary>文件名称
        </member>
        <member name="P:Admin.NET.Core.SysFile.Suffix">
            <summary>
            文件后缀
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysFile.FilePath">
            <summary>
            存储路径
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysFile.SizeKb">
            <summary>
            文件大小KB
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysFile.SizeInfo">
            <summary>
            文件大小信息-计算后的
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysFile.Url">
            <summary>
            外链地址-OSS上传后生成外链地址方便前端预览
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysJobCluster">
            <summary>
            系统作业集群表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobCluster.ClusterId">
            <summary>
            作业集群Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobCluster.Description">
            <summary>
            描述信息
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobCluster.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobCluster.UpdatedTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysJobDetail">
            <summary>
            系统作业信息表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobDetail.JobId">
            <summary>
            作业Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobDetail.GroupName">
            <summary>
            组名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobDetail.JobType">
            <summary>
            作业类型FullName
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobDetail.AssemblyName">
            <summary>
            程序集Name
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobDetail.Description">
            <summary>
            描述信息
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobDetail.Concurrent">
            <summary>
            是否并行执行
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobDetail.IncludeAnnotations">
            <summary>
            是否扫描特性触发器
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobDetail.Properties">
            <summary>
            额外数据
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobDetail.UpdatedTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobDetail.CreateType">
            <summary>
            作业创建类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobDetail.ScriptCode">
            <summary>
            脚本代码
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysJobTrigger">
            <summary>
            系统作业触发器表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.TriggerId">
            <summary>
            触发器Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.JobId">
            <summary>
            作业Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.TriggerType">
            <summary>
            触发器类型FullName
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.AssemblyName">
            <summary>
            程序集Name
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.Args">
            <summary>
            参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.Description">
            <summary>
            描述信息
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.StartTime">
            <summary>
            起始时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.LastRunTime">
            <summary>
            最近运行时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.NextRunTime">
            <summary>
            下一次运行时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.NumberOfRuns">
            <summary>
            触发次数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.MaxNumberOfRuns">
            <summary>
            最大触发次数（0:不限制，n:N次）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.NumberOfErrors">
            <summary>
            出错次数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.MaxNumberOfErrors">
            <summary>
            最大出错次数（0:不限制，n:N次）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.NumRetries">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.RetryTimeout">
            <summary>
            重试间隔时间（ms）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.StartNow">
            <summary>
            是否立即启动
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.RunOnStart">
            <summary>
            是否启动时执行一次
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.ResetOnlyOnce">
            <summary>
            是否在启动时重置最大触发次数等于一次的作业
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysJobTrigger.UpdatedTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysLogAudit">
            <summary>
            系统审计日志表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogAudit.TableName">
            <summary>
            表名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogAudit.ColumnName">
            <summary>
            列名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogAudit.NewValue">
            <summary>
            新值
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogAudit.OldValue">
            <summary>
            旧值
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogAudit.Operate">
            <summary>
            操作方式（新增、更新、删除）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogAudit.AuditTime">
            <summary>
            审计时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogAudit.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogAudit.RealName">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysLogDiff">
            <summary>
            系统差异日志表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogDiff.BeforeData">
            <summary>
            操作前记录
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogDiff.AfterData">
            <summary>
            操作后记录
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogDiff.Sql">
            <summary>
            Sql
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogDiff.Parameters">
            <summary>
            参数  手动传入的参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogDiff.BusinessData">
            <summary>
            业务对象
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogDiff.DiffType">
            <summary>
            差异操作
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogDiff.Elapsed">
            <summary>
            耗时
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysLogEx">
            <summary>
            系统异常日志表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogEx.HttpMethod">
            <summary>
            请求方式
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogEx.RequestUrl">
            <summary>
            请求地址
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogEx.RequestParam">
            <summary>
            请求参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogEx.ReturnResult">
            <summary>
            返回结果
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogEx.EventId">
            <summary>
            事件Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogEx.ThreadId">
            <summary>
            线程Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogEx.TraceId">
            <summary>
            请求跟踪Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogEx.Exception">
            <summary>
            异常信息
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogEx.Message">
            <summary>
            日志消息Json
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysLogOp">
            <summary>
            系统操作日志表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogOp.HttpMethod">
            <summary>
            请求方式
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogOp.RequestUrl">
            <summary>
            请求地址
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogOp.RequestParam">
            <summary>
            请求参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogOp.ReturnResult">
            <summary>
            返回结果
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogOp.EventId">
            <summary>
            事件Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogOp.ThreadId">
            <summary>
            线程Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogOp.TraceId">
            <summary>
            请求跟踪Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogOp.Exception">
            <summary>
            异常信息
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogOp.Message">
            <summary>
            日志消息Json
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysLogVis">
            <summary>
            系统访问日志表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.ControllerName">
            <summary>
            模块名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.ActionName">
             <summary>
             方法名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.DisplayTitle">
             <summary>
             显示名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.Status">
            <summary>
            执行状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.RemoteIp">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.Location">
            <summary>
            登录地点
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.Longitude">
            <summary>
            经度
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.Latitude">
            <summary>
            维度
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.Browser">
            <summary>
            浏览器
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.Os">
            <summary>
            操作系统
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.Elapsed">
            <summary>
            操作用时
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.LogDateTime">
            <summary>
            日志时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.LogLevel">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysLogVis.RealName">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysMenu">
            <summary>
            系统菜单表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.Pid">
            <summary>
            父Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.Type">
            <summary>
            菜单类型（1目录 2菜单 3按钮）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.Name">
            <summary>
            路由名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.Path">
            <summary>
            路由地址
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.Component">
            <summary>
            组件路径
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.Redirect">
            <summary>
            重定向
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.Permission">
            <summary>
            权限标识
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.Title">
            <summary>
            菜单名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.Icon">
            <summary>
            图标
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.IsIframe">
            <summary>
            是否内嵌
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.OutLink">
            <summary>
            外链链接
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.IsHide">
            <summary>
            是否隐藏
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.IsKeepAlive">
            <summary>
            是否缓存
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.IsAffix">
            <summary>
            是否固定
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.OrderNo">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysMenu.Children">
            <summary>
            菜单子项
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysNotice">
            <summary>
            系统通知公告表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNotice.Title">
            <summary>
            标题
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNotice.Content">
            <summary>
            内容
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNotice.Type">
            <summary>
            类型（1通知 2公告）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNotice.PublicUserId">
            <summary>
            发布人Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNotice.PublicUserName">
            <summary>
            发布人姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNotice.PublicOrgId">
            <summary>
            发布机构Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNotice.PublicOrgName">
            <summary>
            发布机构名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNotice.PublicTime">
            <summary>
            发布时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNotice.CancelTime">
            <summary>
            撤回时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNotice.Status">
            <summary>
            状态（0草稿 1发布 2撤回 3删除）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNotice.MessageLevel">
            <summary>
            消息级别（1系统消息 2公司消息）
            </summary>
        </member>
        <member name="T:Admin.NET.Core.NoticeMessageLevelEnum">
            <summary>
            消息级别枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.NoticeMessageLevelEnum.SYSTEM">
            <summary>
            系统消息
            </summary>
        </member>
        <member name="F:Admin.NET.Core.NoticeMessageLevelEnum.COMPANY">
            <summary>
            公司消息
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysNoticeUser">
            <summary>
            系统通知公告用户表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNoticeUser.NoticeId">
            <summary>
            通知公告Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNoticeUser.SysNotice">
            <summary>
            通知公告
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNoticeUser.UserId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNoticeUser.ReadTime">
            <summary>
            阅读时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysNoticeUser.ReadStatus">
            <summary>
            状态（0未读 1已读）
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysOnlineUser">
            <summary>
            系统在线用户表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOnlineUser.ConnectionId">
            <summary>
            连接Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOnlineUser.UserId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOnlineUser.UserName">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOnlineUser.RealName">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOnlineUser.Time">
            <summary>
            连接时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOnlineUser.Ip">
            <summary>
            连接IP
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOnlineUser.Browser">
            <summary>
            浏览器
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOnlineUser.Os">
            <summary>
            操作系统
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysOrg">
            <summary>
            系统机构表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.Pid">
            <summary>
            父Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.PosId">
            <summary>
            职位ID
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.OrgType">
            <summary>
            机构类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.OrderNo">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.Contacts">
            <summary>
            联系人
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.Phone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.BankName">
            <summary>
            开户行
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.BankCode">
            <summary>
            银行账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.TaxId">
            <summary>
            税务登记号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.Address">
            <summary>
            地址
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.CompanyLegalRepresentative">
            <summary>
            公司法人
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.CompanyNature">
            <summary>
            公司性质
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.TaxType">
            <summary>
            税务类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.Invoicer">
            <summary>
            开票员
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.Children">
            <summary>
            机构子项
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.Prov">
            <summary>
            税务区域
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysOrg.SysRoleId">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysPlugin">
            <summary>
            系统动态插件表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPlugin.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPlugin.CsharpCode">
            <summary>
            C#代码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPlugin.AssemblyName">
            <summary>
            程序集名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPlugin.OrderNo">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPlugin.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPlugin.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysPos">
            <summary>
            系统职位表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPos.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPos.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPos.OrderNo">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPos.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPos.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPos.SysOrgNameId">
            <summary>
            部门ID
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPos.SysRoleId">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysPrint">
            <summary>
            系统打印模板表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPrint.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPrint.PrintTemType">
            <summary>
            模板类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPrint.Template">
            <summary>
            打印模板
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPrint.OrderNo">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPrint.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPrint.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysPrint.ShowName">
            <summary>
            模板显示名称
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysRegion">
            <summary>
            系统行政地区表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRegion.Pid">
            <summary>
            父Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRegion.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRegion.ShortName">
            <summary>
            简称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRegion.MergerName">
            <summary>
            组合名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRegion.Code">
            <summary>
            行政代码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRegion.ZipCode">
            <summary>
            邮政编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRegion.CityCode">
            <summary>
            区号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRegion.Level">
            <summary>
            层级
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRegion.PinYin">
            <summary>
            拼音
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRegion.Lng">
            <summary>
            经度
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRegion.Lat">
            <summary>
            维度
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRegion.OrderNo">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRegion.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRegion.Children">
            <summary>
            机构子项
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysRole">
            <summary>
            系统角色表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRole.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRole.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRole.OrderNo">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRole.DataScope">
            <summary>
            数据范围（1全部数据 2本部门及以下数据 3本部门数据 4仅本人数据 5自定义数据）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRole.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRole.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysRoleMenu">
            <summary>
            系统角色菜单表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRoleMenu.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRoleMenu.MenuId">
            <summary>
            菜单Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRoleMenu.SysMenu">
            <summary>
            菜单
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysRoleOrg">
            <summary>
            系统角色机构表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRoleOrg.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRoleOrg.OrgId">
            <summary>
            机构Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysRoleOrg.SysOrg">
            <summary>
            机构
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysTenant">
            <summary>
            系统租户表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.UserId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.OrgId">
            <summary>
            机构Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.Host">
            <summary>
            主机
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.TenantType">
            <summary>
            租户类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.DbType">
            <summary>
            数据库类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.Connection">
            <summary>
            数据库连接
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.ConfigId">
            <summary>
            数据库标识
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.OrderNo">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.SaleUserId">
            <summary>
            销售人ID
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.Duration">
            <summary>
            时长
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.EndTime">
            <summary>
            到期时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.NewFlag">
            <summary>
            标识 0-新客 1-续费
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.PublicKey">
            <summary>
            公钥-开票平台
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.PrivateKey">
            <summary>
            私钥-开票平台
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysTenant.AppType">
            <summary>
            租户来源
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysUser">
            <summary>
            系统用户表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.RealName">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.NickName">
            <summary>
            昵称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Sex">
            <summary>
            性别-男_1、女_2
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Age">
            <summary>
            年龄
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Birthday">
            <summary>
            出生日期
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Nation">
            <summary>
            民族
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Phone">
            <summary>
            手机号码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.CardType">
            <summary>
            证件类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.IdCardNum">
            <summary>
            身份证号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Address">
            <summary>
            地址
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.CultureLevel">
            <summary>
            文化程度
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.PoliticalOutlook">
            <summary>
            政治面貌
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.College">
            <summary>
            毕业院校
            </summary>COLLEGE
        </member>
        <member name="P:Admin.NET.Core.SysUser.OfficePhone">
            <summary>
            办公电话
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.EmergencyContact">
            <summary>
            紧急联系人
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.EmergencyPhone">
            <summary>
            紧急联系人电话
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.EmergencyAddress">
            <summary>
            紧急联系人地址
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Introduction">
            <summary>
            个人简介
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.OrderNo">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.AccountType">
            <summary>
            账号类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.OrgId">
            <summary>
            机构Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.SysOrg">
            <summary>
            机构
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.PosId">
            <summary>
            职位Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.SysPos">
            <summary>
            职位
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.JobNum">
            <summary>
            工号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.PosLevel">
            <summary>
            职级
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.PosTitle">
            <summary>
            职称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Expertise">
            <summary>
            擅长领域
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.OfficeZone">
            <summary>
            办公区域
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Office">
            <summary>
            办公室
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.JoinDate">
            <summary>
            入职日期
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.LastLoginIp">
            <summary>
            最新登录Ip
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.LastLoginAddress">
            <summary>
            最新登录地点
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.LastLoginTime">
            <summary>
            最新登录时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.LastLoginDevice">
            <summary>
            最新登录设备
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.Signature">
            <summary>
            电子签名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.EntryTime">
            <summary>
            入职时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.ContractTime">
            <summary>
            合同时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.ContractStatus">
            <summary>
            合同状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.DepartTime">
            <summary>
            离职时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.DPUserName">
            <summary>
            电票平台账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.DPPassword">
            <summary>
            电票平台账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.ReportToId">
            <summary>
            直属上级ID
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.ReportTo">
            <summary>
            直属上级
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.ReportToName">
            <summary>
            直属上级名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.zyczydm">
            <summary>
            操作员代码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.zyid">
            <summary>
            id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUser.nsrsbh">
            <summary>
            纳税人识别号
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysUserExtOrg">
            <summary>
            系统用户扩展机构表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserExtOrg.UserId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserExtOrg.OrgId">
            <summary>
            机构Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserExtOrg.SysOrg">
            <summary>
            机构
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserExtOrg.PosId">
            <summary>
            职位Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserExtOrg.SysPos">
            <summary>
            职位
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserExtOrg.JobNum">
            <summary>
            工号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserExtOrg.PosLevel">
            <summary>
            职级
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserExtOrg.JoinDate">
            <summary>
            入职日期
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysUserRole">
            <summary>
            系统用户角色表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserRole.UserId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserRole.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserRole.SysRole">
            <summary>
            角色
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysWechatPay">
            <summary>
            系统微信支付表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.MerchantId">
            <summary>
            微信商户号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.AppId">
            <summary>
            服务商AppId
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.OutTradeNumber">
            <summary>
            商户订单号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.TransactionId">
            <summary>
            支付订单号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.TradeType">
            <summary>
            交易类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.TradeState">
            <summary>
            交易状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.TradeStateDescription">
            <summary>
            交易状态描述
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.BankType">
            <summary>
            付款银行类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.Total">
            <summary>
            订单总金额
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.PayerTotal">
            <summary>
            用户支付金额
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.SuccessTime">
            <summary>
            支付完成时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.ExpireTime">
            <summary>
            交易结束时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.Description">
            <summary>
            商品描述
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.Scene">
            <summary>
            场景信息
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.Attachment">
            <summary>
            附加数据
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.GoodsTag">
            <summary>
            优惠标记
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.Settlement">
            <summary>
            结算信息
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.NotifyUrl">
            <summary>
            回调通知地址
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.OpenId">
            <summary>
            微信OpenId标识
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.SysWechatUser">
            <summary>
            关联微信用户
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.SubMerchantId">
            <summary>
            子商户号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.SubAppId">
            <summary>
            子商户AppId
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatPay.SubOpenId">
            <summary>
            子商户唯一标识
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysWechatUser">
            <summary>
            系统微信用户表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.UserId">
            <summary>
            系统用户Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.SysUser">
            <summary>
            系统用户
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.PlatformType">
            <summary>
            平台类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.OpenId">
            <summary>
            OpenId
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.SessionKey">
            <summary>
            会话密钥
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.UnionId">
            <summary>
            UnionId
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.NickName">
            <summary>
            昵称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.Mobile">
            <summary>
            手机号码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.Sex">
            <summary>
            性别
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.Language">
            <summary>
            语言
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.City">
            <summary>
            城市
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.Province">
            <summary>
            省
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.Country">
            <summary>
            国家
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.AccessToken">
            <summary>
            AccessToken
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.RefreshToken">
            <summary>
            RefreshToken
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.ExpiresIn">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysWechatUser.Scope">
            <summary>
            用户授权的作用域，使用逗号分隔
            </summary>
        </member>
        <member name="T:Admin.NET.Core.AccountTypeEnum">
            <summary>
            账号类型枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.AccountTypeEnum.None">
            <summary>
            其他
            </summary>
        </member>
        <member name="F:Admin.NET.Core.AccountTypeEnum.User">
            <summary>
            普通账号
            </summary>
        </member>
        <member name="F:Admin.NET.Core.AccountTypeEnum.Admin">
            <summary>
            系统管理员
            </summary>
        </member>
        <member name="F:Admin.NET.Core.AccountTypeEnum.SuperAdmin">
            <summary>
            超级管理员
            </summary>
        </member>
        <member name="T:Admin.NET.Core.CacheTypeEnum">
            <summary>
            缓存类型枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CacheTypeEnum.Memory">
            <summary>
            内存缓存
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CacheTypeEnum.Redis">
            <summary>
            Redis缓存
            </summary>
        </member>
        <member name="T:Admin.NET.Core.CardTypeEnum">
            <summary>
            证件类型枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CardTypeEnum.IdCard">
            <summary>
            身份证
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CardTypeEnum.PassportCard">
            <summary>
            护照
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CardTypeEnum.BirthCard">
            <summary>
            出生证
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CardTypeEnum.GatCard">
            <summary>
            港澳台通行证
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CardTypeEnum.ForeignCard">
            <summary>
            外国人居留证
            </summary>
        </member>
        <member name="T:Admin.NET.Core.CryptogramEnum">
            <summary>
            密码加密枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CryptogramEnum.MD5">
            <summary>
            MD5
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CryptogramEnum.SM2">
            <summary>
            SM2（国密）
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CryptogramEnum.SM4">
            <summary>
            SM4（国密）
            </summary>
        </member>
        <member name="T:Admin.NET.Core.CultureLevelEnum">
            <summary>
            文化程度枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CultureLevelEnum.Level1">
            <summary>
            小学
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CultureLevelEnum.Level2">
            <summary>
            初中
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CultureLevelEnum.Level3">
            <summary>
            高中
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CultureLevelEnum.Level4">
            <summary>
            中专
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CultureLevelEnum.Level5">
            <summary>
            大专
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CultureLevelEnum.Level6">
            <summary>
            本科
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CultureLevelEnum.Level7">
            <summary>
            硕士研究生
            </summary>
        </member>
        <member name="F:Admin.NET.Core.CultureLevelEnum.Level8">
            <summary>
            博士研究生
            </summary>
        </member>
        <member name="T:Admin.NET.Core.DataOpTypeEnum">
            <summary>
            数据操作类型枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataOpTypeEnum.Other">
            <summary>
            其它
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataOpTypeEnum.Add">
            <summary>
            增加
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataOpTypeEnum.Delete">
            <summary>
            删除
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataOpTypeEnum.Edit">
            <summary>
            编辑
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataOpTypeEnum.Update">
            <summary>
            更新
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataOpTypeEnum.Query">
            <summary>
            查询
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataOpTypeEnum.Detail">
            <summary>
            详情
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataOpTypeEnum.Tree">
            <summary>
            树
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataOpTypeEnum.Import">
            <summary>
            导入
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataOpTypeEnum.Export">
            <summary>
            导出
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataOpTypeEnum.Grant">
            <summary>
            授权
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataOpTypeEnum.Force">
            <summary>
            强退
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataOpTypeEnum.Clean">
            <summary>
            清空
            </summary>
        </member>
        <member name="T:Admin.NET.Core.DataScopeEnum">
            <summary>
            角色数据范围枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataScopeEnum.All">
            <summary>
            全部数据
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataScopeEnum.DeptChild">
            <summary>
            本部门及以下数据
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataScopeEnum.Dept">
            <summary>
            本部门数据
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataScopeEnum.Self">
            <summary>
            仅本人数据
            </summary>
        </member>
        <member name="F:Admin.NET.Core.DataScopeEnum.Define">
            <summary>
            自定义数据
            </summary>
        </member>
        <member name="T:Admin.NET.Core.ErrorCodeEnum">
            <summary>
            系统错误码
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D0008">
            <summary>
            验证码错误
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D0009">
            <summary>
            账号不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1000">
            <summary>
            密码不正确
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1001">
            <summary>
            非法操作！禁止删除自己
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1002">
            <summary>
            记录不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1003">
            <summary>
            账号已存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1004">
            <summary>
            旧密码不匹配
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1005">
            <summary>
            测试数据禁止更改admin密码
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1006">
            <summary>
            数据已存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1007">
            <summary>
            数据不存在或含有关联引用，禁止删除
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1008">
            <summary>
            禁止为管理员分配角色
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1009">
            <summary>
            重复数据或记录含有不存在数据
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1010">
            <summary>
            禁止为超级管理员角色分配权限
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1011">
            <summary>
            非法操作，未登录
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1012">
            <summary>
            Id不能为空
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1013">
            <summary>
            所属机构不在自己的数据范围内
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1014">
            <summary>
            禁止删除超级管理员
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1015">
            <summary>
            禁止修改超级管理员状态
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1016">
            <summary>
            没有权限
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1017">
            <summary>
            账号已冻结
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1018">
            <summary>
            禁止删除管理员
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1019">
            <summary>
            禁止删除系统管理员角色
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1020">
            <summary>
            禁止修改系统管理员角色
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1021">
            <summary>
            禁止为系统管理员角色分配权限
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1022">
            <summary>
            禁止为超级管理员分配角色
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1023">
            <summary>
            禁止删除默认租户
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1024">
            <summary>
            已将其他地方登录账号下线
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D2000">
            <summary>
            父机构不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D2001">
            <summary>
            当前机构Id不能与父机构Id相同
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D2002">
            <summary>
            已有相同组织机构,编码或名称相同
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D2003">
            <summary>
            没有权限操作机构
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D2004">
            <summary>
            该机构下有用户禁止删除
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D2005">
            <summary>
            附属机构下有用户禁止删除
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D2006">
            <summary>
            只能增加下级机构
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D2007">
            <summary>
            下级机构下有用户禁止删除
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D2008">
            <summary>
            租户默认机构禁止删除
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D3000">
            <summary>
            字典类型不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D3001">
            <summary>
            字典类型已存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D3002">
            <summary>
            字典类型下面有字典值禁止删除
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D3003">
            <summary>
            字典值已存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D3004">
            <summary>
            字典值不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D3005">
            <summary>
            字典状态错误
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D4000">
            <summary>
            菜单已存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D4001">
            <summary>
            路由地址为空
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D4002">
            <summary>
            打开方式为空
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D4003">
            <summary>
            权限标识格式为空
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D4004">
            <summary>
            权限标识格式错误
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D4005">
            <summary>
            权限不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D4006">
            <summary>
            父级菜单不能为当前节点，请重新选择父级菜单
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D4007">
            <summary>
            不能移动根节点
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D5000">
            <summary>
            已存在同名或同编码应用
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D5001">
            <summary>
            默认激活系统只能有一个
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D5002">
            <summary>
            该应用下有菜单禁止删除
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D5003">
            <summary>
            已存在同名或同编码应用
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D6000">
            <summary>
            已存在同名或同编码职位
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D6001">
            <summary>
            该职位下有用户禁止删除
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D7000">
            <summary>
            通知公告状态错误
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D7001">
            <summary>
            通知公告删除失败
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D7002">
            <summary>
            通知公告编辑失败
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D7003">
            <summary>
            通知公告操作失败，非发布者不能进行操作
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D8000">
            <summary>
            文件不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D8001">
            <summary>
            不允许的文件类型
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D8002">
            <summary>
            文件超过允许大小
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D8003">
            <summary>
            文件后缀错误
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D9000">
            <summary>
            已存在同名或同编码参数配置
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D9001">
            <summary>
            禁止删除系统参数
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1100">
            <summary>
            已存在同名任务调度
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1101">
            <summary>
            任务调度不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1200">
            <summary>
            演示环境禁止修改数据
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1300">
            <summary>
            已存在同名的租户
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1301">
            <summary>
            已存在同名的租户管理员
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1400">
            <summary>
            该表代码模板已经生成过
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1501">
            <summary>
            该类型不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1502">
            <summary>
            该字段不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1503">
            <summary>
            该类型不是枚举类型
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1504">
            <summary>
            该实体不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1505">
            <summary>
            父菜单不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1600">
            <summary>
            父资源不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1601">
            <summary>
            当前资源Id不能与父资源Id相同
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1602">
            <summary>
            已有相同编码或名称
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1701">
            <summary>
            脚本代码不能为空
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1702">
            <summary>
            脚本代码中的作业类，需要定义 [JobDetail] 特性
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1703">
            <summary>
            作业编号需要与脚本代码中的作业类 [JobDetail('jobId')] 一致
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1704">
            <summary>
            禁止修改作业编号
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1800">
            <summary>
            已存在同名打印模板
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1801">
            <summary>
            默认打印模板不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.D1900">
            <summary>
            已存在同名功能或同名程序及插件
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.xg1000">
            <summary>
            已存在同名或同编码项目
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.xg1001">
            <summary>
            已存在相同证件号码人员
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.xg1002">
            <summary>
            检测数据不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.db1000">
            <summary>
            请添加数据列
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.db1001">
            <summary>
            数据表不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.db1002">
            <summary>
            数据表不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.R2000">
            <summary>
            父节点不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.R2001">
            <summary>
            当前节点Id不能与父节点Id相同
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.R2002">
            <summary>
            已有相同编码或名称
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.Z1001">
            <summary>
            默认租户状态禁止修改
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.Z1002">
            <summary>
            禁止创建此类型的数据库
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.Z1003">
            <summary>
            租户已禁用
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.GY1001">
            <summary>
            公用序号不存在
            </summary>
        </member>
        <member name="F:Admin.NET.Core.ErrorCodeEnum.Z1004">
            <summary>
            租户已禁用
            </summary>
        </member>
        <member name="T:Admin.NET.Core.GenderEnum">
            <summary>
            性别枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.GenderEnum.Male">
            <summary>
            男
            </summary>
        </member>
        <member name="F:Admin.NET.Core.GenderEnum.Female">
            <summary>
            女
            </summary>
        </member>
        <member name="T:Admin.NET.Core.JobCreateTypeEnum">
            <summary>
            作业创建类型枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.JobCreateTypeEnum.BuiltIn">
            <summary>
            内置
            </summary>
        </member>
        <member name="F:Admin.NET.Core.JobCreateTypeEnum.Script">
            <summary>
            脚本
            </summary>
        </member>
        <member name="F:Admin.NET.Core.JobCreateTypeEnum.Http">
            <summary>
            HTTP请求
            </summary>
        </member>
        <member name="T:Admin.NET.Core.JobStatusEnum">
            <summary>
            岗位状态枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.JobStatusEnum.On">
            <summary>
            在职
            </summary>
        </member>
        <member name="F:Admin.NET.Core.JobStatusEnum.Off">
            <summary>
            离职
            </summary>
        </member>
        <member name="F:Admin.NET.Core.JobStatusEnum.Leave">
            <summary>
            请假
            </summary>
        </member>
        <member name="F:Admin.NET.Core.JobStatusEnum.Other">
            <summary>
            其他
            </summary>
        </member>
        <member name="T:Admin.NET.Core.LoginModeEnum">
            <summary>
            登录模式枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.LoginModeEnum.PC">
            <summary>
            PC模式
            </summary>
        </member>
        <member name="F:Admin.NET.Core.LoginModeEnum.APP">
            <summary>
            APP
            </summary>
        </member>
        <member name="T:Admin.NET.Core.LoginTypeEnum">
            <summary>
            登录类型枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.LoginTypeEnum.Login">
            <summary>
            PC登录
            </summary>
        </member>
        <member name="F:Admin.NET.Core.LoginTypeEnum.Logout">
            <summary>
            PC退出
            </summary>
        </member>
        <member name="F:Admin.NET.Core.LoginTypeEnum.Register">
            <summary>
            PC注册
            </summary>
        </member>
        <member name="T:Admin.NET.Core.MenuTypeEnum">
            <summary>
            系统菜单类型枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.MenuTypeEnum.Dir">
            <summary>
            目录
            </summary>
        </member>
        <member name="F:Admin.NET.Core.MenuTypeEnum.Menu">
            <summary>
            菜单
            </summary>
        </member>
        <member name="F:Admin.NET.Core.MenuTypeEnum.Btn">
            <summary>
            按钮
            </summary>
        </member>
        <member name="T:Admin.NET.Core.MessageTypeEnum">
            <summary>
            消息类型枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.MessageTypeEnum.Info">
            <summary>
            普通信息
            </summary>
        </member>
        <member name="F:Admin.NET.Core.MessageTypeEnum.Success">
            <summary>
            成功提示
            </summary>
        </member>
        <member name="F:Admin.NET.Core.MessageTypeEnum.Warning">
            <summary>
            警告提示
            </summary>
        </member>
        <member name="F:Admin.NET.Core.MessageTypeEnum.Error">
            <summary>
            错误提示
            </summary>
        </member>
        <member name="T:Admin.NET.Core.NoticeStatusEnum">
            <summary>
            通知公告状态枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.NoticeStatusEnum.DRAFT">
            <summary>
            草稿
            </summary>
        </member>
        <member name="F:Admin.NET.Core.NoticeStatusEnum.PUBLIC">
            <summary>
            发布
            </summary>
        </member>
        <member name="F:Admin.NET.Core.NoticeStatusEnum.CANCEL">
            <summary>
            撤回
            </summary>
        </member>
        <member name="F:Admin.NET.Core.NoticeStatusEnum.DELETED">
            <summary>
            删除
            </summary>
        </member>
        <member name="T:Admin.NET.Core.NoticeTypeEnum">
            <summary>
            通知公告状类型枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.NoticeTypeEnum.NOTICE">
            <summary>
            通知
            </summary>
        </member>
        <member name="F:Admin.NET.Core.NoticeTypeEnum.ANNOUNCEMENT">
            <summary>
            公告
            </summary>
        </member>
        <member name="T:Admin.NET.Core.NoticeUserStatusEnum">
            <summary>
            通知公告用户状态枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.NoticeUserStatusEnum.UNREAD">
            <summary>
            未读
            </summary>
        </member>
        <member name="F:Admin.NET.Core.NoticeUserStatusEnum.READ">
            <summary>
            已读
            </summary>
        </member>
        <member name="T:Admin.NET.Core.PlatformTypeEnum">
            <summary>
            平台类型枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.PlatformTypeEnum.微信公众号">
            <summary>
            微信公众号
            </summary>
        </member>
        <member name="F:Admin.NET.Core.PlatformTypeEnum.微信小程序">
            <summary>
            微信小程序
            </summary>
        </member>
        <member name="F:Admin.NET.Core.PlatformTypeEnum.支付宝小程序">
            <summary>
            支付宝小程序
            </summary>
        </member>
        <member name="F:Admin.NET.Core.PlatformTypeEnum.微信APP快捷登陆">
            <summary>
            微信APP快捷登陆
            </summary>
        </member>
        <member name="F:Admin.NET.Core.PlatformTypeEnum.QQ在APP中快捷登陆">
            <summary>
            QQ在APP中快捷登陆
            </summary>
        </member>
        <member name="F:Admin.NET.Core.PlatformTypeEnum.头条系小程序">
            <summary>
            头条系小程序
            </summary>
        </member>
        <member name="T:Admin.NET.Core.RequestTypeEnum">
            <summary>
            HTTP请求类型
            </summary>
        </member>
        <member name="F:Admin.NET.Core.RequestTypeEnum.Run">
            <summary>
            执行内部方法
            </summary>
        </member>
        <member name="F:Admin.NET.Core.RequestTypeEnum.Get">
            <summary>
            GET
            </summary>
        </member>
        <member name="F:Admin.NET.Core.RequestTypeEnum.Post">
            <summary>
            POST
            </summary>
        </member>
        <member name="F:Admin.NET.Core.RequestTypeEnum.Put">
            <summary>
            PUT
            </summary>
        </member>
        <member name="F:Admin.NET.Core.RequestTypeEnum.Delete">
            <summary>
            DELETE
            </summary>
        </member>
        <member name="T:Admin.NET.Core.StatusEnum">
            <summary>
            通用状态枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.StatusEnum.Enable">
            <summary>
            启用
            </summary>
        </member>
        <member name="F:Admin.NET.Core.StatusEnum.Disable">
            <summary>
            停用
            </summary>
        </member>
        <member name="T:Admin.NET.Core.TenantAppTypeEnum">
            <summary>
            租户来源枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.TenantAppTypeEnum.HuanJu">
            <summary>
            自建
            </summary>
        </member>
        <member name="F:Admin.NET.Core.TenantAppTypeEnum.ZhiYun">
            <summary>
            智云
            </summary>
        </member>
        <member name="T:Admin.NET.Core.TenantTypeEnum">
            <summary>
            租户类型枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.TenantTypeEnum.Id">
            <summary>
            Id隔离
            </summary>
        </member>
        <member name="F:Admin.NET.Core.TenantTypeEnum.Db">
            <summary>
            库隔离
            </summary>
        </member>
        <member name="T:Admin.NET.Core.WechatReturnCodeEnum">
            <summary>
            微信开发返回码
            </summary>
        </member>
        <member name="F:Admin.NET.Core.WechatReturnCodeEnum.不合法的凭证类型">
            <summary>
            <para>公众号：不合法的凭证类型</para>
            <para>小程序：暂无生成权限</para>
            </summary>
        </member>
        <member name="F:Admin.NET.Core.WechatReturnCodeEnum.不合法的APPID">
            <summary>
            <para>微信：不合法的APPID</para>
            <para>小程序：生成权限被封禁</para>
            </summary>
        </member>
        <member name="F:Admin.NET.Core.WechatReturnCodeEnum.输入参数有误">
            <summary>
            <para>公众号：输入参数有误</para>
            <para>小程序：参数expire_time填写错误</para>
            </summary>
        </member>
        <member name="F:Admin.NET.Core.WechatReturnCodeEnum.用户拒绝接受消息">
            <summary>[小程序订阅消息]用户拒绝接受消息，如果用户之前曾经订阅过，则表示用户取消了订阅关系</summary>
        </member>
        <member name="F:Admin.NET.Core.WechatReturnCodeEnum.模板参数不准确">
            <summary>[小程序订阅消息]模板参数不准确，可能为空或者不满足规则，errmsg会提示具体是哪个字段出错</summary>
        </member>
        <member name="F:Admin.NET.Core.WechatReturnCodeEnum.客服帐号名长度超过限制">
            <summary>
            客服帐号名长度超过限制(仅允许10个英文字符，不包括@及@后的公众号的微信号)(invalid kf_acount length)
            </summary>
        </member>
        <member name="F:Admin.NET.Core.WechatReturnCodeEnum.客服帐号名包含非法字符">
            <summary>
            客服帐号名包含非法字符(仅允许英文+数字)(illegal character in kf_account)
            </summary>
        </member>
        <member name="F:Admin.NET.Core.WechatReturnCodeEnum.客服帐号个数超过限制">
            <summary>客服帐号个数超过限制(10个客服账号)(kf_account count exceeded)</summary>
        </member>
        <member name="F:Admin.NET.Core.WechatReturnCodeEnum.签名错误">
            <summary>
            小程序为“签名错误”。对应公众号： 87009, “errmsg” : “reply is not exists” //该回复不存在
            </summary>
        </member>
        <member name="T:Admin.NET.Core.YesNoEnum">
            <summary>
            是否枚举
            </summary>
        </member>
        <member name="F:Admin.NET.Core.YesNoEnum.Y">
            <summary>
            是
            </summary>
        </member>
        <member name="F:Admin.NET.Core.YesNoEnum.N">
            <summary>
            否
            </summary>
        </member>
        <member name="T:Admin.NET.Core.AppEventSubscriber">
            <summary>
            事件订阅
            </summary>
        </member>
        <member name="T:Admin.NET.Core.RedisEventSourceStorer">
            <summary>
            Redis自定义事件源存储器
            </summary>
        </member>
        <member name="M:Admin.NET.Core.RedisEventSourceStorer.WriteAsync(Furion.EventBus.IEventSource,System.Threading.CancellationToken)">
            <summary>
            往 Redis 中写入一条
            </summary>
            <param name="eventSource"></param>
            <param name="cancellationToken"></param>
        </member>
        <member name="M:Admin.NET.Core.RedisEventSourceStorer.ReadAsync(System.Threading.CancellationToken)">
            <summary>
            从 Redis 中读取一条
            </summary>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.RetryEventHandlerExecutor">
            <summary>
            事件执行器-超时控制、失败重试熔断等等
            </summary>
        </member>
        <member name="T:Admin.NET.Core.EnumExtension">
            <summary>
            枚举拓展
            </summary>
        </member>
        <member name="M:Admin.NET.Core.EnumExtension.GetEnumDictionary(System.Type)">
            <summary>
            获取枚举对象Key与名称的字典（缓存）
            </summary>
            <param name="enumType"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.EnumExtension.GetEnumDictionaryItems(System.Type)">
            <summary>
            获取枚举对象Key与名称的字典
            </summary>
            <param name="enumType"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.EnumExtension.GetEnumDescDictionary(System.Type)">
            <summary>
            获取枚举类型key与描述的字典（缓存）
            </summary>
            <param name="enumType"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Admin.NET.Core.EnumExtension.GetEnumDescDictionaryItems(System.Type)">
            <summary>
            获取枚举类型key与描述的字典（没有描述则获取name）
            </summary>
            <param name="enumType"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Admin.NET.Core.EnumExtension.TryToGetEnumType(System.Reflection.Assembly,System.String)">
            <summary>
            从程序集中查找指定枚举类型
            </summary>
            <param name="assembly"></param>
            <param name="typeName"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.EnumExtension.LoadEnumTypeDict(System.Reflection.Assembly)">
            <summary>
            从程序集中加载所有枚举类型
            </summary>
            <param name="assembly"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.EnumExtension.GetDescription(System.Enum)">
            <summary>
            获取枚举的Description
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.EnumExtension.GetDescription(System.Object)">
            <summary>
            获取枚举的Description
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.EnumExtension.EnumToList(System.Type)">
            <summary>
            将枚举转成枚举信息集合
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.EnumExtension.EnumToList``1(System.Type)">
            <summary>
            枚举ToList
            </summary>
            <typeparam name="T"></typeparam>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.EnumEntity">
            <summary>
            枚举实体
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EnumEntity.Describe">
            <summary>
            枚举的描述
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EnumEntity.Name">
            <summary>
            枚举名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EnumEntity.Value">
            <summary>
            枚举对象的值
            </summary>
        </member>
        <member name="T:Admin.NET.Core.LogoExtension">
            <summary>
            logo显示
            </summary>
        </member>
        <member name="T:Admin.NET.Core.ObjectExtension">
            <summary>
            对象拓展
            </summary>
        </member>
        <member name="M:Admin.NET.Core.ObjectExtension.HasImplementedRawGeneric(System.Type,System.Type)">
            <summary>
            判断类型是否实现某个泛型
            </summary>
            <param name="type">类型</param>
            <param name="generic">泛型类型</param>
            <returns>bool</returns>
        </member>
        <member name="M:Admin.NET.Core.ObjectExtension.ToQueryString(System.Collections.Generic.Dictionary{System.String,System.String},System.Boolean)">
            <summary>
            将字典转化为QueryString格式
            </summary>
            <param name="dict"></param>
            <param name="urlEncode"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ObjectExtension.UrlEncode(System.String)">
            <summary>
            将字符串URL编码
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ObjectExtension.ToJson(System.Object)">
            <summary>
            对象序列化成Json字符串
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ObjectExtension.ToObject``1(System.String)">
            <summary>
            Json字符串反序列化成对象
            </summary>
            <typeparam name="T"></typeparam>
            <param name="json"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ObjectExtension.ParseToLong(System.Object)">
            <summary>
            将object转换为long，若失败则返回0
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ObjectExtension.ParseToLong(System.String,System.Int64)">
            <summary>
            将object转换为long，若失败则返回指定值
            </summary>
            <param name="str"></param>
            <param name="defaultValue"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ObjectExtension.ParseToDouble(System.Object)">
            <summary>
            将object转换为double，若失败则返回0
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ObjectExtension.ParseToDouble(System.Object,System.Double)">
            <summary>
            将object转换为double，若失败则返回指定值
            </summary>
            <param name="str"></param>
            <param name="defaultValue"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ObjectExtension.ParseToDateTime(System.String)">
            <summary>
            将string转换为DateTime，若失败则返回日期最小值
            </summary>
            <param name="str"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ObjectExtension.ParseToDateTime(System.String,System.Nullable{System.DateTime})">
            <summary>
            将string转换为DateTime，若失败则返回默认值
            </summary>
            <param name="str"></param>
            <param name="defaultValue"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ObjectExtension.IsNullOrEmpty(System.Object)">
            <summary>
            判断是否有值
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.FakeDelete``1(SqlSugar.ISugarRepository,``0)">
            <summary>
            实体假删除 _rep.FakeDelete(entity)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="repository"></param>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.FakeDelete``1(SqlSugar.ISqlSugarClient,``0)">
            <summary>
            实体假删除 db.FakeDelete(entity)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="db"></param>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.FakeDeleteAsync``1(SqlSugar.ISugarRepository,``0)">
            <summary>
            实体假删除异步 _rep.FakeDeleteAsync(entity)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="repository"></param>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.FakeDeleteAsync``1(SqlSugar.ISqlSugarClient,``0)">
            <summary>
            实体假删除 db.FakeDelete(entity)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="db"></param>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.OrderBuilder``1(SqlSugar.ISugarQueryable{``0},Admin.NET.Core.BasePageInput,System.String,System.Boolean)">
            <summary>
            排序方式(默认降序)
            </summary>
            <param name="queryable"></param>
            <param name="pageInput"> </param>
            <param name="defualtSortField"> 默认排序字段 </param>
            <param name="descSort"> 是否降序 </param>
            <returns> </returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.UpdateWithDiffLog``1(SqlSugar.ISugarRepository,``0,System.Boolean)">
            <summary>
            更新实体并记录差异日志 _rep.UpdateWithDiffLog(entity)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="repository"></param>
            <param name="entity"></param>
            <param name="ignoreAllNullColumns"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.UpdateWithDiffLog``1(SqlSugar.ISqlSugarClient,``0,System.Boolean)">
            <summary>
            更新实体并记录差异日志 _rep.UpdateWithDiffLog(entity)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="db"></param>
            <param name="entity"></param>
            <param name="ignoreAllNullColumns"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.UpdateWithDiffLogAsync``1(SqlSugar.ISugarRepository,``0,System.Boolean)">
            <summary>
            更新实体并记录差异日志 _rep.UpdateWithDiffLogAsync(entity)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="repository"></param>
            <param name="entity"></param>
            <param name="ignoreAllNullColumns"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.UpdateWithDiffLogAsync``1(SqlSugar.ISqlSugarClient,``0,System.Boolean)">
            <summary>
            更新实体并记录差异日志 _rep.UpdateWithDiffLogAsync(entity)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="db"></param>
            <param name="entity"></param>
            <param name="ignoreAllNullColumns"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.InsertWithDiffLog``1(SqlSugar.ISugarRepository,``0)">
            <summary>
            新增实体并记录差异日志 _rep.InsertWithDiffLog(entity)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="repository"></param>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.InsertWithDiffLog``1(SqlSugar.ISqlSugarClient,``0)">
            <summary>
            新增实体并记录差异日志 _rep.InsertWithDiffLog(entity)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="db"></param>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.InsertWithDiffLogAsync``1(SqlSugar.ISugarRepository,``0)">
            <summary>
            新增实体并记录差异日志 _rep.InsertWithDiffLogAsync(entity)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="repository"></param>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.InsertWithDiffLogAsync``1(SqlSugar.ISqlSugarClient,``0)">
            <summary>
            新增实体并记录差异日志 _rep.InsertWithDiffLog(entity)
            </summary>
            <typeparam name="T"></typeparam>
            <param name="db"></param>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.AS``1(SqlSugar.ISugarQueryable{``0})">
            <summary>
            多库查询
            </summary>
            <param name="queryable"></param>
            <returns> </returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.AS``2(SqlSugar.ISugarQueryable{``0,``1})">
            <summary>
            多库查询
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="T2"></typeparam>
            <param name="queryable"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.AS``1(SqlSugar.IUpdateable{``0})">
            <summary>
            多库更新
            </summary>
            <param name="updateable"></param>
            <returns> </returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.AS``1(SqlSugar.IInsertable{``0})">
            <summary>
            多库新增
            </summary>
            <param name="insertable"></param>
            <returns> </returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.AS``1(SqlSugar.IDeleteable{``0})">
            <summary>
            多库删除
            </summary>
            <param name="deleteable"></param>
            <returns> </returns>
        </member>
        <member name="M:Admin.NET.Core.RepositoryExtension.GetTableInfo``1">
            <summary>
            根据实体类型获取表信息
            </summary>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.IOnlineUserHub.OnlineUserList(Admin.NET.Core.OnlineUserList)">
            <summary>
            在线用户列表
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.IOnlineUserHub.ForceOffline(System.Object)">
            <summary>
            强制下线
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.IOnlineUserHub.PublicNotice(Admin.NET.Core.SysNotice)">
            <summary>
            发布站内消息
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.IOnlineUserHub.ReceiveMessage(System.Object)">
            <summary>
            接收消息
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.OnlineUserHub">
            <summary>
            在线用户集线器
            </summary>
        </member>
        <member name="M:Admin.NET.Core.OnlineUserHub.OnConnectedAsync">
            <summary>
            连接
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.OnlineUserHub.OnDisconnectedAsync(System.Exception)">
            <summary>
            断开
            </summary>
            <param name="exception"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.OnlineUserHub.ForceOffline(Admin.NET.Core.OnlineUserHubInput)">
            <summary>
            强制下线
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.OnlineUserHub.ClientsSendMessage(Admin.NET.Core.MessageInput)">
            <summary>
            前端调用发送方法（发送信息给某个人）
            </summary>
            <param name="message"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.OnlineUserHub.ClientsSendMessagetoAll(Admin.NET.Core.MessageInput)">
            <summary>
            前端调用发送方法（发送信息给所有人）
            </summary>
            <param name="message"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.OnlineUserHub.ClientsSendMessagetoOther(Admin.NET.Core.MessageInput)">
            <summary>
            前端调用发送方法（发送消息给除了发送人的其他人）
            </summary>
            <param name="message"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.OnlineUserHub.ClientsSendMessagetoUsers(Admin.NET.Core.MessageInput)">
            <summary>
            前端调用发送方法（发送消息给某些人）
            </summary>
            <param name="message"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.DynamicJobCompiler">
            <summary>
            动态作业编译
            </summary>
        </member>
        <member name="M:Admin.NET.Core.DynamicJobCompiler.BuildJob(System.String)">
            <summary>
            编译代码并返回其中实现 IJob 的类型
            </summary>
            <param name="script">动态编译的作业代码</param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.LogJob">
            <summary>
            清理日志作业任务
            </summary>
        </member>
        <member name="T:Admin.NET.Core.OnlineUserJob">
            <summary>
            清理在线用户作业任务
            </summary>
        </member>
        <member name="T:Admin.NET.Core.DatabaseLoggingWriter">
            <summary>
            数据库日志写入器
            </summary>
        </member>
        <member name="M:Admin.NET.Core.DatabaseLoggingWriter.GetIpAddress(System.String)">
            <summary>
            解析IP地址
            </summary>
            <param name="ip"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.ElasticSearchLoggingWriter">
            <summary>
            ES日志写入器
            </summary>
        </member>
        <member name="T:Admin.NET.Core.ElasticSearchSetup">
            <summary>
            ES服务注册
            </summary>
        </member>
        <member name="T:Admin.NET.Core.APIJSONOptions">
            <summary>
            APIJSON配置选项
            </summary>
        </member>
        <member name="P:Admin.NET.Core.APIJSONOptions.Roles">
            <summary>
            角色集合
            </summary>
        </member>
        <member name="T:Admin.NET.Core.APIJSON_Role">
            <summary>
            APIJSON角色权限
            </summary>
        </member>
        <member name="P:Admin.NET.Core.APIJSON_Role.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.APIJSON_Role.Select">
            <summary>
            查询
            </summary>
        </member>
        <member name="P:Admin.NET.Core.APIJSON_Role.Insert">
            <summary>
            增加
            </summary>
        </member>
        <member name="P:Admin.NET.Core.APIJSON_Role.Update">
            <summary>
            更新
            </summary>
        </member>
        <member name="P:Admin.NET.Core.APIJSON_Role.Delete">
            <summary>
            删除
            </summary>
        </member>
        <member name="T:Admin.NET.Core.APIJSON_RoleItem">
            <summary>
            APIJSON角色权限内容
            </summary>
        </member>
        <member name="P:Admin.NET.Core.APIJSON_RoleItem.Table">
            <summary>
            表集合
            </summary>
        </member>
        <member name="P:Admin.NET.Core.APIJSON_RoleItem.Column">
            <summary>
            列集合
            </summary>
        </member>
        <member name="P:Admin.NET.Core.APIJSON_RoleItem.Filter">
            <summary>
            过滤器
            </summary>
        </member>
        <member name="T:Admin.NET.Core.CacheOptions">
            <summary>
            缓存配置选项
            </summary>
        </member>
        <member name="P:Admin.NET.Core.CacheOptions.CacheType">
            <summary>
            缓存类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.CacheOptions.RedisConnectionString">
            <summary>
            Redis连接字符串
            </summary>
        </member>
        <member name="T:Admin.NET.Core.CodeGenOptions">
            <summary>
            代码生成配置选项
            </summary>
        </member>
        <member name="P:Admin.NET.Core.CodeGenOptions.EntityAssemblyNames">
            <summary>
            数据库实体程序集名称集合
            </summary>
        </member>
        <member name="P:Admin.NET.Core.CodeGenOptions.BaseEntityNames">
            <summary>
            数据库基础实体名称集合
            </summary>
        </member>
        <member name="P:Admin.NET.Core.CodeGenOptions.EntityBaseColumn">
            <summary>
            基础实体名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.CodeGenOptions.FrontRootPath">
            <summary>
            前端文件根目录
            </summary>
        </member>
        <member name="P:Admin.NET.Core.CodeGenOptions.BackendApplicationNamespace">
            <summary>
            后端生成到的项目
            </summary>
        </member>
        <member name="T:Admin.NET.Core.CryptogramOptions">
            <summary>
            密码配置选项
            </summary>
        </member>
        <member name="P:Admin.NET.Core.CryptogramOptions.CryptoType">
            <summary>
            密码类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.CryptogramOptions.PublicKey">
            <summary>
            公钥
            </summary>
        </member>
        <member name="P:Admin.NET.Core.CryptogramOptions.PrivateKey">
            <summary>
            私钥
            </summary>
        </member>
        <member name="T:Admin.NET.Core.DbConnectionOptions">
            <summary>
            数据库配置选项
            </summary>
        </member>
        <member name="P:Admin.NET.Core.DbConnectionOptions.ConnectionConfigs">
            <summary>
            数据库集合
            </summary>
        </member>
        <member name="P:Admin.NET.Core.DbConnectionConfig.EnableInitDb">
            <summary>
            启用库表初始化
            </summary>
        </member>
        <member name="P:Admin.NET.Core.DbConnectionConfig.EnableInitSeed">
            <summary>
            启用种子初始化
            </summary>
        </member>
        <member name="P:Admin.NET.Core.DbConnectionConfig.EnableDiffLog">
            <summary>
            启用库表差异日志
            </summary>
        </member>
        <member name="P:Admin.NET.Core.DbConnectionConfig.EnableUnderLine">
            <summary>
            启用驼峰转下划线
            </summary>
        </member>
        <member name="T:Admin.NET.Core.EmailOptions">
            <summary>
            邮件配置选项
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EmailOptions.Host">
            <summary>
            主机
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EmailOptions.Port">
            <summary>
            端口
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EmailOptions.DefaultFromEmail">
            <summary>
            默认发件者邮箱
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EmailOptions.DefaultToEmail">
            <summary>
            默认接收人邮箱
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EmailOptions.EnableSsl">
            <summary>
            启用SSL
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EmailOptions.UseDefaultCredentials">
            <summary>
            是否使用默认凭据
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EmailOptions.UserName">
            <summary>
            邮箱账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EmailOptions.Password">
            <summary>
            邮箱密码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EmailOptions.DefaultFromName">
            <summary>
            默认邮件标题
            </summary>
        </member>
        <member name="T:Admin.NET.Core.EnumOptions">
            <summary>
            枚举配置选项
            </summary>
        </member>
        <member name="P:Admin.NET.Core.EnumOptions.EntityAssemblyNames">
            <summary>
            枚举实体程序集名称集合
            </summary>
        </member>
        <member name="T:Admin.NET.Core.OAuthOptions">
            <summary>
            第三方登录授权配置选项
            </summary>
        </member>
        <member name="P:Admin.NET.Core.OAuthOptions.Weixin">
            <summary>
            微信配置
            </summary>
        </member>
        <member name="T:Admin.NET.Core.OSSProviderOptions">
            <summary>
            对象存储配置选项
            </summary>
        </member>
        <member name="P:Admin.NET.Core.OSSProviderOptions.IsEnable">
            <summary>
            是否启用OSS存储
            </summary>
        </member>
        <member name="P:Admin.NET.Core.OSSProviderOptions.Bucket">
            <summary>
            自定义桶名称 不能直接使用Provider来替代桶名称
            例：阿里云 1.只能包括小写字母，数字，短横线（-）2.必须以小写字母或者数字开头 3.长度必须在3-63字节之间
            </summary>
        </member>
        <member name="T:Admin.NET.Core.PayCallBackOptions">
            <summary>
            支付回调配置选项
            </summary>
        </member>
        <member name="P:Admin.NET.Core.PayCallBackOptions.WechatPayUrl">
            <summary>
            微信支付回调
            </summary>
        </member>
        <member name="P:Admin.NET.Core.PayCallBackOptions.WechatRefundUrl">
            <summary>
            微信退款回调
            </summary>
        </member>
        <member name="P:Admin.NET.Core.PayCallBackOptions.AlipayUrl">
            <summary>
            支付宝支付回调
            </summary>
        </member>
        <member name="P:Admin.NET.Core.PayCallBackOptions.AlipayRefundUrl">
            <summary>
            支付宝退款回调
            </summary>
        </member>
        <member name="T:Admin.NET.Core.IpRateLimitingOptions">
            <summary>
            IP限流配置选项
            </summary>
        </member>
        <member name="T:Admin.NET.Core.IpRateLimitPoliciesOptions">
            <summary>
            IP限流策略配置选项
            </summary>
        </member>
        <member name="T:Admin.NET.Core.ClientRateLimitingOptions">
            <summary>
            客户端限流配置选项
            </summary>
        </member>
        <member name="T:Admin.NET.Core.ClientRateLimitPoliciesOptions">
            <summary>
            客户端限流策略配置选项
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SnowIdOptions">
            <summary>
            雪花Id配置选项
            </summary>
        </member>
        <member name="T:Admin.NET.Core.UploadOptions">
            <summary>
            文件上传配置选项
            </summary>
        </member>
        <member name="P:Admin.NET.Core.UploadOptions.Path">
            <summary>
            路径
            </summary>
        </member>
        <member name="P:Admin.NET.Core.UploadOptions.MaxSize">
            <summary>
            大小
            </summary>
        </member>
        <member name="P:Admin.NET.Core.UploadOptions.ContentType">
            <summary>
            上传格式
            </summary>
        </member>
        <member name="T:Admin.NET.Core.WechatOptions">
            <summary>
            微信相关配置选项
            </summary>
        </member>
        <member name="T:Admin.NET.Core.WechatPayOptions">
            <summary>
            微信支付配置选项
            </summary>
        </member>
        <member name="P:Admin.NET.Core.WechatPayOptions.AppId">
            <summary>
            微信公众平台AppId、开放平台AppId、小程序AppId、企业微信CorpId
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysConfigSeedData">
            <summary>
            系统配置表种子数据
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SysConfigSeedData.HasData">
            <summary>
            种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.SysDictDataSeedData">
            <summary>
            系统字典值表种子数据
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SysDictDataSeedData.HasData">
            <summary>
            种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.SysDictTypeSeedData">
            <summary>
            系统字典类型表种子数据
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SysDictTypeSeedData.HasData">
            <summary>
            种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.SysMenuSeedData">
            <summary>
            系统菜单表种子数据
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SysMenuSeedData.HasData">
            <summary>
            种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.SysOrgSeedData">
            <summary>
            系统机构表种子数据
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SysOrgSeedData.HasData">
            <summary>
            种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.SysPosSeedData">
            <summary>
            系统职位表种子数据
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SysPosSeedData.HasData">
            <summary>
            种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.SysRoleMenuSeedData">
            <summary>
            系统角色菜单表种子数据
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SysRoleMenuSeedData.HasData">
            <summary>
            种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.SysRoleSeedData">
            <summary>
            系统角色表种子数据
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SysRoleSeedData.HasData">
            <summary>
            种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.SysTenantSeedData">
            <summary>
            系统租户表种子数据
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SysTenantSeedData.HasData">
            <summary>
            种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.SysUserExtOrgSeedData">
            <summary>
            系统用户扩展机构表种子数据
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SysUserExtOrgSeedData.HasData">
            <summary>
            种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.SysUserRoleSeedData">
            <summary>
            系统用户角色表种子数据
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SysUserRoleSeedData.HasData">
            <summary>
            种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.SysUserSeedData">
            <summary>
            系统用户表种子数据
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SysUserSeedData.HasData">
            <summary>
            种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.APIJSONService">
            <summary>
            APIJSON服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.APIJSONService.Post(Newtonsoft.Json.Linq.JObject)">
            <summary>
            统一入口
            </summary>
            <param name="jobject"></param>
            <remarks>参数：{"[]":{"SYS_LOG_OP":{}}}</remarks>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.FuncList">
            <summary>
            自定义方法
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.FuncList.Merge(System.Object,System.Object)">
            <summary>
            字符串相加
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.FuncList.MergeObj(System.Object,System.Object)">
            <summary>
            对象合并
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.FuncList.IsContain(System.Object,System.Object)">
            <summary>
            是否包含
            </summary>
            <param name="a"></param>
            <param name="b"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.IdentityService.GetUserIdentity">
            <summary>
            获取当前用户Id
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.IdentityService.GetUserRoleName">
            <summary>
            获取当前用户权限名称
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.IdentityService.GetRole">
            <summary>
            获取当前用户权限
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.IdentityService.GetSelectRole(System.String)">
            <summary>
            获取当前表的可查询字段
            </summary>
            <param name="table"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.IdentityService.ColIsRole(System.String,System.String[])">
            <summary>
            当前列是否在角色里面
            </summary>
            <param name="col"></param>
            <param name="selectrole"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SelectTable">
             <summary>
            
             </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SelectTable.#ctor(Admin.NET.Core.Service.IdentityService,Admin.NET.Core.Service.TableMapper,SqlSugar.ISqlSugarClient)">
             <summary>
            
             </summary>
             <param name="identityService"></param>
             <param name="tableMapper"></param>
             <param name="dbClient"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.SelectTable.IsTable(System.String)">
            <summary>
            判断表名是否正确
            </summary>
            <param name="table"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SelectTable.IsCol(System.String,System.String)">
            <summary>
            判断表的列名是否正确
            </summary>
            <param name="table"></param>
            <param name="col"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SelectTable.ExecFunc(System.String,System.Object[],System.Type[])">
            <summary>
            动态调用方法
            </summary>
            <param name="funcname"></param>
            <param name="param"></param>
            <param name="types"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SelectTable.GetTableData(System.String,System.Int32,System.Int32,System.Int32,System.String,Newtonsoft.Json.Linq.JObject)">
             <summary>
            
             </summary>
             <param name="subtable"></param>
             <param name="page"></param>
             <param name="count"></param>
             <param name="query"></param>
             <param name="json"></param>
             <param name="dd"></param>
             <returns></returns>
             <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Admin.NET.Core.Service.SelectTable.GetFirstData(System.String,System.String,Newtonsoft.Json.Linq.JObject)">
             <summary>
            
             </summary>
             <param name="subtable"></param>
             <param name="json"></param>
             <param name="dd"></param>
             <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SelectTable.Query(System.String)">
            <summary>
            解析并查询
            </summary>
            <param name="queryJson"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SelectTable.QuerySingle(Newtonsoft.Json.Linq.JObject,System.String)">
            <summary>
            单表查询
            </summary>
            <param name="queryObj"></param>
            <param name="nodeName">返回数据的节点名称  默认为 infos</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SelectTable.ToSql(Newtonsoft.Json.Linq.JObject)">
            <summary>
            获取查询语句
            </summary>
            <param name="queryObj"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SelectTable.Query(Newtonsoft.Json.Linq.JObject)">
            <summary>
            解析并查询
            </summary>
            <param name="queryObj"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.TableMapper">
            <summary>
            表名映射
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.TableMapper.GetTableName(System.String)">
            <summary>
            获取表别名
            </summary>
            <param name="oldname"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.LoginInput">
            <summary>
            用户登录参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginInput.Account">
            <summary>
            账号
            </summary>
            <example>admin</example>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginInput.Password">
            <summary>
            密码
            </summary>
            <example>123456</example>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginInput.CodeId">
            <summary>
            验证码Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginInput.Code">
            <summary>
            验证码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginInput.zyczydm">
            <summary>
            操作员代码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginInput.zyid">
            <summary>
            id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginInput.nsrsbh">
            <summary>
            纳税人识别号
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.LoginOutput">
            <summary>
            用户登录结果
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginOutput.AccessToken">
            <summary>
            令牌Token
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginOutput.RefreshToken">
            <summary>
            刷新Token
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.LoginUserOutput">
            <summary>
            用户登录信息
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginUserOutput.Account">
            <summary>
            账号名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginUserOutput.RealName">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginUserOutput.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginUserOutput.Introduction">
            <summary>
            个人简介
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginUserOutput.Address">
            <summary>
            地址
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginUserOutput.Signature">
            <summary>
            电子签名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginUserOutput.OrgId">
            <summary>
            机构Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginUserOutput.OrgName">
            <summary>
            机构名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginUserOutput.PosName">
            <summary>
            职位名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginUserOutput.Buttons">
            <summary>
            按钮权限集合
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginUserOutput.DPUserName">
            <summary>
            电票平台账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginUserOutput.DPPassword">
            <summary>
            电票平台账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginUserOutput.Prov">
            <summary>
            税务区域
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LoginUserOutput.TaxId">
            <summary>
            税务登记号
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysAuthService">
            <summary>
            系统登录授权服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysAuthService.Login(Admin.NET.Core.Service.LoginInput)">
            <summary>
            登录系统
            </summary>
            <param name="input"></param>
            <remarks>用户名/密码：superadmin/123456</remarks>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysAuthService.GetUserInfo">
            <summary>
            获取登录账号
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysAuthService.GetRefreshToken(System.String)">
            <summary>
            获取刷新Token
            </summary>
            <param name="accessToken"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysAuthService.Logout">
            <summary>
            退出系统
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysAuthService.GetLoginConfig">
            <summary>
            获取登录配置
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysAuthService.GetUserConfig">
            <summary>
            获取用户配置
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysAuthService.GetCaptcha">
            <summary>
            获取验证码
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysAuthService.SwaggerCheckUrl">
            <summary>
            swagger登录检查
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysAuthService.SwaggerSubmitUrl(Furion.SpecificationDocument.SpecificationAuth)">
            <summary>
            swagger登录提交
            </summary>
            <param name="auth"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysCacheService">
            <summary>
            系统缓存服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCacheService.GetKeyList">
            <summary>
            获取缓存键名集合
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCacheService.Set(System.String,System.Object)">
            <summary>
            增加缓存
            </summary>
            <param name="key"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCacheService.Set(System.String,System.Object,System.TimeSpan)">
            <summary>
            增加缓存并设置过期时间
            </summary>
            <param name="key"></param>
            <param name="value"></param>
            <param name="expire"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCacheService.Get``1(System.String)">
            <summary>
            获取缓存
            </summary>
            <typeparam name="T"></typeparam>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCacheService.Remove(System.String)">
            <summary>
            删除缓存
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCacheService.ExistKey(System.String)">
            <summary>
            检查缓存是否存在
            </summary>
            <param name="key">键</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCacheService.RemoveByPrefixKey(System.String)">
            <summary>
            根据键名前缀删除缓存
            </summary>
            <param name="prefixKey">键名前缀</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCacheService.GetValue(System.String)">
            <summary>
            获取缓存值
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="P:Admin.NET.Core.Service.CustomViewEngine.ConfigId">
            <summary>
            库定位器
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.CodeGenConfig">
            <summary>
            代码生成详细配置参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.CodeGenId">
            <summary>
            代码生成主表ID
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.ColumnName">
            <summary>
            数据库字段名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.LowerColumnName">
            <summary>
            数据库字段名(首字母小写)
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.ColumnComment">
            <summary>
            字段描述
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.NetType">
            <summary>
            .NET类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.EffectType">
            <summary>
            作用类型（字典）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.FkEntityName">
            <summary>
            外键实体名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.FkTableName">
            <summary>
            外键表名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.LowerFkEntityName">
            <summary>
            外键实体名称(首字母小写)
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.FkColumnName">
            <summary>
            外键显示字段
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.LowerFkColumnName">
            <summary>
            外键显示字段(首字母小写)
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.FkColumnNetType">
            <summary>
            外键显示字段.NET类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.DictTypeCode">
            <summary>
            字典code
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.WhetherRetract">
            <summary>
            列表是否缩进（字典）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.WhetherRequired">
            <summary>
            是否必填（字典）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.QueryWhether">
            <summary>
            是否是查询条件
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.QueryType">
            <summary>
            查询方式
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.WhetherTable">
            <summary>
            列表显示
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.WhetherAddUpdate">
            <summary>
            增改
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.ColumnKey">
            <summary>
            主外键
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.DataType">
            <summary>
            数据库中类型（物理类型）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.WhetherCommon">
            <summary>
            是否是通用字段
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.TableNickName">
            <summary>
            表的别名 Table as XXX
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.DisplayColumn">
            <summary>
            显示文本字段
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.ValueColumn">
            <summary>
            选中值字段
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenConfig.PidColumn">
            <summary>
            父级字段
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.CodeGenInput">
            <summary>
            代码生成参数类
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenInput.AuthorName">
            <summary>
            作者姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenInput.ClassName">
            <summary>
            类名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenInput.TablePrefix">
            <summary>
            是否移除表前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenInput.ConfigId">
            <summary>
            库定位器名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenInput.DbName">
            <summary>
            数据库名(保留字段)
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenInput.DbType">
            <summary>
            数据库类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenInput.ConnectionString">
            <summary>
            数据库链接
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenInput.GenerateType">
            <summary>
            生成方式
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenInput.TableName">
            <summary>
            数据库表名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenInput.NameSpace">
            <summary>
            命名空间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenInput.BusName">
            <summary>
            业务名（业务代码包名称）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenInput.TableComment">
            <summary>
            功能名（数据库表名称）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenInput.MenuApplication">
            <summary>
            菜单应用分类（应用编码）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenInput.MenuPid">
            <summary>
            菜单父级
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddCodeGenInput.TableName">
            <summary>
            数据库表名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddCodeGenInput.BusName">
            <summary>
            业务名（业务代码包名称）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddCodeGenInput.NameSpace">
            <summary>
            命名空间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddCodeGenInput.AuthorName">
            <summary>
            作者姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddCodeGenInput.GenerateType">
            <summary>
            生成方式
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddCodeGenInput.MenuPid">
            <summary>
            菜单父级
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.DeleteCodeGenInput.Id">
            <summary>
            代码生成器Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.UpdateCodeGenInput.Id">
            <summary>
            代码生成器Id
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.CodeGenOutput">
            <summary>
            代码生成参数类
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenOutput.Id">
            <summary>
            代码生成器Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenOutput.AuthorName">
            <summary>
            作者姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenOutput.ClassName">
            <summary>
            类名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenOutput.TablePrefix">
            <summary>
            是否移除表前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenOutput.GenerateType">
            <summary>
            生成方式
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenOutput.TableName">
            <summary>
            数据库表名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenOutput.PackageName">
            <summary>
            包名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenOutput.BusName">
            <summary>
            业务名（业务代码包名称）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenOutput.TableComment">
            <summary>
            功能名（数据库表名称）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenOutput.MenuApplication">
            <summary>
            菜单应用分类（应用编码）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.CodeGenOutput.MenuPid">
            <summary>
            菜单父级
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.ColumnOuput">
            <summary>
            数据库表列
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.ColumnOuput.ColumnName">
            <summary>
            字段名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.ColumnOuput.DataType">
            <summary>
            数据库中类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.ColumnOuput.NetType">
            <summary>
            .NET字段类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.ColumnOuput.ColumnComment">
            <summary>
            字段描述
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.ColumnOuput.ColumnKey">
            <summary>
            主外键
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.DatabaseOutput">
            <summary>
            数据库
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.DatabaseOutput.ConfigId">
            <summary>
            库定位器名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.DatabaseOutput.DbType">
            <summary>
            数据库类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.DatabaseOutput.ConnectionString">
            <summary>
            数据库连接字符串
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.TableOutput">
            <summary>
            数据库表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TableOutput.ConfigId">
            <summary>
            库定位器名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TableOutput.TableName">
            <summary>
            表名（字母形式的）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TableOutput.EntityName">
            <summary>
            实体名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TableOutput.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TableOutput.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TableOutput.TableComment">
            <summary>
            表名称描述（功能名）
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysCodeGenConfigService">
            <summary>
            系统代码生成配置服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenConfigService.GetList(Admin.NET.Core.Service.CodeGenConfig)">
            <summary>
            获取代码生成配置列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenConfigService.UpdateCodeGenConfig(System.Collections.Generic.List{Admin.NET.Core.Service.CodeGenConfig})">
            <summary>
            更新代码生成配置
            </summary>
            <param name="inputList"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenConfigService.DeleteCodeGenConfig(System.Int64)">
            <summary>
            删除代码生成配置
            </summary>
            <param name="codeGenId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenConfigService.GetDetail(Admin.NET.Core.Service.CodeGenConfig)">
            <summary>
            获取代码生成配置详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenConfigService.AddList(System.Collections.Generic.List{Admin.NET.Core.Service.ColumnOuput},Admin.NET.Core.SysCodeGen)">
            <summary>
            批量增加代码生成配置
            </summary>
            <param name="tableColumnOuputList"></param>
            <param name="codeGenerate"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenConfigService.GetDefaultQueryType(Admin.NET.Core.SysCodeGenConfig)">
            <summary>
            默认查询类型
            </summary>
            <param name="codeGenConfig"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysCodeGenService">
            <summary>
            系统代码生成器服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.Page(Admin.NET.Core.Service.CodeGenInput)">
            <summary>
            获取代码生成分页列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.AddCodeGen(Admin.NET.Core.Service.AddCodeGenInput)">
            <summary>
            增加代码生成
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.UpdateCodeGen(Admin.NET.Core.Service.UpdateCodeGenInput)">
            <summary>
            更新代码生成
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.DeleteCodeGen(System.Collections.Generic.List{Admin.NET.Core.Service.DeleteCodeGenInput})">
            <summary>
            删除代码生成
            </summary>
            <param name="inputs"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.GetDetail(Admin.NET.Core.Service.QueryCodeGenInput)">
            <summary>
            获取代码生成详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.GetDatabaseList">
            <summary>
            获取数据库库集合
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.GetTableList(System.String)">
            <summary>
            获取数据库表(实体)集合
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.GetColumnListByTableName(System.String,System.String)">
            <summary>
            根据表名获取列集合
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.GetColumnList(Admin.NET.Core.Service.AddCodeGenInput)">
            <summary>
            获取数据表列（实体属性）集合
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.GetEntityInfos">
            <summary>
            获取库表信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.RunLocal(Admin.NET.Core.SysCodeGen)">
            <summary>
            代码生成到本地
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.GetJoinTableStr(System.Collections.Generic.List{Admin.NET.Core.Service.CodeGenConfig})">
            <summary>
            获取连表的实体名和别名
            </summary>
            <param name="configs"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.AddMenu(System.String,System.String,System.Int64)">
            <summary>
            增加菜单
            </summary>
            <param name="className"></param>
            <param name="busName"></param>
            <param name="pid"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.GetTemplatePathList(Admin.NET.Core.SysCodeGen)">
            <summary>
            获取模板文件路径集合
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.GetTemplatePathList">
            <summary>
            获取模板文件路径集合
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.GetTargetPathList(Admin.NET.Core.SysCodeGen)">
            <summary>
            设置生成文件路径
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysCodeGenService.GetZipPathList(Admin.NET.Core.SysCodeGen)">
            <summary>
            设置生成文件路径
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="P:Admin.NET.Core.Service.PageConfigInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageConfigInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageConfigInput.GroupCode">
            <summary>
            分组编码
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysConfigService">
            <summary>
            系统参数配置服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysConfigService.Page(Admin.NET.Core.Service.PageConfigInput)">
            <summary>
            获取参数配置分页列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysConfigService.GetList">
            <summary>
            获取参数配置列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysConfigService.AddConfig(Admin.NET.Core.Service.AddConfigInput)">
            <summary>
            增加参数配置
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysConfigService.UpdateConfig(Admin.NET.Core.Service.UpdateConfigInput)">
            <summary>
            更新参数配置
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysConfigService.DeleteConfig(Admin.NET.Core.Service.DeleteConfigInput)">
            <summary>
            删除参数配置
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysConfigService.BatchDeleteConfig(System.Collections.Generic.List{System.Int64})">
            <summary>
            批量删除参数配置
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysConfigService.GetDetail(Admin.NET.Core.Service.ConfigInput)">
            <summary>
            获取参数配置详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysConfigService.GetConfigValue``1(System.String)">
            <summary>
            获取参数配置值
            </summary>
            <param name="code"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysConfigService.GetGroupList">
            <summary>
            获取分组列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysConfigService.GetTokenExpire">
            <summary>
            获取 Token 过期时间
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysConfigService.GetRefreshTokenExpire">
            <summary>
            获取 RefreshToken 过期时间
            </summary>
            <returns></returns>
        </member>
        <member name="P:Admin.NET.Core.Service.ConstOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.ConstOutput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.ConstOutput.Data">
            <summary>
            扩展字段
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysConstService">
            <summary>
            系统常量服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysConstService.GetList">
            <summary>
            获取所有常量列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysConstService.GetData(System.String)">
            <summary>
            根据类名获取常量数据
            </summary>
            <param name="typeName"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysConstService.GetConstAttributeList">
            <summary>
            获取常量特性类型列表
            </summary>
            <returns></returns>
        </member>
        <member name="P:Admin.NET.Core.Service.CreateEntityInput.TableName">
            <summary>
            表名
            </summary>
            <example>student</example>
        </member>
        <member name="P:Admin.NET.Core.Service.CreateEntityInput.EntityName">
            <summary>
            实体名
            </summary>
            <example>Student</example>
        </member>
        <member name="P:Admin.NET.Core.Service.CreateEntityInput.BaseClassName">
            <summary>
            基类名
            </summary>
            <example>AutoIncrementEntity</example>
        </member>
        <member name="P:Admin.NET.Core.Service.CreateEntityInput.Position">
            <summary>
            导出位置
            </summary>
            <example>Web.Application</example>
        </member>
        <member name="P:Admin.NET.Core.Service.CreateEntityInput.ConfigId">
            <summary>
            库标识
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysDatabaseService">
            <summary>
            系统数据库管理服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDatabaseService.GetList">
            <summary>
            获取库列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDatabaseService.GetColumnList(System.String,System.String)">
            <summary>
            获取字段列表
            </summary>
            <param name="tableName">表名</param>
            <param name="configId">ConfigId</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDatabaseService.AddColumn(Admin.NET.Core.Service.DbColumnInput)">
            <summary>
            增加列
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDatabaseService.DeleteColumn(Admin.NET.Core.Service.DeleteDbColumnInput)">
            <summary>
            删除列
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDatabaseService.UpdateColumn(Admin.NET.Core.Service.UpdateDbColumnInput)">
            <summary>
            编辑列
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDatabaseService.GetTableList(System.String)">
            <summary>
            获取表列表
            </summary>
            <param name="configId">ConfigId</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDatabaseService.AddTable(Admin.NET.Core.Service.DbTableInput)">
            <summary>
            增加表
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDatabaseService.DeleteTable(Admin.NET.Core.Service.DeleteDbTableInput)">
            <summary>
            删除表
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDatabaseService.UpdateTable(Admin.NET.Core.Service.UpdateDbTableInput)">
            <summary>
            编辑表
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDatabaseService.CreateEntity(Admin.NET.Core.Service.CreateEntityInput)">
            <summary>
            创建实体
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDatabaseService.GetEntityTemplatePath">
            <summary>
            获取实体模板文件路径
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDatabaseService.GetEntityTargetPath(Admin.NET.Core.Service.CreateEntityInput)">
            <summary>
            设置生成实体文件路径
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="P:Admin.NET.Core.Service.DictDataInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageDictDataInput.DictTypeId">
            <summary>
            字典类型Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageDictDataInput.Value">
            <summary>
            值
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageDictDataInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.GetDataDictDataInput.DictTypeId">
            <summary>
            字典类型Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.QueryDictDataInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.QueryDictDataInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.DictTypeInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageDictTypeInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageDictTypeInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.GetDataDictTypeInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysDictDataService">
            <summary>
            系统字典值服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictDataService.Page(Admin.NET.Core.Service.PageDictDataInput)">
            <summary>
            获取字典值分页列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictDataService.GetList(Admin.NET.Core.Service.GetDataDictDataInput)">
            <summary>
            获取字典值列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictDataService.AddDictData(Admin.NET.Core.Service.AddDictDataInput)">
            <summary>
            增加字典值
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictDataService.UpdateDictData(Admin.NET.Core.Service.UpdateDictDataInput)">
            <summary>
            更新字典值
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictDataService.DeleteDictData(Admin.NET.Core.Service.DeleteDictDataInput)">
            <summary>
            删除字典值
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictDataService.GetDetail(Admin.NET.Core.Service.DictDataInput)">
            <summary>
            获取字典值详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictDataService.SetStatus(Admin.NET.Core.Service.DictDataInput)">
            <summary>
            修改字典值状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictDataService.GetDictDataListByDictTypeId(System.Int64)">
            <summary>
            根据字典类型Id获取字典值集合
            </summary>
            <param name="dictTypeId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictDataService.GetDataList(System.String)">
            <summary>
            根据字典类型编码获取字典值集合
            </summary>
            <param name="code"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictDataService.GetDataList(Admin.NET.Core.Service.QueryDictDataInput)">
            <summary>
            根据查询条件获取字典值集合
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictDataService.DeleteDictData(System.Int64)">
            <summary>
            根据字典类型Id删除字典值
            </summary>
            <param name="dictTypeId"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysDictTypeService">
            <summary>
            系统字典类型服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictTypeService.Page(Admin.NET.Core.Service.PageDictTypeInput)">
            <summary>
            获取字典类型分页列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictTypeService.GetList">
            <summary>
            获取字典类型列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictTypeService.GetDataList(Admin.NET.Core.Service.GetDataDictTypeInput)">
            <summary>
            获取字典类型-值列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictTypeService.AddDictType(Admin.NET.Core.Service.AddDictTypeInput)">
            <summary>
            添加字典类型
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictTypeService.UpdateDictType(Admin.NET.Core.Service.UpdateDictTypeInput)">
            <summary>
            更新字典类型
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictTypeService.DeleteDictType(Admin.NET.Core.Service.DeleteDictTypeInput)">
            <summary>
            删除字典类型
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictTypeService.GetDetail(Admin.NET.Core.Service.DictTypeInput)">
            <summary>
            获取字典类型详情
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictTypeService.SetStatus(Admin.NET.Core.Service.DictTypeInput)">
            <summary>
            修改字典类型状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysDictTypeService.GetAllDictList">
            <summary>
            获取所有字典集合 🔖
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.EnumInput">
            <summary>
            枚举输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.EnumInput.EnumName">
            <summary>
            枚举类型名称
            </summary>
            <example>AccountTypeEnum</example>
        </member>
        <member name="P:Admin.NET.Core.Service.QueryEnumDataInput.EntityName">
            <summary>
            实体名称
            </summary>
            <example>SysUser</example>
        </member>
        <member name="P:Admin.NET.Core.Service.QueryEnumDataInput.FieldName">
            <summary>
            字段名称
            </summary>
            <example>AccountType</example>
        </member>
        <member name="T:Admin.NET.Core.Service.EnumTypeOutput">
            <summary>
            枚举类型输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.EnumTypeOutput.TypeDescribe">
            <summary>
            枚举类型描述
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.EnumTypeOutput.TypeName">
            <summary>
            枚举类型名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.EnumTypeOutput.TypeRemark">
            <summary>
            枚举类型备注
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysEnumService">
            <summary>
            系统枚举服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysEnumService.GetEnumTypeList">
            <summary>
            获取所有枚举类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysEnumService.GetEnumDataList(Admin.NET.Core.Service.EnumInput)">
            <summary>
            通过枚举类型获取枚举值集合
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysEnumService.GetEnumDataListByField(Admin.NET.Core.Service.QueryEnumDataInput)">
            <summary>
            通过实体的字段名获取相关枚举值集合（目前仅支持枚举类型）
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="P:Admin.NET.Core.Service.PageFileInput.FileName">
            <summary>
            文件名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageFileInput.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageFileInput.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.FileOutput.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.FileOutput.Provider">
            <summary>
            提供者
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.FileOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.FileOutput.Url">
            <summary>
            URL
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.FileOutput.SizeKb">
            <summary>
            大小
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.FileOutput.Suffix">
            <summary>
            后缀
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.FileOutput.FilePath">
            <summary>
            路径
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.FileOutput.FileName">
            <summary>
            文件名称
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysFileService">
            <summary>
            系统文件服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysFileService.Page(Admin.NET.Core.Service.PageFileInput)">
            <summary>
            获取文件分页列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysFileService.UploadFile(Microsoft.AspNetCore.Http.IFormFile,System.String)">
            <summary>
            上传文件
            </summary>
            <param name="file"></param>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysFileService.UploadFiles(System.Collections.Generic.List{Microsoft.AspNetCore.Http.IFormFile})">
            <summary>
            上传多文件
            </summary>
            <param name="files"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysFileService.DownloadFile(Admin.NET.Core.Service.FileInput)">
            <summary>
            下载文件(文件流)
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysFileService.DeleteFile(Admin.NET.Core.Service.DeleteFileInput)">
            <summary>
            删除文件
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysFileService.GetFile(Admin.NET.Core.Service.FileInput)">
            <summary>
            获取文件
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysFileService.HandleUploadFile(Microsoft.AspNetCore.Http.IFormFile,System.String)">
            <summary>
            上传文件
            </summary>
            <param name="file">文件</param>
            <param name="savePath">路径</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysFileService.GetMinioPreviewFileUrl(System.String,System.String)">
            <summary>
            获取Minio文件的下载或者预览地址
            </summary>
            <param name="bucketName">桶名</param>
            <param name="fileName">文件名</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysFileService.UploadAvatar(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            上传头像
            </summary>
            <param name="file"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysFileService.UploadSignature(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            上传电子签名
            </summary>
            <param name="file"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.DbJobPersistence">
            <summary>
            作业持久化（数据库）
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.DbJobPersistence.Preload">
            <summary>
            作业调度服务启动时
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.DbJobPersistence.OnLoading(Furion.Schedule.SchedulerBuilder)">
            <summary>
            作业计划初始化通知
            </summary>
            <param name="builder"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.DbJobPersistence.OnChanged(Furion.Schedule.PersistenceContext)">
            <summary>
            作业计划Scheduler的JobDetail变化时
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.DbJobPersistence.OnTriggerChanged(Furion.Schedule.PersistenceTriggerContext)">
            <summary>
            作业计划Scheduler的触发器Trigger变化时
            </summary>
            <param name="context"></param>
        </member>
        <member name="P:Admin.NET.Core.Service.JobDetailInput.JobId">
            <summary>
            作业Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageJobInput.JobId">
            <summary>
            作业Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageJobInput.Description">
            <summary>
            描述信息
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddJobDetailInput.JobId">
            <summary>
            作业Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.JobOutput.JobDetail">
            <summary>
            作业信息
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.JobOutput.JobTriggers">
            <summary>
            触发器集合
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.JobTriggerInput.JobId">
            <summary>
            作业Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.JobTriggerInput.TriggerId">
            <summary>
            触发器Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddJobTriggerInput.JobId">
            <summary>
            作业Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddJobTriggerInput.TriggerId">
            <summary>
            触发器Id
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.JobClusterServer">
            <summary>
            作业集群控制
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.JobClusterServer.Start(Furion.Schedule.JobClusterContext)">
            <summary>
            当前作业调度器启动通知
            </summary>
            <param name="context">作业集群服务上下文</param>
        </member>
        <member name="M:Admin.NET.Core.Service.JobClusterServer.WaitingForAsync(Furion.Schedule.JobClusterContext)">
            <summary>
            等待被唤醒
            </summary>
            <param name="context">作业集群服务上下文</param>
            <returns><see cref="T:System.Threading.Tasks.Task"/></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.JobClusterServer.Stop(Furion.Schedule.JobClusterContext)">
            <summary>
            当前作业调度器停止通知
            </summary>
            <param name="context">作业集群服务上下文</param>
        </member>
        <member name="M:Admin.NET.Core.Service.JobClusterServer.Crash(Furion.Schedule.JobClusterContext)">
            <summary>
            当前作业调度器宕机
            </summary>
            <param name="context">作业集群服务上下文</param>
        </member>
        <member name="M:Admin.NET.Core.Service.JobClusterServer.WorkNowAsync(System.String)">
            <summary>
            指示集群可以工作
            </summary>
            <param name="clusterId">集群 Id</param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysJobService">
            <summary>
            系统作业任务服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.PageJobDetail(Admin.NET.Core.Service.PageJobInput)">
            <summary>
            获取作业分页列表
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.AddJobDetail(Admin.NET.Core.Service.AddJobDetailInput)">
            <summary>
            添加作业
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.UpdateJobDetail(Admin.NET.Core.Service.UpdateJobDetailInput)">
            <summary>
            更新作业
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.DeleteJobDetail(Admin.NET.Core.Service.DeleteJobDetailInput)">
            <summary>
            删除作业
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.GetJobTriggerList(Admin.NET.Core.Service.JobDetailInput)">
            <summary>
            获取触发器列表
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.AddJobTrigger(Admin.NET.Core.Service.AddJobTriggerInput)">
            <summary>
            添加触发器
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.UpdateJobTrigger(Admin.NET.Core.Service.UpdateJobTriggerInput)">
            <summary>
            更新触发器
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.DeleteJobTrigger(Admin.NET.Core.Service.DeleteJobTriggerInput)">
            <summary>
            删除触发器
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.PauseAllJob">
            <summary>
            暂停所有作业
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.StartAllJob">
            <summary>
            启动所有作业
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.PauseJob(Admin.NET.Core.Service.JobDetailInput)">
            <summary>
            暂停作业
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.StartJob(Admin.NET.Core.Service.JobDetailInput)">
            <summary>
            启动作业
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.PauseTrigger(Admin.NET.Core.Service.JobTriggerInput)">
            <summary>
            暂停触发器
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.StartTrigger(Admin.NET.Core.Service.JobTriggerInput)">
            <summary>
            启动触发器
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.CancelSleep">
            <summary>
            强制唤醒作业调度器
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.PersistAll">
            <summary>
            强制触发所有作业持久化
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysJobService.GetJobClusterList">
            <summary>
            获取集群列表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageLogInput.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageLogInput.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LogInput.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.LogInput.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysLogDiffService">
            <summary>
            系统差异日志服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysLogDiffService.Page(Admin.NET.Core.Service.PageLogInput)">
            <summary>
            获取差异日志分页列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysLogDiffService.Clear">
            <summary>
            清空差异日志
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysLogExService">
            <summary>
            系统异常日志服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysLogExService.Page(Admin.NET.Core.Service.PageLogInput)">
            <summary>
            获取异常日志分页列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysLogExService.Clear">
            <summary>
            清空异常日志
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysLogExService.ExportLogEx(Admin.NET.Core.Service.LogInput)">
            <summary>
            导出异常日志
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysLogOpService">
            <summary>
            系统操作日志服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysLogOpService.Page(Admin.NET.Core.Service.PageLogInput)">
            <summary>
            获取操作日志分页列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysLogOpService.Clear">
            <summary>
            清空操作日志
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysLogOpService.ExportLogOp(Admin.NET.Core.Service.LogInput)">
            <summary>
            导出操作日志
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysLogVisService">
            <summary>
            系统访问日志服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysLogVisService.Page(Admin.NET.Core.Service.PageLogInput)">
            <summary>
            获取访问日志分页列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysLogVisService.Clear">
            <summary>
            清空访问日志
            </summary>
            <returns></returns>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuInput.Title">
            <summary>
            标题
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuInput.Type">
            <summary>
            菜单类型（1目录 2菜单 3按钮）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddMenuInput.Title">
            <summary>
            名称
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.MenuOutput">
            <summary>
            系统菜单返回结果
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuOutput.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuOutput.Pid">
            <summary>
            父Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuOutput.Type">
            <summary>
            菜单类型（0目录 1菜单 2按钮）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuOutput.Path">
            <summary>
            路由地址
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuOutput.Component">
            <summary>
            组件路径
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuOutput.Permission">
            <summary>
            权限标识
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuOutput.Redirect">
            <summary>
            重定向
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuOutput.OrderNo">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuOutput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuOutput.Meta">
            <summary>
            菜单Meta
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.MenuOutput.Children">
            <summary>
            菜单子项
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysMenuMeta">
            <summary>
            菜单Meta配置
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.SysMenuMeta.Title">
            <summary>
            标题
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.SysMenuMeta.Icon">
            <summary>
            图标
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.SysMenuMeta.IsIframe">
            <summary>
            是否内嵌
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.SysMenuMeta.IsLink">
            <summary>
            外链链接
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.SysMenuMeta.IsHide">
            <summary>
            是否隐藏
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.SysMenuMeta.IsKeepAlive">
            <summary>
            是否缓存
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.SysMenuMeta.IsAffix">
            <summary>
            是否固定
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysMenuMapper">
            <summary>
            配置菜单对象映射
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysMenuService">
            <summary>
            系统菜单服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMenuService.GetLoginMenuTree">
            <summary>
            获取登录菜单树
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMenuService.DeleteBtnFromMenuTree(System.Collections.Generic.List{Admin.NET.Core.SysMenu})">
            <summary>
            删除登录菜单树里面的按钮
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMenuService.GetList(Admin.NET.Core.Service.MenuInput)">
            <summary>
            获取菜单列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMenuService.AddMenu(Admin.NET.Core.Service.AddMenuInput)">
            <summary>
            增加菜单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMenuService.UpdateMenu(Admin.NET.Core.Service.UpdateMenuInput)">
            <summary>
            更新菜单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMenuService.DeleteMenu(Admin.NET.Core.Service.DeleteMenuInput)">
            <summary>
            删除菜单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMenuService.CheckMenuParam(Admin.NET.Core.SysMenu)">
            <summary>
            增加和编辑时检查菜单数据
            </summary>
            <param name="menu"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMenuService.GetOwnBtnPermList">
            <summary>
            获取用户拥有按钮权限集合（缓存）
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMenuService.GetAllBtnPermList">
            <summary>
            获取系统所有按钮权限集合（缓存）
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMenuService.DeleteMenuCache">
            <summary>
            清除菜单和按钮缓存
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMenuService.GetMenuIdList">
            <summary>
            获取当前用户菜单Id集合
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysMessageService">
            <summary>
            系统消息发送服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMessageService.SendAllUser(Admin.NET.Core.MessageInput)">
            <summary>
            发送消息给所有人
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMessageService.SendOtherUser(Admin.NET.Core.MessageInput)">
            <summary>
            发送消息给除了发送人的其他人
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMessageService.SendUser(Admin.NET.Core.MessageInput)">
            <summary>
            发送消息给某个人
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMessageService.SendUsers(Admin.NET.Core.MessageInput)">
            <summary>
            发送消息给某些人
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysMessageService.SendEmail(System.String)">
            <summary>
            发送邮件
            </summary>
            <param name="message"></param>
            <returns></returns>
        </member>
        <member name="P:Admin.NET.Core.Service.PageNoticeInput.Title">
            <summary>
            标题
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageNoticeInput.MessageLevel">
            <summary>
            消息级别（1系统消息 2公司消息）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageNoticeInput.Type">
            <summary>
            类型（1通知 2公告）
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysNoticeService">
            <summary>
            系统通知公告服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysNoticeService.Page(Admin.NET.Core.Service.PageNoticeInput)">
            <summary>
            获取通知公告分页列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysNoticeService.AddNotice(Admin.NET.Core.Service.AddNoticeInput)">
            <summary>
            增加通知公告
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysNoticeService.UpdateNotice(Admin.NET.Core.Service.UpdateNoticeInput)">
            <summary>
            更新通知公告
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysNoticeService.DeleteNotice(Admin.NET.Core.Service.DeleteNoticeInput)">
            <summary>
            删除通知公告
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysNoticeService.Public(Admin.NET.Core.Service.NoticeInput)">
            <summary>
            发布通知公告
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysNoticeService.SetRead(Admin.NET.Core.Service.NoticeInput)">
            <summary>
            设置通知公告已读状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysNoticeService.GetPageReceived(Admin.NET.Core.Service.PageNoticeInput)">
            <summary>
            获取接收的通知公告
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysNoticeService.GetUnReadList">
            <summary>
            获取未读的通知公告
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysNoticeService.InitNoticeInfo(Admin.NET.Core.SysNotice)">
            <summary>
            初始化通知公告信息
            </summary>
            <param name="notice"></param>
        </member>
        <member name="P:Admin.NET.Core.Service.PageOnlineUserInput.UserName">
            <summary>
            账号名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageOnlineUserInput.RealName">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysOnlineUserService">
            <summary>
            系统在线用户服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysOnlineUserService.Page(Admin.NET.Core.Service.PageOnlineUserInput)">
            <summary>
            获取在线用户分页列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysOnlineUserService.ForceOffline(Admin.NET.Core.SysOnlineUser)">
            <summary>
            强制下线
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysOnlineUserService.PublicNotice(Admin.NET.Core.SysNotice,System.Collections.Generic.List{System.Int64})">
            <summary>
            发布站内消息
            </summary>
            <param name="notice"></param>
            <param name="userIds"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysOnlineUserService.SignleLogin(System.Int64)">
            <summary>
            单用户登录
            </summary>
            <returns></returns>
        </member>
        <member name="P:Admin.NET.Core.Service.OrgInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.OrgInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.OrgInput.OrgType">
            <summary>
            机构类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddOrgInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysOrgService">
            <summary>
            系统机构服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysOrgService.GetList(Admin.NET.Core.Service.OrgInput)">
            <summary>
            获取机构列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysOrgService.GetSysOrgDetail(System.Int64)">
            <summary>
            获取机构信息详情
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysOrgService.AddOrg(Admin.NET.Core.Service.AddOrgInput)">
            <summary>
            增加机构
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysOrgService.UpdateOrg(Admin.NET.Core.Service.UpdateOrgInput)">
            <summary>
            更新机构
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysOrgService.DeleteOrg(Admin.NET.Core.Service.DeleteOrgInput)">
            <summary>
            删除机构
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysOrgService.GetUserOrgIdList">
            <summary>
            根据用户Id获取机构Id集合
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysOrgService.GetUserRoleOrgIdList(System.Int64)">
            <summary>
            获取用户角色机构Id集合
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysOrgService.GetUserOrgIdList(System.Collections.Generic.List{Admin.NET.Core.SysRole})">
            <summary>
            根据角色Id集合获取机构Id集合
            </summary>
            <param name="roleList"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysOrgService.GetOrgIdListByDataScope(System.Int32)">
            <summary>
            根据数据范围获取机构Id集合
            </summary>
            <param name="dataScope"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysOrgService.GetChildIdListWithSelfById(System.Int64)">
            <summary>
            根据节点Id获取子节点Id集合(包含自己)
            </summary>
            <param name="pid"></param>
            <returns></returns>
        </member>
        <member name="P:Admin.NET.Core.Service.PagePluginInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PagePluginInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddPluginInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysPluginService">
            <summary>
            系统动态插件服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPluginService.Page(Admin.NET.Core.Service.PagePluginInput)">
            <summary>
            获取动态插件列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPluginService.AddPlugin(Admin.NET.Core.Service.AddPluginInput)">
            <summary>
            增加动态插件
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPluginService.UpdatePlugin(Admin.NET.Core.Service.UpdatePluginInput)">
            <summary>
            更新动态插件
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPluginService.DeletePlugin(Admin.NET.Core.Service.DeletePluginInput)">
            <summary>
            删除动态插件
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPluginService.CompileAssembly(System.String,System.String)">
            <summary>
            添加动态程序集/接口
            </summary>
            <param name="csharpCode"></param>
            <param name="assemblyName">程序集名称</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPluginService.RemoveAssembly(System.String)">
            <summary>
            移除动态程序集/接口
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PosInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PosInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddPosInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysPosService">
            <summary>
            系统职位服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPosService.GetList(Admin.NET.Core.Service.PosInput)">
            <summary>
            获取职位列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPosService.GetListByOrg(System.Collections.Generic.List{System.Int64})">
            <summary>
            获取职位列表
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPosService.AddPos(Admin.NET.Core.Service.AddPosInput)">
            <summary>
            增加职位
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPosService.UpdatePos(Admin.NET.Core.Service.UpdatePosInput)">
            <summary>
            更新职位
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPosService.DeletePos(Admin.NET.Core.Service.DeletePosInput)">
            <summary>
            删除职位
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="P:Admin.NET.Core.Service.PagePrintInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PagePrintInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddPrintInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.OnePrintInput.PrintTemType">
            <summary>
            模板类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PrintOutput.GroupKey">
            <summary>
            分组键
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PrintOutput.GroupName">
            <summary>
            分组名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PrintOutput.ListPrint">
            <summary>
            打印模板集合
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysPrintService">
            <summary>
            系统打印模板服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPrintService.Page(Admin.NET.Core.Service.PagePrintInput)">
            <summary>
            获取打印模板列表 🖨️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPrintService.GetPrint(System.String)">
            <summary>
            获取打印模板 🖨️
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPrintService.GetOnePrint(Admin.NET.Core.Service.OnePrintInput)">
            <summary>
            获取单个打印模板 🖨️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPrintService.AddPrint(Admin.NET.Core.Service.AddPrintInput)">
            <summary>
            增加打印模板 🖨️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPrintService.UpdatePrint(Admin.NET.Core.Service.UpdatePrintInput)">
            <summary>
            更新打印模板 🖨️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysPrintService.DeletePrint(Admin.NET.Core.Service.DeletePrintInput)">
            <summary>
            删除打印模板 🖨️
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.PubOrderDto">
            <summary>
            公用序号输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PubOrderDto.Prefix">
            <summary>
            前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PubOrderDto.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PubOrderDto.SN">
            <summary>
            序号
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.PubOrderBaseInput">
            <summary>
            公用序号基础输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PubOrderBaseInput.Prefix">
            <summary>
            前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PubOrderBaseInput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PubOrderBaseInput.SN">
            <summary>
            序号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PubOrderBaseInput.TenantId">
            <summary>
            租户Id
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.PubOrderInput">
            <summary>
            公用序号分页查询输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PubOrderInput.Prefix">
            <summary>
            前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PubOrderInput.TenantId">
            <summary>
            租户Id
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.AddPubOrderInput">
            <summary>
            公用序号增加输入参数
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.DeletePubOrderInput">
            <summary>
            公用序号删除输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.DeletePubOrderInput.Prefix">
            <summary>
            前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.DeletePubOrderInput.TenantId">
            <summary>
            租户Id
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.UpdatePubOrderInput">
            <summary>
            公用序号更新输入参数
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.QueryByIdPubOrderInput">
            <summary>
            公用序号主键查询输入参数
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.PubOrderOutput">
            <summary>
            公用序号输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PubOrderOutput.Prefix">
            <summary>
            前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PubOrderOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PubOrderOutput.TenantId">
            <summary>
            租户Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PubOrderOutput.SN">
            <summary>
            序号
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.PubOrderService">
            <summary>
            公用序号服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.PubOrderService.Page(Admin.NET.Core.Service.PubOrderInput)">
            <summary>
            分页查询公用序号
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.PubOrderService.Add(Admin.NET.Core.Service.AddPubOrderInput)">
            <summary>
            增加公用序号
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.PubOrderService.Delete(Admin.NET.Core.Service.DeletePubOrderInput)">
            <summary>
            删除公用序号
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.PubOrderService.Update">
            <summary>
            更新公用序号(每天零点重置公用序号)
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.PubOrderService.UpdateNew">
            <summary>
            更新公用序号
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.PubOrderService.Get(Admin.NET.Core.Service.QueryByIdPubOrderInput)">
            <summary>
            获取公用序号
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.PubOrderService.List(Admin.NET.Core.Service.PubOrderInput)">
            <summary>
            获取公用序号列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.PubOrderService.GetNewOrder(System.String)">
            <summary>
            获取公用序号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageRegionInput.Pid">
            <summary>
            父节点Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageRegionInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageRegionInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddRegionInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysRegionService">
            <summary>
            系统行政区域服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRegionService.Page(Admin.NET.Core.Service.PageRegionInput)">
            <summary>
            获取行政区域分页列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRegionService.GetList(Admin.NET.Core.Service.RegionInput)">
            <summary>
            获取行政区域列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRegionService.AddRegion(Admin.NET.Core.Service.AddRegionInput)">
            <summary>
            增加行政区域
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRegionService.UpdateRegion(Admin.NET.Core.Service.UpdateRegionInput)">
            <summary>
            更新行政区域
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRegionService.DeleteRegion(Admin.NET.Core.Service.DeleteRegionInput)">
            <summary>
            删除行政区域
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRegionService.Sync">
            <summary>
            同步行政区域
            </summary>
            <returns></returns>
        </member>
        <member name="P:Admin.NET.Core.Service.RoleInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageRoleInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageRoleInput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddRoleInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddRoleInput.MenuIdList">
            <summary>
            菜单Id集合
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.RoleMenuInput">
            <summary>
            授权角色菜单
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.RoleMenuInput.MenuIdList">
            <summary>
            菜单Id集合
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.RoleMenuOutput">
            <summary>
            角色菜单输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.RoleMenuOutput.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.RoleMenuOutput.Title">
            <summary>
            名称
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.RoleOrgInput">
            <summary>
            授权角色机构
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.RoleOrgInput.DataScope">
            <summary>
            数据范围
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.RoleOrgInput.OrgIdList">
            <summary>
            机构Id集合
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.RoleOutput">
            <summary>
            角色列表输出参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.RoleOutput.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.RoleOutput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.RoleOutput.Code">
            <summary>
            编码
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysRoleMenuService">
            <summary>
            系统角色菜单服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleMenuService.GetRoleMenuIdList(System.Collections.Generic.List{System.Int64})">
            <summary>
            根据角色Id集合获取菜单Id集合
            </summary>
            <param name="roleIdList"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleMenuService.GrantRoleMenu(Admin.NET.Core.Service.RoleMenuInput)">
            <summary>
            授权角色菜单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleMenuService.DeleteRoleMenuByMenuIdList(System.Collections.Generic.List{System.Int64})">
            <summary>
            根据菜单Id集合删除角色菜单
            </summary>
            <param name="menuIdList"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleMenuService.DeleteRoleMenuByRoleId(System.Int64)">
            <summary>
            根据角色Id删除角色菜单
            </summary>
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysRoleOrgService">
            <summary>
            系统角色机构服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleOrgService.GrantRoleOrg(Admin.NET.Core.Service.RoleOrgInput)">
            <summary>
            授权角色机构
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleOrgService.GetRoleOrgIdList(System.Collections.Generic.List{System.Int64})">
            <summary>
            根据角色Id集合获取角色机构Id集合
            </summary>
            <param name="roleIdList"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleOrgService.DeleteRoleOrgByOrgIdList(System.Collections.Generic.List{System.Int64})">
            <summary>
            根据机构Id集合删除角色机构
            </summary>
            <param name="orgIdList"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleOrgService.DeleteRoleOrgByRoleId(System.Int64)">
            <summary>
            根据角色Id删除角色机构
            </summary>
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysRoleService">
            <summary>
            系统角色服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleService.Page(Admin.NET.Core.Service.PageRoleInput)">
            <summary>
            获取角色分页列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleService.GetList">
            <summary>
            获取角色列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleService.AddRole(Admin.NET.Core.Service.AddRoleInput)">
            <summary>
            增加角色
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleService.AddRoleByPos(Admin.NET.Core.Service.AddRoleInput)">
            <summary>
            增加角色
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleService.UpdateRoleMenu(Admin.NET.Core.Service.AddRoleInput)">
            <summary>
            更新角色菜单权限
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleService.UpdateRole(Admin.NET.Core.Service.UpdateRoleInput)">
            <summary>
            更新角色
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleService.DeleteRole(Admin.NET.Core.Service.DeleteRoleInput)">
            <summary>
            删除角色
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleService.GrantMenu(Admin.NET.Core.Service.RoleMenuInput)">
            <summary>
            授权角色菜单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleService.GrantDataScope(Admin.NET.Core.Service.RoleOrgInput)">
            <summary>
            授权角色数据范围
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleService.GetOwnMenuList(Admin.NET.Core.Service.RoleInput)">
            <summary>
            根据角色Id获取菜单Id集合
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleService.GetOwnOrgList(Admin.NET.Core.Service.RoleInput)">
            <summary>
            根据角色Id获取机构Id集合
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysRoleService.SetStatus(Admin.NET.Core.Service.RoleInput)">
            <summary>
            设置角色状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysServerService">
            <summary>
            系统服务器监控服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysServerService.GetServerBase">
            <summary>
            获取服务器配置信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysServerService.GetServerUsed">
            <summary>
            获取服务器使用信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysServerService.GetServerDisk">
            <summary>
            获取服务器磁盘信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysServerService.GetAssemblyList">
            <summary>
            获取框架主要程序集
            </summary>
            <returns></returns>
        </member>
        <member name="P:Admin.NET.Core.Service.TenantInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageTenantInput.Name">
            <summary>
            名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageTenantInput.Phone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageTenantInput.SaleUserName">
            <summary>
            销售人
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageTenantInput.AdminName">
            <summary>
            管理员
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageTenantInput.PublicKey">
            <summary>
            公钥
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageTenantInput.PrivateKey">
            <summary>
            私钥
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddTenantInput.Name">
            <summary>
            租户名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddTenantInput.AdminName">
            <summary>
            管理员名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddTenantInput.AppType">
            <summary>
            APP类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TenantUserInput.UserId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TenantOutput.Name">
            <summary>
            租户名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TenantOutput.AdminName">
            <summary>
            管理员
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TenantOutput.Email">
            <summary>
            电子邮箱
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TenantOutput.Phone">
            <summary>
            电话
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TenantOutput.SaleUserName">
            <summary>
            销售人
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TenantOutput.DPUserName">
            <summary>
            电票用户名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TenantOutput.DPPassword">
            <summary>
            电票密码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.TenantOutput.roleId">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysTenantService">
            <summary>
            系统租户管理服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysTenantService.Page(Admin.NET.Core.Service.PageTenantInput)">
            <summary>
            获取租户分页列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysTenantService.GetTenantDbList">
            <summary>
            获取库隔离的租户列表
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysTenantService.AddTenant(Admin.NET.Core.Service.AddTenantInput)">
            <summary>
            增加租户
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysTenantService.SetStatus(Admin.NET.Core.Service.TenantInput)">
            <summary>
            设置租户状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysTenantService.GetTenantDetail(System.Int64)">
            <summary>
            根据租户ID获取租户信息详情
            </summary>
            <param name="tenantId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysTenantService.InitNewTenant(Admin.NET.Core.Service.TenantOutput,System.String)">
            <summary>
            新增租户初始化
            </summary>
            <param name="tenant"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.SysTenantService.DeleteTenant(Admin.NET.Core.Service.DeleteTenantInput)">
            <summary>
            删除租户
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysTenantService.UpdateTenant(Admin.NET.Core.Service.UpdateTenantInput)">
            <summary>
            更新租户
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysTenantService.UpdateTenantRoles">
            <summary>
            更新租户权限
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysTenantService.GrantMenu(Admin.NET.Core.Service.RoleMenuInput)">
            <summary>
            授权租户管理员角色菜单
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysTenantService.GetOwnMenuList(Admin.NET.Core.Service.TenantUserInput)">
            <summary>
            获取租户管理员角色拥有菜单Id集合
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysTenantService.ResetPwd(Admin.NET.Core.Service.TenantUserInput)">
            <summary>
            重置租户管理员密码
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysTenantService.UpdateTenantCache">
            <summary>
            缓存所有租户
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysTenantService.CreateDb(Admin.NET.Core.Service.TenantInput)">
            <summary>
            创建租户数据库
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="P:Admin.NET.Core.Service.UserExtOrgInput.OrgId">
            <summary>
            机构Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.UserExtOrgInput.PosId">
            <summary>
            职位Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.UserExtOrgInput.JobNum">
            <summary>
            工号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.UserExtOrgInput.PosLevel">
            <summary>
            职级
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.UserExtOrgInput.JoinDate">
            <summary>
            入职日期
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.UserInput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageUserInput.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageUserInput.RealName">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageUserInput.Phone">
            <summary>
            手机号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.PageUserInput.OrgId">
            <summary>
            查询时所选机构Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddUserInput.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddUserInput.RealName">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddUserInput.RoleIdList">
            <summary>
            角色集合
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddUserInput.ExtOrgIdList">
            <summary>
            扩展机构集合
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.UpdateDPUserInput.Id">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.UpdateDPUserInput.DPUserName">
            <summary>
            电票平台账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.UpdateDPUserInput.DPPassword">
            <summary>
            电票平台账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.DeleteUserInput.OrgId">
            <summary>
            机构Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.ChangePwdInput.PasswordOld">
            <summary>
            当前密码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.ChangePwdInput.PasswordNew">
            <summary>
            新密码
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.UserRoleInput">
            <summary>
            授权用户角色
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.UserRoleInput.UserId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.UserRoleInput.RoleIdList">
            <summary>
            角色Id集合
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysUserExtOrgService">
            <summary>
            系统用户扩展机构服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserExtOrgService.GetUserExtOrgList(System.Int64)">
            <summary>
            获取用户扩展机构集合
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserExtOrgService.UpdateUserExtOrg(System.Int64,System.Collections.Generic.List{Admin.NET.Core.SysUserExtOrg})">
            <summary>
            更新用户扩展机构
            </summary>
            <param name="userId"></param>
            <param name="extOrgList"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserExtOrgService.DeleteUserExtOrgByOrgIdList(System.Collections.Generic.List{System.Int64})">
            <summary>
            根据机构Id集合删除扩展机构
            </summary>
            <param name="orgIdList"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserExtOrgService.DeleteUserExtOrgByUserId(System.Int64)">
            <summary>
            根据用户Id删除扩展机构
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserExtOrgService.HasUserOrg(System.Int64)">
            <summary>
            根据机构Id判断是否有用户
            </summary>
            <param name="orgId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserExtOrgService.HasUserPos(System.Int64)">
            <summary>
            根据职位Id判断是否有用户
            </summary>
            <param name="posId"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysUserRoleService">
            <summary>
            系统用户角色服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserRoleService.GrantUserRole(Admin.NET.Core.Service.UserRoleInput)">
            <summary>
            授权用户角色
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserRoleService.DeleteUserRoleByRoleId(System.Int64)">
            <summary>
            根据角色Id删除用户角色
            </summary>
            <param name="roleId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserRoleService.DeleteUserRoleByUserId(System.Int64)">
            <summary>
            根据用户Id删除用户角色
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserRoleService.GetUserRoleList(System.Int64)">
            <summary>
            根据用户Id获取角色集合
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserRoleService.GetUserRoleIdList(System.Int64)">
            <summary>
            根据用户Id获取角色Id集合
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysUserService">
            <summary>
            系统用户服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.Page(Admin.NET.Core.Service.PageUserInput)">
            <summary>
            获取用户分页列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.GetUserList">
            <summary>
            获取用户列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.GetUserListMaintained">
            <summary>
            获取用户列表(已维护税务账号)
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.AddUser(Admin.NET.Core.Service.AddUserInput)">
            <summary>
            增加用户
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.UpdateRoleAndExtOrg(Admin.NET.Core.Service.AddUserInput)">
            <summary>
            更新角色和扩展机构
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.UpdateUser(Admin.NET.Core.Service.UpdateUserInput)">
            <summary>
            更新用户
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.UpdateDPUserInfo(System.Collections.Generic.List{Admin.NET.Core.Service.UpdateDPUserInput})">
            <summary>
            更新用户电票信息
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.DeleteUser(Admin.NET.Core.Service.DeleteUserInput)">
            <summary>
            删除用户
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.GetBaseInfo">
            <summary>
            查看用户基本信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.GetListByName(System.String)">
            <summary>
            根据用户名模糊检索
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.UpdateBaseInfo(Admin.NET.Core.SysUser)">
            <summary>
            更新用户基本信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.SetStatus(Admin.NET.Core.Service.UserInput)">
            <summary>
            设置用户状态
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.GrantRole(Admin.NET.Core.Service.UserRoleInput)">
            <summary>
            授权用户角色
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.ChangePwd(Admin.NET.Core.Service.ChangePwdInput)">
            <summary>
            修改用户密码
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.ResetPwd(Admin.NET.Core.Service.ResetPwdUserInput)">
            <summary>
            重置用户密码
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.GetOwnRoleList(System.Int64)">
            <summary>
            获取用户拥有角色集合
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.GetOwnExtOrgList(System.Int64)">
            <summary>
            获取用户扩展机构集合
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysUserService.GetUserInfoByPosOrg(System.Int64,System.Int64)">
            <summary>
            根据职位和部门查询用户
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.GenAuthUrlInput.RedirectUrl">
            <summary>
            RedirectUrl
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.GenAuthUrlInput.Scope">
            <summary>
            Scope
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WechatOAuth2Input.Code">
            <summary>
            Code
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WechatUserLogin.OpenId">
            <summary>
            OpenId
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.SignatureInput.Url">
            <summary>
            Url
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WechatPayTransactionInput.OpenId">
            <summary>
            OpenId
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WechatPayTransactionInput.Total">
            <summary>
            订单金额
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WechatPayTransactionInput.Description">
            <summary>
            商品描述
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WechatPayTransactionInput.Attachment">
            <summary>
            附加数据
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WechatPayTransactionInput.GoodsTag">
            <summary>
            优惠标记
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WechatPayParaInput.PrepayId">
            <summary>
            订单Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WechatPayOutput.OpenId">
            <summary>
            OpenId
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WechatPayOutput.Total">
            <summary>
            订单金额
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WechatPayOutput.Attachment">
            <summary>
            附加数据
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WechatPayOutput.GoodsTag">
            <summary>
            优惠标记
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WechatUserInput.NickName">
            <summary>
            昵称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WechatUserInput.PhoneNumber">
            <summary>
            手机号码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.JsCode2SessionInput.JsCode">
            <summary>
            JsCode
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WxPhoneInput.Code">
            <summary>
            Code
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WxOpenIdLoginInput.OpenId">
            <summary>
            OpenId
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.WxPhoneLoginInput.PhoneNumber">
            <summary>
            电话号码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.SendSubscribeMessageInput.TemplateId">
            <summary>
            订阅模板Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.SendSubscribeMessageInput.ToUserOpenId">
            <summary>
            接收者的OpenId
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.SendSubscribeMessageInput.Data">
            <summary>
            模板内容，格式形如 { "key1": { "value": any }, "key2": { "value": any } }的object
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.SendSubscribeMessageInput.MiniprogramState">
            <summary>
            跳转小程序类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.SendSubscribeMessageInput.Language">
            <summary>
            语言类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.SendSubscribeMessageInput.MiniProgramPagePath">
            <summary>
            点击模板卡片后的跳转页面（仅限本小程序内的页面），支持带参数（示例pages/app/index?foo=bar）
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddSubscribeMessageTemplateInput.TemplateTitleId">
            <summary>
            模板标题Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddSubscribeMessageTemplateInput.KeyworkIdList">
            <summary>
            模板关键词列表,例如 [3,5,4]
            </summary>
        </member>
        <member name="P:Admin.NET.Core.Service.AddSubscribeMessageTemplateInput.SceneDescription">
            <summary>
            服务场景描述，15个字以内
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.SysWechatPayService">
            <summary>
            微信支付服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatPayService.CreateTenpayClient">
            <summary>
            初始化微信支付客户端
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatPayService.GenerateParametersForJsapiPay(Admin.NET.Core.Service.WechatPayParaInput)">
            <summary>
            生成JSAPI调起支付所需参数
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatPayService.CreatePayTransaction(Admin.NET.Core.Service.WechatPayTransactionInput)">
            <summary>
            微信支付统一下单获取Id(商户直连)
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatPayService.CreatePayPartnerTransaction(Admin.NET.Core.Service.WechatPayTransactionInput)">
            <summary>
            微信支付统一下单获取Id(服务商模式)
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatPayService.GetPayInfo(System.String)">
            <summary>
            获取支付订单详情
            </summary>
            <param name="tradeId"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatPayService.PayCallBack">
            <summary>
            微信支付成功回调(商户直连)
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatPayService.PayPartnerCallBack">
            <summary>
            微信支付成功回调(服务商模式)
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysWechatService">
            <summary>
            微信公众号服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatService.GenAuthUrl(Admin.NET.Core.Service.GenAuthUrlInput)">
            <summary>
            生成网页授权Url
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatService.SnsOAuth2(Admin.NET.Core.Service.WechatOAuth2Input)">
            <summary>
            获取微信用户OpenId
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatService.OpenIdLogin(Admin.NET.Core.Service.WechatUserLogin)">
            <summary>
            微信用户登录OpenId
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatService.GenConfigPara(Admin.NET.Core.Service.SignatureInput)">
            <summary>
            获取配置签名参数(wx.config)
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysWechatUserService">
            <summary>
            微信账号服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatUserService.Page(Admin.NET.Core.Service.WechatUserInput)">
            <summary>
            获取微信用户列表
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatUserService.AddWechatUser(Admin.NET.Core.SysWechatUser)">
            <summary>
            增加微信用户
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatUserService.UpdateWechatUser(Admin.NET.Core.SysWechatUser)">
            <summary>
            更新微信用户
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWechatUserService.DeleteWechatUser(Admin.NET.Core.Service.DeleteWechatUserInput)">
            <summary>
            删除微信用户
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Service.SysWxOpenService">
            <summary>
            微信小程序服务
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWxOpenService.GetWxOpenId(Admin.NET.Core.Service.JsCode2SessionInput)">
            <summary>
            获取微信用户OpenId
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWxOpenService.GetWxPhone(Admin.NET.Core.Service.WxPhoneInput)">
            <summary>
            获取微信用户电话号码
            </summary>
            <param name="input"></param>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWxOpenService.WxOpenIdLogin(Admin.NET.Core.Service.WxOpenIdLoginInput)">
            <summary>
            微信小程序登录OpenId
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWxOpenService.GetSubscribeMessageTemplateList">
            <summary>
            获取订阅消息模板列表
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWxOpenService.SendSubscribeMessage(Admin.NET.Core.Service.SendSubscribeMessageInput)">
            <summary>
            发送订阅消息
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWxOpenService.AddSubscribeMessageTemplate(Admin.NET.Core.Service.AddSubscribeMessageTemplateInput)">
            <summary>
            增加订阅消息模板
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.SysWxOpenService.GetCgibinToken">
            <summary>
            获取Access_token
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Service.WechatApiHttpClient">
            <summary>
            微信API客户端
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Service.WechatApiHttpClient.CreateWechatClient">
            <summary>
            微信公众号
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Service.WechatApiHttpClient.CreateWxOpenClient">
            <summary>
            微信小程序
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.BaseService`1">
            <summary>
            实体操作基服务
            </summary>
            <typeparam name="TEntity"></typeparam>
        </member>
        <member name="M:Admin.NET.Core.BaseService`1.GetDetail(System.Int64)">
            <summary>
            获取实体详情
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.BaseService`1.GetList">
            <summary>
            获取实体集合
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.BaseService`1.Add(`0)">
            <summary>
            增加实体
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.BaseService`1.Update(`0)">
            <summary>
            更新实体
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.BaseService`1.Delete(System.Int64)">
            <summary>
            删除实体
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.ExportLogDto">
            <summary>
            导出日志数据
            </summary>
        </member>
        <member name="P:Admin.NET.Core.ExportLogDto.LogName">
            <summary>
            记录器类别名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.ExportLogDto.LogLevel">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:Admin.NET.Core.ExportLogDto.EventId">
            <summary>
            事件Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.ExportLogDto.Message">
            <summary>
            日志消息
            </summary>
        </member>
        <member name="P:Admin.NET.Core.ExportLogDto.Exception">
            <summary>
            异常对象
            </summary>
        </member>
        <member name="P:Admin.NET.Core.ExportLogDto.State">
            <summary>
            当前状态值
            </summary>
        </member>
        <member name="P:Admin.NET.Core.ExportLogDto.LogDateTime">
            <summary>
            日志记录时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.ExportLogDto.ThreadId">
            <summary>
            线程Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.ExportLogDto.TraceId">
            <summary>
            请求跟踪Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.MessageInput.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:Admin.NET.Core.MessageInput.UserIds">
            <summary>
            用户ID列表
            </summary>
        </member>
        <member name="P:Admin.NET.Core.MessageInput.Title">
            <summary>
            消息标题
            </summary>
        </member>
        <member name="P:Admin.NET.Core.MessageInput.MessageType">
            <summary>
            消息类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.MessageInput.Message">
            <summary>
            消息内容
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SysUserOutput">
            <summary>
            系统用户输出
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Account">
            <summary>
            账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.RealName">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.NickName">
            <summary>
            昵称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Avatar">
            <summary>
            头像
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Sex">
            <summary>
            性别-男_1、女_2
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Age">
            <summary>
            年龄
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Birthday">
            <summary>
            出生日期
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Nation">
            <summary>
            民族
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Phone">
            <summary>
            手机号码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.CardType">
            <summary>
            证件类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.IdCardNum">
            <summary>
            身份证号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Address">
            <summary>
            地址
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.CultureLevel">
            <summary>
            文化程度
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.PoliticalOutlook">
            <summary>
            政治面貌
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.College">
            <summary>
            毕业院校
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.OfficePhone">
            <summary>
            办公电话
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.EmergencyContact">
            <summary>
            紧急联系人
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.EmergencyPhone">
            <summary>
            紧急联系人电话
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.EmergencyAddress">
            <summary>
            紧急联系人地址
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Introduction">
            <summary>
            个人简介
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.OrderNo">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.AccountType">
            <summary>
            账号类型
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.OrgId">
            <summary>
            机构Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Orgname">
            <summary>
            机构名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.PosId">
            <summary>
            职位Id
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Posname">
            <summary>
            职位名称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.JobNum">
            <summary>
            工号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.PosLevel">
            <summary>
            职级
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.PosTitle">
            <summary>
            职称
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Expertise">
            <summary>
            擅长领域
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.OfficeZone">
            <summary>
            办公区域
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Office">
            <summary>
            办公室
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.JoinDate">
            <summary>
            入职日期
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.LastLoginIp">
            <summary>
            最新登录Ip
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.LastLoginAddress">
            <summary>
            最新登录地点
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.LastLoginTime">
            <summary>
            最新登录时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.LastLoginDevice">
            <summary>
            最新登录设备
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.Signature">
            <summary>
            电子签名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.EntryTime">
            <summary>
            入职时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.ContractTime">
            <summary>
            合同时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.ContractStatus">
            <summary>
            合同状态
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.DepartTime">
            <summary>
            离职时间
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.DPUserName">
            <summary>
            电票平台账号
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.DPPassword">
            <summary>
            电票平台密码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.ReportToId">
            <summary>
            直属上级ID
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SysUserOutput.ReportToName">
            <summary>
            直属上级名称
            </summary>
        </member>
        <member name="T:Admin.NET.Core.UserManager">
            <summary>
            当前登录用户
            </summary>
        </member>
        <member name="T:Admin.NET.Core.ISqlSugarEntitySeedData`1">
            <summary>
            实体种子数据接口
            </summary>
            <typeparam name="TEntity"></typeparam>
        </member>
        <member name="M:Admin.NET.Core.ISqlSugarEntitySeedData`1.HasData">
            <summary>
            种子数据
            </summary>
            <returns></returns>
        </member>
        <member name="F:Admin.NET.Core.SqlSugarFilter._cache">
            <summary>
            缓存全局查询过滤器（内存缓存）
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarFilter.SetOrgEntityFilter(SqlSugar.SqlSugarScopeProvider)">
            <summary>
            配置用户机构集合过滤器
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarFilter.SetDataScopeFilter(SqlSugar.SqlSugarScopeProvider)">
            <summary>
            配置用户仅本人数据过滤器
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarFilter.SetCustomEntityFilter(SqlSugar.SqlSugarScopeProvider)">
            <summary>
            配置自定义过滤器
            </summary>
        </member>
        <member name="T:Admin.NET.Core.IEntityFilter">
            <summary>
            自定义实体过滤器接口
            </summary>
        </member>
        <member name="M:Admin.NET.Core.IEntityFilter.AddEntityFilter">
            <summary>
            实体过滤器
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.SqlSugarPagedList`1">
            <summary>
            分页泛型集合
            </summary>
            <typeparam name="TEntity"></typeparam>
        </member>
        <member name="P:Admin.NET.Core.SqlSugarPagedList`1.Page">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SqlSugarPagedList`1.PageSize">
            <summary>
            页容量
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SqlSugarPagedList`1.Total">
            <summary>
            总条数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SqlSugarPagedList`1.TotalPages">
            <summary>
            总页数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SqlSugarPagedList`1.Items">
            <summary>
            当前页集合
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SqlSugarPagedList`1.HasPrevPage">
            <summary>
            是否有上一页
            </summary>
        </member>
        <member name="P:Admin.NET.Core.SqlSugarPagedList`1.HasNextPage">
            <summary>
            是否有下一页
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SqlSugarPagedExtensions">
            <summary>
            分页拓展类
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarPagedExtensions.ToPagedList``2(SqlSugar.ISugarQueryable{``0},System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            分页拓展
            </summary>
            <param name="query"><see cref="T:SqlSugar.ISugarQueryable`1"/>对象</param>
            <param name="pageIndex">当前页码，从1开始</param>
            <param name="pageSize">页码容量</param>
            <param name="expression">查询结果 Select 表达式</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarPagedExtensions.ToPagedList``1(SqlSugar.ISugarQueryable{``0},System.Int32,System.Int32)">
            <summary>
            分页拓展
            </summary>
            <param name="query"><see cref="T:SqlSugar.ISugarQueryable`1"/>对象</param>
            <param name="pageIndex">当前页码，从1开始</param>
            <param name="pageSize">页码容量</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarPagedExtensions.ToPagedListAsync``2(SqlSugar.ISugarQueryable{``0},System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            分页拓展
            </summary>
            <param name="query"><see cref="T:SqlSugar.ISugarQueryable`1"/>对象</param>
            <param name="pageIndex">当前页码，从1开始</param>
            <param name="pageSize">页码容量</param>
            <param name="expression">查询结果 Select 表达式</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarPagedExtensions.ToPagedListAsync``1(SqlSugar.ISugarQueryable{``0},System.Int32,System.Int32)">
            <summary>
            分页拓展
            </summary>
            <param name="query"><see cref="T:SqlSugar.ISugarQueryable`1"/>对象</param>
            <param name="pageIndex">当前页码，从1开始</param>
            <param name="pageSize">页码容量</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarPagedExtensions.ToPagedListAsync``1(System.Collections.Generic.IEnumerable{``0},System.Int32,System.Int32)">
            <summary>
            分页拓展
            </summary>
            <param name="list">集合对象</param>
            <param name="pageIndex">当前页码，从1开始</param>
            <param name="pageSize">页码容量</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarPagedExtensions.CreateSqlSugarPagedList``1(System.Collections.Generic.IEnumerable{``0},System.Int32,System.Int32,System.Int32)">
            <summary>
            创建 <see cref="T:Admin.NET.Core.SqlSugarPagedList`1"/> 对象
            </summary>
            <typeparam name="TEntity"></typeparam>
            <param name="items">分页内容的对象集合</param>
            <param name="total">总条数</param>
            <param name="pageIndex">当前页码，从1开始</param>
            <param name="pageSize">页码容量</param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.SqlSugarRepository`1">
            <summary>
            SqlSugar仓储类
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarSetup.AddSqlSugar(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            SqlSugar 上下文初始化
            </summary>
            <param name="services"></param>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarSetup.SetDbConfig(Admin.NET.Core.DbConnectionConfig)">
            <summary>
            配置连接属性
            </summary>
            <param name="config"></param>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarSetup.SetDbAop(SqlSugar.SqlSugarScopeProvider)">
            <summary>
            配置Aop
            </summary>
            <param name="db"></param>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarSetup.SetDbDiffLog(SqlSugar.SqlSugarScopeProvider,Admin.NET.Core.DbConnectionConfig)">
            <summary>
            开启库表差异化日志
            </summary>
            <param name="db"></param>
            <param name="config"></param>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarSetup.InitDatabase(SqlSugar.SqlSugarScope,Admin.NET.Core.DbConnectionConfig)">
            <summary>
            初始化数据库
            </summary>
            <param name="db"></param>
            <param name="config"></param>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarSetup.InitTenantDatabase(SqlSugar.ITenant,Admin.NET.Core.DbConnectionConfig)">
            <summary>
            初始化租户业务数据库
            </summary>
            <param name="iTenant"></param>
            <param name="config"></param>
        </member>
        <member name="T:Admin.NET.Core.SqlSugarUnitOfWork">
            <summary>
            SqlSugar 事务和工作单元
            </summary>
        </member>
        <member name="F:Admin.NET.Core.SqlSugarUnitOfWork._sqlSugarClient">
            <summary>
            SqlSugar 对象
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarUnitOfWork.#ctor(SqlSugar.ISqlSugarClient)">
            <summary>
            构造函数
            </summary>
            <param name="sqlSugarClient"></param>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarUnitOfWork.BeginTransaction(Microsoft.AspNetCore.Mvc.Filters.FilterContext,Furion.DatabaseAccessor.UnitOfWorkAttribute)">
            <summary>
            开启工作单元处理
            </summary>
            <param name="context"></param>
            <param name="unitOfWork"></param>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(Microsoft.AspNetCore.Mvc.Filters.FilterContext,Furion.DatabaseAccessor.UnitOfWorkAttribute)">
            <summary>
            提交工作单元处理
            </summary>
            <param name="resultContext"></param>
            <param name="unitOfWork"></param>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarUnitOfWork.RollbackTransaction(Microsoft.AspNetCore.Mvc.Filters.FilterContext,Furion.DatabaseAccessor.UnitOfWorkAttribute)">
            <summary>
            回滚工作单元处理
            </summary>
            <param name="resultContext"></param>
            <param name="unitOfWork"></param>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="M:Admin.NET.Core.SqlSugarUnitOfWork.OnCompleted(Microsoft.AspNetCore.Mvc.Filters.FilterContext,Microsoft.AspNetCore.Mvc.Filters.FilterContext)">
            <summary>
            执行完毕（无论成功失败）
            </summary>
            <param name="context"></param>
            <param name="resultContext"></param>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="T:Admin.NET.Core.AdminResultProvider">
            <summary>
            全局规范化结果
            </summary>
        </member>
        <member name="M:Admin.NET.Core.AdminResultProvider.OnException(Microsoft.AspNetCore.Mvc.Filters.ExceptionContext,Furion.FriendlyException.ExceptionMetadata)">
            <summary>
            异常返回值
            </summary>
            <param name="context"></param>
            <param name="metadata"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.AdminResultProvider.OnSucceeded(Microsoft.AspNetCore.Mvc.Filters.ActionExecutedContext,System.Object)">
            <summary>
            成功返回值
            </summary>
            <param name="context"></param>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.AdminResultProvider.OnValidateFailed(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,Furion.DataValidation.ValidationMetadata)">
            <summary>
            验证失败返回值
            </summary>
            <param name="context"></param>
            <param name="metadata"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.AdminResultProvider.OnResponseStatusCodes(Microsoft.AspNetCore.Http.HttpContext,System.Int32,Furion.UnifyResult.UnifyResultSettingsOptions)">
            <summary>
            特定状态码返回值
            </summary>
            <param name="context"></param>
            <param name="statusCode"></param>
            <param name="unifyResultSettings"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.AdminResultProvider.RESTfulResult(System.Int32,System.Boolean,System.Object,System.Object)">
            <summary>
            返回 RESTful 风格结果集
            </summary>
            <param name="statusCode"></param>
            <param name="succeeded"></param>
            <param name="data"></param>
            <param name="errors"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.AdminResult`1">
            <summary>
            全局返回结果
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="P:Admin.NET.Core.AdminResult`1.Code">
            <summary>
            状态码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.AdminResult`1.Type">
            <summary>
            类型success、warning、error
            </summary>
        </member>
        <member name="P:Admin.NET.Core.AdminResult`1.Message">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:Admin.NET.Core.AdminResult`1.Result">
            <summary>
            数据
            </summary>
        </member>
        <member name="P:Admin.NET.Core.AdminResult`1.Extras">
            <summary>
            附加数据
            </summary>
        </member>
        <member name="P:Admin.NET.Core.AdminResult`1.Time">
            <summary>
            时间
            </summary>
        </member>
        <member name="T:Admin.NET.Core.BaseIdInput">
            <summary>
            主键Id输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.BaseIdInput.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="T:Admin.NET.Core.BasePageInput">
            <summary>
            全局分页查询输入参数
            </summary>
        </member>
        <member name="P:Admin.NET.Core.BasePageInput.Page">
            <summary>
            当前页码
            </summary>
        </member>
        <member name="P:Admin.NET.Core.BasePageInput.PageSize">
            <summary>
            页码容量
            </summary>
        </member>
        <member name="P:Admin.NET.Core.BasePageInput.Field">
            <summary>
            排序字段
            </summary>
        </member>
        <member name="P:Admin.NET.Core.BasePageInput.Order">
            <summary>
            排序方向
            </summary>
        </member>
        <member name="P:Admin.NET.Core.BasePageInput.DescStr">
            <summary>
            降序排序
            </summary>
        </member>
        <member name="T:Admin.NET.Core.CodeGenUtil">
            <summary>
            代码生成帮助类
            </summary>
        </member>
        <member name="M:Admin.NET.Core.CodeGenUtil.CamelColumnName(System.String,System.String[])">
            <summary>
            转换大驼峰法命名
            </summary>
            <param name="columnName">字段名</param>
            <param name="dbColumnNames">EntityBase 实体属性名称</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.CodeGenUtil.DataTypeToEff(System.String)">
            <summary>
            数据类型转显示类型
            </summary>
            <param name="dataType"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.CommonUtil">
            <summary>
            通用工具类
            </summary>
        </member>
        <member name="M:Admin.NET.Core.CommonUtil.ExecPercent(System.Decimal,System.Decimal)">
            <summary>
            生成百分数
            </summary>
            <param name="PassCount"></param>
            <param name="allCount"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.CommonUtil.GetLocalhost">
            <summary>
            获取服务地址
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.CommonUtil.XmlSerialize``1(``0)">
            <summary>
            XML序列化
            </summary>
            <typeparam name="T"></typeparam>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.CommonUtil.XmlParse(System.String)">
            <summary>
            字符串转XML格式
            </summary>
            <param name="xmlStr"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ComputerUtil.GetComputerInfo">
            <summary>
            内存信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ComputerUtil.GetDiskInfos">
            <summary>
            磁盘信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ComputerUtil.GetIpFromOnline">
            <summary>
            获取外网IP地址
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ComputerUtil.GetRunTime">
            <summary>
            获取系统运行时间
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.MemoryMetrics">
            <summary>
            内存信息
            </summary>
        </member>
        <member name="P:Admin.NET.Core.MemoryMetrics.UsedRam">
            <summary>
            已用内存
            </summary>
        </member>
        <member name="P:Admin.NET.Core.MemoryMetrics.CpuRate">
            <summary>
            CPU使用率%
            </summary>
        </member>
        <member name="P:Admin.NET.Core.MemoryMetrics.TotalRam">
            <summary>
            总内存 GB
            </summary>
        </member>
        <member name="P:Admin.NET.Core.MemoryMetrics.RamRate">
            <summary>
            内存使用率 %
            </summary>
        </member>
        <member name="P:Admin.NET.Core.MemoryMetrics.FreeRam">
            <summary>
            空闲内存
            </summary>
        </member>
        <member name="T:Admin.NET.Core.DiskInfo">
            <summary>
            磁盘信息
            </summary>
        </member>
        <member name="P:Admin.NET.Core.DiskInfo.DiskName">
            <summary>
            磁盘名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.DiskInfo.TypeName">
            <summary>
            类型名
            </summary>
        </member>
        <member name="P:Admin.NET.Core.DiskInfo.TotalFree">
            <summary>
            总剩余
            </summary>
        </member>
        <member name="P:Admin.NET.Core.DiskInfo.TotalSize">
            <summary>
            总量
            </summary>
        </member>
        <member name="P:Admin.NET.Core.DiskInfo.Used">
            <summary>
            已使用
            </summary>
        </member>
        <member name="P:Admin.NET.Core.DiskInfo.AvailableFreeSpace">
            <summary>
            可使用
            </summary>
        </member>
        <member name="P:Admin.NET.Core.DiskInfo.AvailablePercent">
            <summary>
            使用百分比
            </summary>
        </member>
        <member name="M:Admin.NET.Core.MemoryMetricsClient.GetWindowsMetrics">
            <summary>
            windows系统获取内存信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.MemoryMetricsClient.GetUnixMetrics">
            <summary>
            Unix系统获取
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ShellUtil.Bash(System.String)">
            <summary>
            linux 系统命令
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.ShellUtil.Cmd(System.String,System.String)">
            <summary>
            windows系统命令
            </summary>
            <param name="fileName"></param>
            <param name="args"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.CryptogramUtil.Encrypt(System.String)">
            <summary>
            加密
            </summary>
            <param name="plainText"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.CryptogramUtil.Decrypt(System.String)">
            <summary>
            解密
            </summary>
            <param name="cipherText"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.CryptogramUtil.SM2Encrypt(System.String)">
            <summary>
            SM2加密
            </summary>
            <param name="plainText"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.CryptogramUtil.SM2Decrypt(System.String)">
            <summary>
            SM2解密
            </summary>
            <param name="cipherText"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.CryptogramUtil.SM4EncryptECB(System.String)">
            <summary>
            SM4加密（ECB）
            </summary>
            <param name="plainText"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.CryptogramUtil.SM4DecryptECB(System.String)">
            <summary>
            SM4解密（ECB）
            </summary>
            <param name="cipherText"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.CryptogramUtil.SM4EncryptCBC(System.String)">
            <summary>
            SM4加密（CBC）
            </summary>
            <param name="plainText"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.CryptogramUtil.SM4DecryptCBC(System.String)">
            <summary>
            SM4解密（CBC）
            </summary>
            <param name="cipherText"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.DateTimeUtil.GetBeginTime(System.Nullable{System.DateTime},System.Int32)">
            <summary>
            获取开始时间
            </summary>
            <param name="dateTime"></param>
            <param name="days"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.DateTimeUtil.ToLocalTimeDateBySeconds(System.Int64)">
            <summary>
             时间戳转本地时间-时间戳精确到秒
            </summary>
        </member>
        <member name="M:Admin.NET.Core.DateTimeUtil.ToUnixTimestampBySeconds(System.DateTime)">
            <summary>
             时间转时间戳Unix-时间戳精确到秒
            </summary>
        </member>
        <member name="M:Admin.NET.Core.DateTimeUtil.ToLocalTimeDateByMilliseconds(System.Int64)">
            <summary>
             时间戳转本地时间-时间戳精确到毫秒
            </summary>
        </member>
        <member name="M:Admin.NET.Core.DateTimeUtil.ToUnixTimestampByMilliseconds(System.DateTime)">
            <summary>
             时间转时间戳Unix-时间戳精确到毫秒
            </summary>
        </member>
        <member name="M:Admin.NET.Core.DateTimeUtil.FormatTime(System.Int64)">
            <summary>
            毫秒转天时分秒
            </summary>
            <param name="ms"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.DateTimeUtil.GetUnixTimeStamp(System.DateTime)">
            <summary>
            获取unix时间戳
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.DateTimeUtil.GetDayMinDate(System.DateTime)">
            <summary>
            获取日期天的最小时间
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.DateTimeUtil.GetDayMaxDate(System.DateTime)">
            <summary>
            获取日期天的最大时间
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.DateTimeUtil.FormatDateTime(System.Nullable{System.DateTime})">
            <summary>
            获取日期天的最大时间
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.DateTimeUtil.GetTodayTimeList(System.DateTime)">
            <summary>
            获取今天日期范围00:00:00 - 23:59:59
            </summary>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.DateTimeUtil.GetWeekByDate(System.DateTime)">
            <summary>
            获取星期几
            </summary>
            <param name="dt"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.DateTimeUtil.GetWeekNumInMonth(System.DateTime)">
            <summary>
            获取这个月的第几周
            </summary>
            <param name="daytime"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Util.SnowflakeGenerator">
            <summary>
            雪花ID生成器
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Util.SnowflakeGenerator.#ctor(System.Nullable{System.Int64},System.Nullable{System.Int64})">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Util.SnowflakeGenerator.NextId">
            <summary>
            生成下一个ID
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Util.SnowflakeGenerator.ParseId(System.Int64)">
            <summary>
            解析ID
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Util.SnowflakeGenerator.GenerateWorkerId">
            <summary>
            自动生成WorkerId
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Util.SnowflakeGenerator.GenerateDatacenterId">
            <summary>
            自动生成DatacenterId
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Util.SnowflakeGenerator.GetTimestamp">
            <summary>
            获取当前时间戳
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Util.SnowflakeGenerator.WaitNextMillis(System.Int64)">
            <summary>
            等待下一个毫秒
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Util.SnowflakeGenerator.RecordTimestamp(System.Int64)">
            <summary>
            记录生成的时间戳
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Util.SnowflakeGenerator.HandleSignificantClockBackward">
            <summary>
            处理严重的时钟回拨
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Util.SnowflakeGenerator.GetRemainingYears">
            <summary>
            获取剩余可用年限
            </summary>
        </member>
        <member name="T:Admin.NET.Core.Util.SnowflakeGeneratorFactory">
            <summary>
            雪花ID生成器工厂
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Util.SnowflakeGeneratorFactory.GetGenerator(System.String,System.Nullable{System.Int64},System.Nullable{System.Int64})">
            <summary>
            获取或创建雪花ID生成器实例
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Util.Npoi.NpoiHelper.ExportExcel``1(System.Collections.Generic.List{``0},System.Collections.Generic.Dictionary{System.String,System.String},System.String)">
            <summary>
            通过文件流导出Excel文件
            </summary>
            <typeparam name="T">数据类型</typeparam>
            <param name="entities">数据实体</param>
            <param name="dicColumns">列对应关系,如Name->姓名</param>
            <param name="title">标题</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Util.Npoi.NpoiHelper.NpoiExcel(System.Data.DataTable,System.String,System.String[],System.String@,System.String@)">
            <summary>
            通过下载导出Excel文件
            </summary>
            <param name="dt">数据表</param>
            <param name="title">文件名称</param>
            <param name="headerarr">表头</param>
            <param name="resultMsg">错误信息</param>
            <param name="excelFilePath">文件路径</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Util.Npoi.ToDataTable.toDataTable(System.Object,System.String)">
            <summary>
            转DataTable
            </summary>
            <param name="obj"></param>
            <param name="_tName"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.JsonSerializerProvider">
            <summary>
            Newtonsoft.Json 实现
            </summary>
        </member>
        <member name="M:Admin.NET.Core.JsonSerializerProvider.Serialize(System.Object,System.Object)">
            <summary>
            序列化对象
            </summary>
            <param name="value"></param>
            <param name="jsonSerializerOptions"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.JsonSerializerProvider.Deserialize``1(System.String,System.Object)">
            <summary>
            反序列化字符串
            </summary>
            <typeparam name="T"></typeparam>
            <param name="json"></param>
            <param name="jsonSerializerOptions"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.JsonSerializerProvider.Deserialize(System.String,System.Type,System.Object)">
            <summary>
            反序列化字符串
            </summary>
            <param name="json"></param>
            <param name="returnType"></param>
            <param name="jsonSerializerOptions"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.JsonSerializerProvider.GetSerializerOptions">
            <summary>
            返回读取全局配置的 JSON 选项
            </summary>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.Auth">
            <summary>
            Authentication/Authorization
            </summary>
        </member>
        <member name="M:Admin.NET.Core.Auth.#ctor(Qiniu.Util.Mac,Admin.NET.Core.AuthOptions)">
            <summary>
            一般初始化
            </summary>
            <param name="mac">账号(密钥)</param>
            <param name="authOptions">认证时的配置</param>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateManageToken(System.String,System.Byte[])">
            <summary>
            生成管理凭证
            有关管理凭证请参阅
            http://developer.qiniu.com/article/developer/security/access-token.html
            </summary>
            <param name="url">请求的URL</param>
            <param name="body">请求的主体内容</param>
            <returns>生成的管理凭证</returns>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateManageToken(System.String)">
            <summary>
            生成管理凭证-不包含body
            </summary>
            <param name="url">请求的URL</param>
            <returns>生成的管理凭证</returns>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateUploadToken(System.String)">
            <summary>
            生成上传凭证
            </summary>
            <param name="jsonStr">上传策略对应的JSON字符串</param>
            <returns>生成的上传凭证</returns>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateDownloadToken(System.String)">
            <summary>
            生成下载凭证
            </summary>
            <param name="url">原始链接</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateStreamPublishToken(System.String)">
            <summary>
            生成推流地址使用的凭证
            </summary>
            <param name="path"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateStreamManageToken(System.String)">
            <summary>
            生成流管理凭证
            </summary>
            <param name="data"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateManageToken(Qiniu.Util.Mac,System.String,System.Byte[])">
            <summary>
            生成管理凭证
            有关管理凭证请参阅
            http://developer.qiniu.com/article/developer/security/access-token.html
            </summary>
            <param name="mac">账号(密钥)</param>
            <param name="url">访问的URL</param>
            <param name="body">请求的body</param>
            <returns>生成的管理凭证</returns>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateManageToken(Qiniu.Util.Mac,System.String)">
            <summary>
            生成管理凭证-不包含body
            </summary>
            <param name="mac">账号(密钥)</param>
            <param name="url">请求的URL</param>
            <returns>生成的管理凭证</returns>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateManageTokenV2(System.String,System.String,System.Collections.Specialized.StringDictionary,System.String)">
            <summary>
            生成 Qiniu 管理凭证
            </summary>
            <param name="method">请求的方法</param>
            <param name="url">请求的 URL</param>
            <param name="headers">请求的 Headers</param>
            <param name="body">请求体（可选），要求 UTF-8 编码</param>
            <returns>生成的管理凭证</returns>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateManageTokenV2(System.String,System.String,System.String)">
            <summary>
            生成 Qiniu 管理凭证-不包含 header
            </summary>
            <param name="method">请求的方法</param>
            <param name="url">请求的 URL</param>
            <param name="body">请求体（可选），要求 UTF-8 编码</param>
            <returns>生成的管理凭证</returns>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateManageTokenV2(Qiniu.Util.Mac,System.String,System.String,System.Collections.Specialized.StringDictionary,System.String)">
            <summary>
            生成 Qiniu 管理凭证-使用外部 mac
            </summary>
            <param name="mac">外部传入的 mac</param>
            <param name="method">请求的方法</param>
            <param name="url">请求的 URL</param>
            <param name="headers">请求的 Headers</param>
            <param name="body">请求体（可选），要求 UTF-8 编码</param>
            <returns>生成的管理凭证</returns>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateUploadToken(Qiniu.Util.Mac,System.String)">
            <summary>
            生成上传凭证
            </summary>
            <param name="mac">账号(密钥)</param>
            <param name="jsonBody">上传策略JSON串</param>
            <returns>生成的上传凭证</returns>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateDownloadToken(Qiniu.Util.Mac,System.String)">
            <summary>
            生成下载凭证
            </summary>
            <param name="mac">账号(密钥)</param>
            <param name="url">原始链接</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateStreamPublishToken(Qiniu.Util.Mac,System.String)">
            <summary>
            生成推流地址使用的凭证
            </summary>
            <param name="mac">账号(密钥)</param>
            <param name="path">URL路径</param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.Auth.CreateStreamManageToken(Qiniu.Util.Mac,System.String)">
            <summary>
            生成流管理凭证
            </summary>
            <param name="mac">账号(密钥)</param>
            <param name="data">待签数据</param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.FileUtil">
            <summary>
            七牛云文件工具类
            </summary>
        </member>
        <member name="M:Admin.NET.Core.FileUtil.UploadPicToQiniu(Microsoft.AspNetCore.Http.IFormFileCollection,System.String,System.String)">
            <summary>
            图片上传到七牛云
            </summary>
        </member>
        <member name="T:Admin.NET.Core.ReflectionUtil">
            <summary>
            反射工具类
            </summary>
        </member>
        <member name="M:Admin.NET.Core.ReflectionUtil.GetDescriptionValue``1(System.Reflection.FieldInfo)">
            <summary>
            获取字段特性
            </summary>
            <param name="field"></param>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.RegularValidate">
            <summary>
            正则校验
            </summary>
        </member>
        <member name="M:Admin.NET.Core.RegularValidate.ValidatePassword(System.String)">
            <summary>
            验证密码规则
            </summary>
            <param name="password"></param>
            <returns></returns>
        </member>
        <member name="T:Admin.NET.Core.SM2Util">
            <summary>
            SM2工具类
            </summary>
        </member>
        <member name="M:Admin.NET.Core.SM2Util.Encrypt(System.String,System.String)">
            <summary>
            加密
            </summary>
            <param name="publicKey_string"></param>
            <param name="data_string"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.SM2Util.Decrypt(System.String,System.String)">
            <summary>
            解密
            </summary>
            <param name="privateKey_string"></param>
            <param name="encryptedData_string"></param>
            <returns></returns>
        </member>
        <member name="M:Admin.NET.Core.SM2Util.加密(System.String)">
            <summary>
            SM2加密
            </summary>
            <param name="plainText">明文</param>
            <returns>密文</returns>
        </member>
        <member name="M:Admin.NET.Core.SM2Util.解密(System.String)">
            <summary>
            SM2解密
            </summary>
            <param name="cipherText">密文</param>
            <returns>明文</returns>
        </member>
        <member name="M:Admin.NET.Core.SupportClass.URShift(System.Int32,System.Int32)">
            <summary>
            Performs an unsigned bitwise right shift with the specified number
            </summary>
            <param name="number">Number to operate on</param>
            <param name="bits">Ammount of bits to shift</param>
            <returns>The resulting number from the shift operation</returns>
        </member>
        <member name="M:Admin.NET.Core.SupportClass.URShift(System.Int32,System.Int64)">
            <summary>
            Performs an unsigned bitwise right shift with the specified number
            </summary>
            <param name="number">Number to operate on</param>
            <param name="bits">Ammount of bits to shift</param>
            <returns>The resulting number from the shift operation</returns>
        </member>
        <member name="M:Admin.NET.Core.SupportClass.URShift(System.Int64,System.Int32)">
            <summary>
            Performs an unsigned bitwise right shift with the specified number
            </summary>
            <param name="number">Number to operate on</param>
            <param name="bits">Ammount of bits to shift</param>
            <returns>The resulting number from the shift operation</returns>
        </member>
        <member name="M:Admin.NET.Core.SupportClass.URShift(System.Int64,System.Int64)">
            <summary>
            Performs an unsigned bitwise right shift with the specified number
            </summary>
            <param name="number">Number to operate on</param>
            <param name="bits">Ammount of bits to shift</param>
            <returns>The resulting number from the shift operation</returns>
        </member>
        <member name="T:Admin.NET.Core.SM3Util">
            <summary>
            SM3工具类
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SM4Util">
            <summary>
            SM4工具类
            </summary>
        </member>
        <member name="T:Admin.NET.Core.SMUtil">
            <summary>
            SM工具类
            </summary>
        </member>
        <member name="T:Admin.NET.Application.Entity.PubOrder">
            <summary>
            公用序号
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.PubOrder.Prefix">
            <summary>
            前缀
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.PubOrder.Remark">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.PubOrder.TenantId">
            <summary>
            租户Id
            </summary>
        </member>
        <member name="P:Admin.NET.Application.Entity.PubOrder.SN">
            <summary>
            序号
            </summary>
        </member>
    </members>
</doc>
