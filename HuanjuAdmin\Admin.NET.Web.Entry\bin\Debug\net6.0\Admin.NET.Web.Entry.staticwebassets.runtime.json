{"ContentRoots": ["D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\"], "Root": {"Children": {"CodeGen": {"Children": {"ImportTemplate": {"Children": {"Web": {"Children": {"src": {"Children": {"api": {"Children": {"main": {"Children": {"importTemplate.ts": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "CodeGen/ImportTemplate/Web/src/api/main/importTemplate.ts"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "views": {"Children": {"main": {"Children": {"importTemplate": {"Children": {"component": {"Children": {"editDialog.vue": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "CodeGen/ImportTemplate/Web/src/views/main/importTemplate/component/editDialog.vue"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "index.vue": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "CodeGen/ImportTemplate/Web/src/views/main/importTemplate/index.vue"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "images": {"Children": {"logo.png": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "images/logo.png"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Template": {"Children": {"data.data.ts.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Template/data.data.ts.vm"}, "Patterns": null}, "dataModal.vue.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Template/dataModal.vue.vm"}, "Patterns": null}, "Dto.cs.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Template/Dto.cs.vm"}, "Patterns": null}, "editDialog.vue.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Template/editDialog.vue.vm"}, "Patterns": null}, "Entity.cs.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Template/Entity.cs.vm"}, "Patterns": null}, "index.vue.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Template/index.vue.vm"}, "Patterns": null}, "Input.cs.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Template/Input.cs.vm"}, "Patterns": null}, "Manage.js.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Template/Manage.js.vm"}, "Patterns": null}, "Output.cs.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Template/Output.cs.vm"}, "Patterns": null}, "Service.cs.vm": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "Template/Service.cs.vm"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "upload": {"Children": {"Address": {"Children": {"13750818896581.jpg": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "upload/Address/13750818896581.jpg"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": [{"ContentRootIndex": 0, "Pattern": "**", "Depth": 0}]}}