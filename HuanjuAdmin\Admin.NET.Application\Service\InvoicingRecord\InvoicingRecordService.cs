﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;

namespace Admin.NET.Application;
/// <summary>
/// 开票记录服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class InvoicingRecordService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<InvoicingRecord> _rep;
    public InvoicingRecordService(SqlSugarRepository<InvoicingRecord> rep)
    {
        _rep = rep;
    }

    /// <summary>
    /// 分页查询开票记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<InvoicingRecordOutput>> Page(InvoicingRecordInput input)
    {
        var query = _rep.AsQueryable()
                    .WhereIF(input.Id > 0, u => u.Id == input.Id)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.DocumentNumber), u => u.DocumentNumber.Contains(input.DocumentNumber.Trim()))
                    .WhereIF(input.ReceiptID > 0, u => u.ReceiptID == input.ReceiptID)
                    .WhereIF(input.Status > 0, u => u.Status == input.Status)

                    .Select<InvoicingRecordOutput>()
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加开票记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddInvoicingRecordInput input)
    {
        var entity = input.Adapt<InvoicingRecord>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除开票记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteInvoicingRecordInput input)
    {
        var entity = input.Adapt<InvoicingRecord>();
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新开票记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateInvoicingRecordInput input)
    {
        var entity = input.Adapt<InvoicingRecord>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取开票记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<InvoicingRecord> Get([FromQuery] QueryByIdInvoicingRecordInput input)
    {
        return await _rep.GetByIdAsync(input.Id);  
    }

    /// <summary>
    /// 获取开票记录列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<InvoicingRecordOutput>> List([FromQuery] InvoicingRecordInput input)
    {
        return await _rep.AsQueryable().Select<InvoicingRecordOutput>().ToListAsync();
    }





}

