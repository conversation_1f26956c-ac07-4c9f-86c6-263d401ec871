/**
 * axios 工具类
 */
import globalAxios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../api-services';
import { BaseAPI, BASE_PATH } from '../api-services/base';
import { ElMessage } from 'element-plus';
import { Local, Session } from './storage';
import _ from 'lodash';

// 接口服务器配置
export const serveConfig = new Configuration({
  basePath: import.meta.env.VITE_API_URL,
});

// token 键定义
export const accessTokenKey = 'access-token';
export const refreshAccessTokenKey = `x-${accessTokenKey}`;

// 获取 token
export const getToken = () => {
  return Local.get(accessTokenKey);
};

// 清除 token 和缓存
export const clearTokens = () => {
  Local.remove(accessTokenKey);
  Local.remove(refreshAccessTokenKey);
  Session.clear();
  localStorage.clear();
  sessionStorage.clear();
};

// 刷新浏览器
export const clearAccessTokens = () => {
  clearTokens();
  window.location.reload();
};

// axios 默认实例
export const axiosInstance: AxiosInstance = globalAxios;

// 创建请求去重 Map
const pendingRequests = new Map();
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: unknown) => void;
  reject: (reason?: any) => void;
}> = [];

// 生成请求的唯一 key
const generateRequestKey = (config: AxiosRequestConfig): string => {
  const { url, method, params, data } = config;
  return [url, method, JSON.stringify(params), JSON.stringify(data)].join('&');
};

// 处理请求队列
const processQueue = (error: any = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve();
    }
  });
  failedQueue = [];
};

// 刷新 token
let refreshTokenPromise: Promise<any> | null = null;
const refreshToken = async () => {
  if (!refreshTokenPromise) {
    // 实现刷新 token 的逻辑
    refreshTokenPromise = new Promise((resolve, reject) => {
      // 这里调用你的刷新 token API
      getAPI(SysAuthApi).apiSysAuthRefreshPost()
        .then(response => {
          refreshTokenPromise = null;
          resolve(response.data.result?.accessToken);
        })
        .catch(error => {
          refreshTokenPromise = null;
          reject(error);
        });
    });
  }
  return refreshTokenPromise;
};

// axios 请求拦截
axiosInstance.interceptors.request.use(
  async (config) => {
    // 生成请求 key
    const requestKey = generateRequestKey(config);

    // // 检查是否存在相同的请求
    // if (pendingRequests.has(requestKey)) {
    //   const controller = new AbortController();
    //   controller.abort();
    //   config.signal = controller.signal;
    // }

    // 记录当前请求
    pendingRequests.set(requestKey, true);

    // 获取本地的 token
    const accessToken = Local.get(accessTokenKey);
    if (accessToken) {
      try {
        // 解析 token
        const jwt: any = decryptJWT(accessToken);
        const exp = getJWTDate(jwt.exp as number);

        // token 即将过期时，先刷新 token
        if (new Date() >= exp) {
          if (!isRefreshing) {
            isRefreshing = true;
            try {
              const newToken = await refreshToken();
              Local.set(accessTokenKey, newToken);
              config.headers!['Authorization'] = `Bearer ${newToken}`;
            } catch (error) {
              clearTokens();
              return Promise.reject(error);
            } finally {
              isRefreshing = false;
            }
          } else {
            // 其他请求等待 token 刷新完成
            return new Promise((resolve, reject) => {
              failedQueue.push({ resolve, reject });
            }).then(() => {
              config.headers!['Authorization'] = `Bearer ${Local.get(accessTokenKey)}`;
              return config;
            });
          }
        } else {
          config.headers!['Authorization'] = `Bearer ${accessToken}`;
        }
      } catch (error) {
        console.error('Token processing error:', error);
        clearTokens();
      }
    }

    return config;
  },
  (error) => {
    // 移除请求记录
    if (error.config) {
      const requestKey = generateRequestKey(error.config);
      pendingRequests.delete(requestKey);
    }
    return Promise.reject(error);
  }
);

// axios 响应拦截
axiosInstance.interceptors.response.use(
  (response) => {
    // 移除请求记录
    const requestKey = generateRequestKey(response.config);
    pendingRequests.delete(requestKey);

    // 获取状态码和返回数据
    const status = response.status;
    const serve = response.data;

    // 处理 401
    if (status === 401) {
      clearAccessTokens();
      return Promise.reject(new Error('Unauthorized'));
    }

    // 处理未规范化的错误
    if (status >= 400) {
      return Promise.reject(new Error(response.statusText || 'Request Error.'));
    }

    // 处理规范化结果错误
    if (serve && serve.hasOwnProperty('errors') && serve.errors) {
      return Promise.reject(new Error(JSON.stringify(serve.errors || 'Request Error.')));
    }

    // 读取响应报文头 token 信息
    const newAccessToken = response.headers[accessTokenKey];
    const newRefreshToken = response.headers[refreshAccessTokenKey];

    // 处理 token 更新
    if (newAccessToken === 'invalid_token') {
      clearAccessTokens();
      return Promise.reject(new Error('Invalid token'));
    } else if (newRefreshToken && newAccessToken && newAccessToken !== 'invalid_token') {
      Local.set(accessTokenKey, newAccessToken);
      Local.set(refreshAccessTokenKey, newRefreshToken);
    }

    // 处理业务错误
    if (serve.code === 401) {
      clearAccessTokens();
      return Promise.reject(new Error('Unauthorized'));
    } else if (serve.code === undefined) {
      return Promise.resolve(response);
    } else if (serve.code !== 200) {
      const message = typeof serve.message === 'object' ? JSON.stringify(serve.message) : serve.message;
      ElMessage.error(message);
      return Promise.reject(new Error(message));
    }

    return response;
  },
  async (error) => {
    // 移除请求记录
    if (error.config) {
      const requestKey = generateRequestKey(error.config);
      pendingRequests.delete(requestKey);
    }

    // 处理 401 错误
    if (error.response?.status === 401) {
      if (!error.config._retry) {
        error.config._retry = true;
        try {
          const newToken = await refreshToken();
          Local.set(accessTokenKey, newToken);
          error.config.headers['Authorization'] = `Bearer ${newToken}`;
          return axiosInstance(error.config);
        } catch (refreshError) {
          clearAccessTokens();
          return Promise.reject(refreshError);
        }
      } else {
        clearAccessTokens();
      }
    }

    // 错误提示
    ElMessage.error(error.message || 'Request failed');
    return Promise.reject(error);
  }
);

/**
 * 包装 Promise 并返回 [Error, any]
 */
export function feature<T, U = Error>(promise: Promise<T>, errorExt?: object): Promise<[U, undefined] | [null, T]> {
  return promise
    .then<[null, T]>((data: T) => [null, data])
    .catch<[U, undefined]>((err: U) => {
      if (errorExt) {
        const parsedError = Object.assign({}, err, errorExt);
        return [parsedError, undefined];
      }
      return [err, undefined];
    });
}

/**
 * 获取/创建服务 API 实例
 */
export function getAPI<T extends BaseAPI>(
  apiType: new (configuration?: Configuration, basePath?: string, axiosInstance?: AxiosInstance) => T,
  configuration: Configuration = serveConfig,
  basePath: string = BASE_PATH,
  axiosObject: AxiosInstance = axiosInstance
) {
  return new apiType(configuration, basePath, axiosObject);
}

/**
 * 解密 JWT token
 */
export function decryptJWT(token: string): any {
  token = token.replace(/_/g, '/').replace(/-/g, '+');
  var json = decodeURIComponent(escape(window.atob(token.split('.')[1])));
  return JSON.parse(json);
}

/**
 * 将 JWT 时间戳转换成 Date
 */
export function getJWTDate(timestamp: number): Date {
  return new Date(timestamp * 1000);
}