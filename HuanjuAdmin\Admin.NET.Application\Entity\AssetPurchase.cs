﻿using System;
using SqlSugar;
using System.ComponentModel;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 固资采购表
    /// </summary>
    [SugarTable("AssetPurchase","固资采购表")]
    [Tenant("1300000000001")]
    public class AssetPurchase  : EntityTenant
    {
        /// <summary>
        /// 固资
        /// </summary>
        [SugarColumn(ColumnDescription = "固资")]
        public long AssetId { get; set; }
        /// <summary>
        /// 固资
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(AssetId))]
        public AssetInventory AssetInventory { get; set; }
        /// <summary>
        /// 单价
        /// </summary>
        [SugarColumn(ColumnDescription = "单价")]
        public decimal? UnitPrice { get; set; }
        /// <summary>
        /// 采购数量
        /// </summary>
        [SugarColumn(ColumnDescription = "采购数量")]
        public int PurchaseCount { get; set; }
        /// <summary>
        /// 入库数量
        /// </summary>
        [SugarColumn(ColumnDescription = "入库数量")]
        public int InCount { get; set; }
        /// <summary>
        /// 总价
        /// </summary>
        [SugarColumn(ColumnDescription = "总价")]
        public decimal? TotalPrice { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        [SugarColumn(ColumnDescription = "供应商")]
        public long? SupplierId { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(SupplierId))]
        public PubSupplier PubSupplier { get; set; }
        /// <summary>
        /// 采购时间
        /// </summary>
        [SugarColumn(ColumnDescription = "采购时间")]
        public DateTime? PurchaseTime { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注")]
        public string? Remark { get; set; }
    }
}