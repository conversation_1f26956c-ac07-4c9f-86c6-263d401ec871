namespace Admin.NET.Application;

/// <summary>
/// 入库类型枚举
/// </summary>
[Description("入库类型枚举")]
public enum RcvTypeEnum
{
    /// <summary>
    /// 采购入库
    /// </summary>
    [Description("采购入库")]
    CaiGou = 0,

    /// <summary>
    /// 售后入库
    /// </summary>
    [Description("售后入库")]
    ShouHou = 1,

    /// <summary>
    /// 退货入库
    /// </summary>
    [Description("退货入库")]
    TuiHuo = 2,

    /// <summary>
    /// 换货入库
    /// </summary>
    [Description("换货入库")]
    HuanHuo = 3,

    /// <summary>
    /// 其它入库
    /// </summary>
    [Description("其它入库")]
    Qita = 4,
}