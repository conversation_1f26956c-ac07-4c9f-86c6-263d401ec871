﻿using System;

namespace Admin.NET.Application;

/// <summary>
/// 商品出库明细输出参数
/// </summary>
public class WarehouseoutMXDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }


    /// <summary>
    /// 商品名称
    /// </summary>
    public string? Tradename { get; set; }

    /// <summary>
    /// 商品条码
    /// </summary>
    public string? Barcode { get; set; }

    /// <summary>
    /// 商品编码
    /// </summary>
    public string? Productcode { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Specifications { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 是否缺货
    /// </summary>
    public int? Vacancy { get; set; }


    /// <summary>
    /// 商品Id
    /// </summary>
    public long GoodsId { get; set; }

    /// <summary>
    /// 出库数量
    /// </summary>
    public int OutCount { get; set; }

    /// <summary>
    /// 实际出库数量
    /// </summary>
    public int TrueOutCount { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// 出库单ID
    /// </summary>
    public long OutId { get; set; }

}
