﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Application.Service.OutboundRecord.Dto;
using Furion.FriendlyException;
using SqlSugar;
using System;
using System.Linq;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.ChannelsECWarehouseGetResponse.Types;

namespace Admin.NET.Application;
/// <summary>
/// 商品出库明细服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WarehouseoutMXService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarClient _db;
    private readonly SqlSugarRepository<WarehouseStore> _resstore;
    private readonly SqlSugarRepository<Warehousegoods> _repgoods;
    private readonly SqlSugarRepository<WarehouseoutMX> _rep;
    private readonly SqlSugarRepository<WarehouseGoodsUnit> _repUnit;
    private readonly SqlSugarRepository<OutInBoundRecord> _repOutInBound;
    UserManager _userManager;
    public WarehouseoutMXService(SqlSugarRepository<WarehouseoutMX> rep, UserManager userManager, SqlSugarRepository<WarehouseGoodsUnit> repUnit, SqlSugarRepository<Warehousegoods> repgoods, ISqlSugarClient db, SqlSugarRepository<WarehouseStore> resstore, SqlSugarRepository<OutInBoundRecord> repOutInBound)
    {
        _repgoods = repgoods;
        _rep = rep;
        _userManager = userManager;
        _repUnit = repUnit;
        this._db = db;
        _resstore = resstore;
        _repOutInBound = repOutInBound;
    }
    /// <summary>
    /// 分页查询商品出库明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WarehouseoutMXOutput>> Page(WarehouseoutMXInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(u => u.TenantId == _userManager.TenantId && u.IsDelete == false)
                    .WhereIF(input.OutId > 0, u => u.OutId == input.OutId)
                    .WhereIF(input.Id > 0, u => u.Id == input.Id)
                    .LeftJoin<Warehouseout>((u, o) => u.OutId == o.Id)
                    .LeftJoin<WarehouseStore>((u, o, s) => o.WarehouseId == s.WarehouseId && u.goodsId == s.TradeID)
                    .Select((u, o, s) => new WarehouseoutMXOutput
                    {
                        Productcode = u.Warehousegoods.Code,
                        Brand = u.Warehousegoods.Brand,
                        Barcode = u.Warehousegoods.barcode,
                        Specifications = u.Warehousegoods.Specs,
                        UnitName = u.WarehouseGoodsUnit.Name,
                        Notes = u.Notes,
                        Deliverytime = u.Deliverytime,
                        Id = u.Id,
                        OutCount = u.OutCount,
                        OutId = u.OutId,
                        Tradename = u.Warehousegoods.Name,
                        TrueOutCount = (int)u.TrueOutCount,
                        GoodsId = u.goodsId,
                        GoodProduct = u.GoodProduct,
                        Unit = u.Unit,
                        WarehouseId = u.warehouseout.WarehouseId,
                        vacancy = s.StockOrNot ?? false,
                        Unitprice = u.Unitprice,
                        TotalAmt = u.TotalAmt
                    });

        query = query.OrderBuilder(input);
        var listMxPage = await query.ToPagedListAsync(input.Page, input.PageSize);
        return listMxPage;
    }

    /// <summary>
    /// 查询商品出库明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetOutBoundMxList")]
    public async Task<List<WarehouseoutMXOutput>> GetOutBoundMxList(WarehouseoutMXInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(u => u.TenantId == _userManager.TenantId && u.IsDelete == false)
                    .WhereIF(input.OutId > 0, u => u.OutId == input.OutId)
                    .WhereIF(input.Id > 0, u => u.Id == input.Id)
                    .LeftJoin<Warehouseout>((u, o) => u.OutId == o.Id)
                    .LeftJoin<WarehouseStore>((u, o, s) => o.WarehouseId == s.WarehouseId && u.goodsId == s.TradeID)
                    .Select((u, o, s) => new WarehouseoutMXOutput
                    {
                        Productcode = u.Warehousegoods.Code,
                        Brand = u.Warehousegoods.Brand,
                        Barcode = u.Warehousegoods.barcode,
                        Specifications = u.Warehousegoods.Specs,
                        UnitName = u.WarehouseGoodsUnit.Name,
                        Notes = u.Notes,
                        Deliverytime = u.Deliverytime,
                        Id = u.Id,
                        OutCount = u.OutCount,
                        OutId = u.OutId,
                        Tradename = u.Warehousegoods.Name,
                        TrueOutCount = (int)u.TrueOutCount,
                        GoodsId = u.goodsId,
                        GoodProduct = u.GoodProduct,
                        Unit = u.Unit,
                        WarehouseId = u.warehouseout.WarehouseId,
                        vacancy = s.StockOrNot ?? false,
                        Unitprice = u.Unitprice,
                        TotalAmt = u.TotalAmt
                    });

        var listMx = await query.OrderBuilder(input).ToListAsync();
        return listMx;
    }

    public async Task<int> AddOrUpdate(List<WarehouseoutMX> listMx)
    {
        return await _rep.AsSugarClient().Storageable(listMx).ExecuteCommandAsync();
        // return await _rep.InsertRangeAsync(listMx);
    }
    /// <summary>
    /// 增加商品出库明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddWarehouseoutMXInput input)
    {
        if (input.AuxiliaryOutCount != null)
        {
            var res = await _repgoods.AsQueryable().WhereIF(input.auxiliaryunit != null, u => u.Id == input.goodsId).FirstAsync();
            input.OutCount = (int)((input.AuxiliaryOutCount * Convert.ToInt32(res.convertCount)) + input.OutCount);
        }
        var entity = input.Adapt<WarehouseoutMX>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除商品出库明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteWarehouseoutMXInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);

        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新商品出库明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWarehouseoutMXInput input)
    {
        var entity = input.Adapt<WarehouseoutMX>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取商品出库明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<WarehouseoutMX> Get([FromQuery] QueryByIdWarehouseoutMXInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);

    }

    /// <summary>
    /// 获取商品出库明细列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WarehouseoutMXOutput>> List([FromQuery] WarehouseoutMXInput input)
    {
        return await _rep.AsQueryable().Select<WarehouseoutMXOutput>().ToListAsync();
    }





}

