﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 商品单位基础输入参数
    /// </summary>
    public class WarehouseGoodsUnitBaseInput
    {
        /// <summary>
        /// 单位
        /// </summary>
        public virtual string Name { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public virtual bool Status { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public virtual string? Remark { get; set; }
        
    }

    /// <summary>
    /// 商品单位分页查询输入参数
    /// </summary>
    public class WarehouseGoodsUnitInput : BasePageInput
    {
        /// <summary>
        /// 单位
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public bool? Status { get; set; }
        
    }

    /// <summary>
    /// 商品单位增加输入参数
    /// </summary>
    public class AddWarehouseGoodsUnitInput : WarehouseGoodsUnitBaseInput
    {
    }

    /// <summary>
    /// 商品单位删除输入参数
    /// </summary>
    public class DeleteWarehouseGoodsUnitInput : BaseIdInput
    {
    }

    /// <summary>
    /// 商品单位更新输入参数
    /// </summary>
    public class UpdateWarehouseGoodsUnitInput : WarehouseGoodsUnitBaseInput
    {
        /// <summary>
        /// Id
        /// </summary>
        [Required(ErrorMessage = "Id不能为空")]
        public long Id { get; set; }
        
    }

    /// <summary>
    /// 商品单位主键查询输入参数
    /// </summary>
    public class QueryByIdWarehouseGoodsUnitInput : DeleteWarehouseGoodsUnitInput
    {

    }
