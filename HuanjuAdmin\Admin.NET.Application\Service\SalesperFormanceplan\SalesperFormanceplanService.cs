﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Application.Enum;
using Admin.NET.Core.Service;
using AngleSharp.Dom;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Nest;
using SqlSugar.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.ProductOfflineGetSameCityTemplateResponse.Types.Template.Types;

namespace Admin.NET.Application;
/// <summary>
/// 履约计划服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class SalesperFormanceplanService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<SaleOfGoods> _repOfGoods;
    private readonly SqlSugarRepository<Warehousegoods> _repgoods;
    private readonly SqlSugarRepository<Warehouseout> _repout;
    private readonly SqlSugarRepository<Receipt> _repReceipt;
    private readonly SqlSugarRepository<SalesperFormanceplan> _rep;
    private readonly SqlSugarRepository<Salescontract> _reps;
    private readonly WarehouseoutService _warehouseoutService;
    private readonly SqlSugarRepository<Warehouse> _repWarehouse;
    UserManager _userManager;
    public SalesperFormanceplanService(WarehouseoutService warehouseoutService, SqlSugarRepository<SalesperFormanceplan> rep, SqlSugarRepository<Receipt> receiptService, SqlSugarRepository<Warehouseout> warehouseoutService2, SqlSugarRepository<Salescontract> reps, SqlSugarRepository<Warehousegoods> repgoods, SqlSugarRepository<SaleOfGoods> repOfGoods, UserManager userManager, SqlSugarRepository<Warehouse> repWarehouse
        )
    {
        _repOfGoods = repOfGoods;
        _repgoods = repgoods;
        _reps = reps;
        _rep = rep;
        _repout = warehouseoutService2;
        _repReceipt = receiptService;
        _warehouseoutService = warehouseoutService;
        _userManager = userManager;
        _repWarehouse = repWarehouse;
    }

    /// <summary>
    /// 分页查询履约计划
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<SalesperFormanceplanOutput>> Page(SalesperFormanceplanInput input)
    {
        var query = _rep.AsQueryable()
             .LeftJoin<Salescontract>((u, contract) => u.SalesOrder == contract.Id.ToString())
             .Where(u => u.SalesOrder == input.SalesOrder)
                    .Select<SalesperFormanceplanOutput>(((u, contract) => new SalesperFormanceplanOutput
                    {
                        Id = u.Id,
                        PlanTime = u.PlanTime.ToDateTime().Date,
                        Type = u.Type,
                        Status = u.Status,
                        Plan = u.Plan,
                        PlanId = u.PlanId,
                        SalesOrderName = contract.SalesOrder
                    })
            )
;

        if (input.PlanTimeRange != null && input.PlanTimeRange.Count > 0)
        {
            DateTime? start = input.PlanTimeRange[0];
            query = query.WhereIF(start.HasValue, u => u.PlanTime > start);
            if (input.PlanTimeRange.Count > 1 && input.PlanTimeRange[1].HasValue)
            {
                var end = input.PlanTimeRange[1].Value.AddDays(1);
                query = query.Where(u => u.PlanTime < end);
            }
        }
        query = query.OrderBuilder(input);
        var data = query.ToList();
        foreach (var item in data)
        {
            if (item.Type == SalesTypeEnmu.SendOut)
                item.Plan = _repout.GetFirstAsync(u => (u.SuperiorNum == item.SalesOrderName && u.IsDelete == false)).Result?.OutboundStatus ?? 0;
            else if (item.Type == SalesTypeEnmu.Collection)
                item.Plan = _repReceipt.GetFirstAsync(u => u.Id == item.PlanId.Value).Result?.PaymentStatus ?? 0;
            else
                item.Plan = item.Plan;
        }
        return data.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加履约计划
    /// </summary> 
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddSalesperFormanceplanInput input)
    {
        #region 改为状态开启时生成
        //var query = _reps.AsQueryable().Where(u => u.Id == input.SalesOrder).Select<Salescontract>().FirstAsync();
        //switch (input.Type)
        //{
        //    case Enum.SalesTypeEnmu.SendOut:
        //        //var OfGoods = _repOfGoods.AsQueryable().Where(u => u.SalesOrder == input.SalesOrder).Select<SaleOfGoods>().FirstAsync();
        //        //var query1 = _repgoods.AsQueryable().Where(u => u.Name.Contains(OfGoods.Result.TradeName)).Select<Warehousegoods>().FirstAsync();
        //        //List<AddWarehouseoutMXInput> listMx  = new List<AddWarehouseoutMXInput>();
        //        //AddWarehouseoutMXInput ami = new AddWarehouseoutMXInput {
        //        // Tradename= OfGoods.Result.TradeName,
        //        //    Barcode  = query1.Result.Code,
        //        //     Brand = query1.Result.Brand,
        //        //    Specifications = query1.Result.Specs,
        //        //    Unit = query1.Result.Unit,
        //        //    goodsId = OfGoods.Result.goodsId,
        //        //};
        //        AddWarehouseoutInput ai = new AddWarehouseoutInput
        //        {
        //            Outboundtype = "销售出库",
        //            Deliverytime = DateTime.Now,
        //            Auditstatus = "待审核",
        //            CustomId = query.Result.CustomerId,
        //            CustomerName = query.Result.CustomerName,
        //            Contacts = query.Result.Contacts,
        //            Phone = query.Result.Tel,
        //            Address = query.Result.Address,
        //        };
        //        AddWarehouseoutMx ah = new AddWarehouseoutMx
        //        {
        //            addWarehouseoutInput = ai,

        //        };
        //        await _warehouseoutService.Add(ah);
        //        break;
        //    case Enum.SalesTypeEnmu.Collection:
        //        AddReceiptInput receipt = new AddReceiptInput
        //        {
        //            UnitName = query.Result.CustomerName,
        //            DocumentAmount = query.Result.ContractAmount,
        //            AmountReceived = query.Result.AmountReceived,
        //            InvoicedAmount = query.Result.InvoicedAmount,
        //        };
        //        await _receiptService.Add(receipt);
        //        break;
        //    case Enum.SalesTypeEnmu.Invoicing:
        //        break;
        //    default:
        //        break;
        //}
        #endregion
        var entity = input.Adapt<SalesperFormanceplan>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除履约计划
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteSalesperFormanceplanInput input)
    {
        var entity = input.Adapt<SalesperFormanceplan>();
        await _rep.DeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新履约计划
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateSalesperFormanceplanInput input)
    {
        var entity = input.Adapt<SalesperFormanceplan>();
        //获取销售合约
        var query = _reps.AsQueryable().Where(u => u.SalesOrder == input.SalesOrder).Select<Salescontract>().FirstAsync().Result;
        //获取所有履约计划
        var list = _rep.AsQueryable().Where(u => u.SalesOrder == input.SalesOrder && u.Type != SalesTypeEnmu.Invoicing).ToList();
        //履约计划有一项是不是未完成或者中止，合约为完成状态改为履约中
        if (list.Any(u => u.Plan < 3) && query.ContractStatus == 4)
        {
            query.ContractStatus = 3;
            await _reps.AsUpdateable(query).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        }
        //履约计划全部是完成，合约为履约中改为完成
        if (list.All(u => u.Plan == 3) && query.ContractStatus == 3)
        {
            query.ContractStatus = 4;
            query.EndTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd"));
            await _reps.AsUpdateable(query).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        }
        //履约计划全部是中止，合约为履约中改为中止
        if (list.All(u => u.Plan == 4))
        {
            query.ContractStatus = 5;
            query.EndTime = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd"));
            await _reps.AsUpdateable(query).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
        }
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }
    /// <summary>
    /// 开关履约计划
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [UnitOfWork]
    [ApiDescriptionSettings(Name = "Switch")]
    public async Task Switch(UpdateSalesperFormanceplanInput input)
    {
        var entity = input.Adapt<SalesperFormanceplan>();
        var saleOrderOri = input.SalesOrder;
        //var entity = _rep.AsQueryable().Where(u => u.Id == input.Id).Select<SalesperFormanceplan>().FirstAsync();
        var query = _reps.AsQueryable().Where(u => u.SalesOrder == input.SalesOrder).Select<Salescontract>().FirstAsync();
        entity.SalesOrder = query.Result.Id + "";
        input.SalesOrder = query.Result.Id + "";
        var saleContact = query.Result;
        if (input.Status == SalesStatusEnmu.InConfirm)
        {
            switch (input.Type)
            {
                case Enum.SalesTypeEnmu.SendOut:
                    var OfGoods = _repOfGoods.AsQueryable().Where(u => (u.SalesOrder + "") == input.SalesOrder).Select<SaleOfGoods>();
                    List<AddWarehouseoutMXInput> listMx = new List<AddWarehouseoutMXInput>();
                    foreach (var item in OfGoods.ToList())
                    {
                        var query1 = _repgoods.AsQueryable().Where(u => u.Name.Contains(item.TradeName)).Select<Warehousegoods>().FirstAsync();
                        AddWarehouseoutMXInput ami = new AddWarehouseoutMXInput
                        {
                            Tradename = item.TradeName,
                            Barcode = query1.Result.Code,
                            Brand = query1.Result.Brand,
                            Specifications = query1.Result.Specs,
                            Unit = query1.Result.Unit,
                            goodsId = item.goodsId,
                            auxiliaryunit = query1.Result.Auxiliaryunit,
                            GoodProduct = true,
                            OutCount = item.puchQty,
                            Unitprice = item.puchPrice ?? 0,
                            TotalAmt = item.puchAmt ?? 0
                        };
                        listMx.Add(ami);
                    }
                    var defaultWareHouse = _repWarehouse.GetFirstAsync(u => (u.TenantId == _userManager.TenantId && u.IsDefault == 1)).Result?.Id ?? 0;
                    AddWarehouseoutInput ai = new AddWarehouseoutInput
                    {
                        Outboundtype = 0,//销售出库
                        Deliverytime = DateTime.Now,
                        Auditstatus = "待审核",
                        CustomId = query.Result.CustomerId,
                        CustomerName = query.Result.CustomerName,
                        Contacts = query.Result.Contacts,
                        Phone = query.Result.Tel,
                        Address = query.Result.Address,
                        SuperiorNum = query.Result.SalesOrder,
                        WarehouseId = defaultWareHouse,
                        GoodsInfo = query.Result.GoodsInfo,
                        TotalAmt = query.Result.TotalAmt,
                        DiscountAmt = query.Result.DiscountAmt,
                        ActualAmt = query.Result.ContractAmount
                    };
                    AddWarehouseoutMx ah = new AddWarehouseoutMx
                    {
                        addWarehouseoutInput = ai,
                        listMx = listMx
                    };
                    await _warehouseoutService.Add(ah);
                    break;
                case Enum.SalesTypeEnmu.Collection:
                    AddReceiptInput receipt = new AddReceiptInput
                    {
                        UnitName = query.Result.CustomerName,
                        DocumentAmount = query.Result.ContractAmount,
                        AmountReceived = query.Result.AmountReceived,
                        InvoicedAmount = query.Result.InvoicedAmount,
                        SuperiorOrder = query.Result.SalesOrder
                    };
                    var rec = receipt.Adapt<Receipt>();
                    var orderNumber = await App.GetRequiredService<PubOrderService>().GetNewOrder("SK");
                    if (orderNumber.IsNullOrEmpty())
                        throw Oops.Oh(ErrorCodeEnum.GY1001);
                    rec.ReceiptNo = orderNumber;
                    var addSuccess = await _repReceipt.AsInsertable(rec).ExecuteCommandIdentityIntoEntityAsync();
                    if (addSuccess)
                        entity.PlanId = rec.Id;
                    break;
                case Enum.SalesTypeEnmu.Invoicing:
                    var result = await _rep.AsQueryable().Where(u => u.SalesOrder == input.SalesOrder && u.Type == SalesTypeEnmu.Collection).Select<SalesperFormanceplan>().FirstAsync();
                    if (result != null && result.Status == SalesStatusEnmu.InConfirm)
                    {
                        entity.Plan = 3;
                        var receiptNew = await _repReceipt.AsQueryable().Where(u => u.SuperiorOrder == saleOrderOri).Select<Receipt>().FirstAsync();
                        receiptNew.InvoiceStatus = 3;
                        await _repReceipt.AsUpdateable(receiptNew).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();

                    }
                    else
                    {
                        throw Oops.Oh("收款计划未打开,禁止打开开票计划");
                    }

                    break;
                default:
                    break;
            }
            //履约计划开始，合约如果是已签约,改为履约中     ------20240810

            //if (saleContact.ContractStatus == 2)
            //{
            //    saleContact.ContractStatus = 3;
            //    await _reps.AsUpdateable(saleContact).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
            //}
        }
        else//关闭时删除生成的数据
        {
            switch (input.Type)
            {
                case Enum.SalesTypeEnmu.SendOut:
                    var repout = await _repout.GetFirstAsync(u => (u.SuperiorNum) == query.Result.SalesOrder) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
                    if (repout.OutboundStatus > 0)
                        throw Oops.Oh("单据已提交,禁止关闭");
                    await _repout.FakeDeleteAsync(repout);   //假删除
                    break;
                case Enum.SalesTypeEnmu.Collection:
                    var result1 = await _rep.AsQueryable().Where(u => u.SalesOrder == input.SalesOrder && u.Type == SalesTypeEnmu.Invoicing).Select<SalesperFormanceplan>().FirstAsync();
                    if (result1 != null && result1.Status == SalesStatusEnmu.InConfirm)
                    {
                        throw Oops.Oh("发票计划打开时,禁止关闭收款计划");
                    }
                    var receipt = await _repReceipt.GetFirstAsync(u => u.Id == entity.PlanId.Value) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
                    if (receipt.PaymentStatus > 0)
                        throw Oops.Oh("单据已提交,禁止关闭");
                    await _repReceipt.FakeDeleteAsync(receipt);   //假删除
                    break;
                case Enum.SalesTypeEnmu.Invoicing:
                    break;
                default:
                    break;
            }
            //履约计划全部为未开始时，合约如果是履约中,改为已签约     ------20240814
            //var salesperFormanceplan = _rep.AsQueryable().Where(u => u.SalesOrder == input.SalesOrder).Select<SalesperFormanceplan>().ToList();
            //salesperFormanceplan.Find(u=>u.Id==entity.Id).Status=input.Status.ToInt();
            //if (salesperFormanceplan.TrueForAll(u=>u.Status== SalesStatusEnmu.Confirm.ToInt())&& saleContact.ContractStatus == 3)
            //{
            //    saleContact.ContractStatus = 2;
            //    await _reps.AsUpdateable(saleContact).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
            //}
        }
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取履约计划
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpGet]
    //[ApiDescriptionSettings(Name = "Detail")]
    //public async Task<SalesperFormanceplan> Get([FromQuery] QueryByIdSalesperFormanceplanInput input)
    //{
    //}

    /// <summary>
    /// 获取履约计划列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<SalesperFormanceplanOutput>> List([FromQuery] SalesperFormanceplanInput input)
    {
        return await _rep.AsQueryable().Select<SalesperFormanceplanOutput>().ToListAsync();
    }





}

