<template>
    <toolbar-panel :form-items="formItems" :operation-buttons="operationButtons" v-model:query-params="queryParams"
        @query="handleQuery" style="height: 90px; " />
    
    <order-table ref="orderTableRef" @handle-query="handleQuery" v-model:page-params="pageParams" @edit-order="editOrder" @delete-order="deleteOrder"/>

    <order-form ref="orderFormRef" @handle-submit="handleSubmit"/>
</template>

<script setup lang="ts">
import toolbarPanel from '/@/components/toolbarPanel/index.vue';
import orderTable from './components/mainModules/orderTable.vue';
import orderForm from './components/dialogModules/orderForm.vue';
import { ref, onMounted } from 'vue';
import { pageProcessOrder } from '/@/api/main/processOrder';

const formItems: FormItem[] = [
    { label: '加工单号', prop: 'orderNo', type: 'input' },
];

const orderFormRef = ref<InstanceType<typeof orderForm>>();

const operationButtons: OperationButton[] = [
    { text: '新增加工单', icon: 'ele-Plus', handler: () => { orderFormRef.value?.openDialog() }, auth: 'processOrder:add' },
];

const queryParams = ref({});

const pageParams = ref<PageParams>({
    page: 1,
    pageSize: 10,
    total: 0
});

const orderParams = ref({
    field: 'orderNo',
    order: 'asc'
});

const orderTableRef = ref<InstanceType<typeof orderTable>>();

const handleQuery = () => {
    // 这里添加获取表格数据的逻辑
    pageProcessOrder({...queryParams.value, ...pageParams.value, ...orderParams.value}).then((res: any) => {
        orderTableRef.value?.setTableData(res.data.result.items);
        pageParams.value.total = res.data.result.total;
        orderTableRef.value?.toggleRowExpansion();
    });
};

const editOrder = (row: any) => {
    orderFormRef.value?.openDialog(row.id);
};

const deleteOrder = (row: any) => {
    handleQuery();
};

const handleSubmit = () => {
    handleQuery();
};

onMounted(() => {
    handleQuery();
});

</script>

<style scoped></style>
