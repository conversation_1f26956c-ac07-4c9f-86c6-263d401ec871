﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 固资库存基础输入参数
/// </summary>
public class AssetInventoryBaseInput
{
    /// <summary>
    /// 名称
    /// </summary>
    public virtual string Name { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public virtual string? Brand { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    public virtual string? Code { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public virtual string? Specs { get; set; }

    /// <summary>
    /// 总数量
    /// </summary>
    public virtual int TotalCount { get; set; }

    /// <summary>
    /// 借用数量
    /// </summary>
    public virtual int BorrowCount { get; set; }

    /// <summary>
    /// 库存数量
    /// </summary>
    public virtual int InventoryCount { get; set; }

    /// <summary>
    /// 初始总数量
    /// </summary>
    public virtual int BaseTotalCount { get; set; }

    /// <summary>
    /// 初始借用数量
    /// </summary>
    public virtual int BaseBorrowCount { get; set; }

    /// <summary>
    /// 初始库存数量
    /// </summary>
    public virtual int BaseInventoryCount { get; set; }

    /// <summary>
    /// 初始需要归还
    /// </summary>
    public virtual bool IsNdReturn { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }

}

/// <summary>
/// 固资库存分页查询输入参数
/// </summary>
public class AssetInventoryInput : BasePageInput
{
    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 需要归还
    /// </summary>
    public bool IsNdReturn { get; set; }

}

/// <summary>
/// 固资库存增加输入参数
/// </summary>
public class AddAssetInventoryInput : AssetInventoryBaseInput
{
}

/// <summary>
/// 固资库存删除输入参数
/// </summary>
public class DeleteAssetInventoryInput : BaseIdInput
{
}

/// <summary>
/// 固资库存更新输入参数
/// </summary>
public class UpdateAssetInventoryInput : AssetInventoryBaseInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public long Id { get; set; }

}

/// <summary>
/// 固资库存主键查询输入参数
/// </summary>
public class QueryByIdAssetInventoryInput : DeleteAssetInventoryInput
{

}
