namespace Admin.NET.Application;

/// <summary>
/// 工单处理方式枚举
/// </summary>
[Description("工单处理方式枚举")]
public enum WorkOrderProcessEnum
{
    /// <summary>
    /// 暂无
    /// </summary>
    [Description("暂无")]
    Nothing = 0,
    /// <summary>
    /// 维修
    /// </summary>
    [Description("维修")]
    Repair = 1,

    /// <summary>
    /// 退货
    /// </summary>
    [Description("退货")]
    Refund = 2,

    /// <summary>
    /// 换货
    /// </summary>
    [Description("换货")]
    Replacement = 3,
}