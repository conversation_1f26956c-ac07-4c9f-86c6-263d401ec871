﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Admin.NET.Core.Service;
using AngleSharp.Dom;
using Furion.FriendlyException;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using SqlSugar;
using System;
using System.Diagnostics.Metrics;
using System.Linq;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.WeDataQueryBindListResponse.Types;

namespace Admin.NET.Application;
/// <summary>
/// 流程审批服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WorkflowReviewService : IDynamicApiController, ITransient
{
    private readonly UserManager _userManager;
    private readonly SqlSugarRepository<WorkflowReview> _rep;
    private readonly SqlSugarRepository<WorkflowRecord> _repRecord;

    public WorkflowReviewService(
        UserManager userManager,
        SqlSugarRepository<WorkflowReview> rep,
        SqlSugarRepository<WorkflowRecord> repRecord)
    {
        _rep = rep;
        _userManager = userManager;
        _repRecord = repRecord;
    }

    /// <summary>
    /// 分页查询流程审批
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WorkflowReviewOutput>> Page(WorkflowReviewInput input)
    {
        var query = _rep.AsQueryable()
                    .WhereIF(!string.IsNullOrWhiteSpace(input.ProcessName), u => u.ProcessName.Contains(input.ProcessName.Trim()))
                    .Select<WorkflowReviewOutput>();
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加流程审批
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddWorkflowReviewInput input)
    {
        var entity = input.Adapt<WorkflowReview>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除流程审批
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteWorkflowReviewInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新流程审批
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWorkflowReviewInput input)
    {
        var entity = input.Adapt<WorkflowReview>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取流程审批
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<WorkflowReview> Get([FromQuery] QueryByIdWorkflowReviewInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取流程审批列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WorkflowReviewOutput>> List(WorkflowReviewInput input)
    {
        var rs = await _rep.AsQueryable()
                .Where(x => x.IsDelete == false)
                .WhereIF(!string.IsNullOrWhiteSpace(input.ProcessName), u => u.ProcessName.Contains(input.ProcessName.Trim()))
                .Select<WorkflowReviewOutput>().ToListAsync();
        return rs;
    }

    /// <summary>
    /// 创建审批流程
    /// </summary>
    public async Task<Tuple<int, string>> GetNewOrder(string processName, string prefix)
    {
        var lcxx = await _rep.GetFirstAsync(x => x.ProcessName == processName && x.TenantId == _userManager.TenantId && x.IsDelete == false && x.Status == 1);

        if (lcxx == null)
        {
            return new Tuple<int, string>(0, "没有设置审批流程");
        }

        var listLCMX = await App.GetService<WorkflowReviewMXService>().List(lcxx.Id);

        if (listLCMX == null)
        {
            return new Tuple<int, string>(-1, "没有设置审批流程明细");
        }

        AddWorkflowOrderInput addWorkflowOrderInput = new AddWorkflowOrderInput();
        addWorkflowOrderInput.UerId = _userManager.UserId;
        addWorkflowOrderInput.OrgId = _userManager.OrgId;
        var spdh = await App.GetService<PubOrderService>().GetNewOrder(prefix + "SP");
        if (spdh.IsNullOrEmpty())
        {
            throw Oops.Oh(ErrorCodeEnum.GY1001);
        }
        addWorkflowOrderInput.ApprovalNumber = spdh;
        addWorkflowOrderInput.WorkflowId = lcxx.Id;
        addWorkflowOrderInput.CreateTime = DateTime.Now;
        addWorkflowOrderInput.Status = 0;
        var orderId = await App.GetService<WorkflowOrderService>().AddReturnID(addWorkflowOrderInput);

        var _repUser = App.GetService<SysUserService>();
        List<WorkflowRecord> listRecord = new List<WorkflowRecord>();
        var minIndex = listLCMX.Min(x => x.StepNumber);
        foreach (var lcmx in listLCMX.OrderBy(x => x.StepNumber))
        {
            if (lcmx.NodeType == 1)
            {
                //按岗位
                var listUser = await _repUser.GetUserInfoByPosOrg(lcmx.PositionId ?? 0, _userManager.OrgId);
                if (listUser == null)
                {
                    foreach (var userInfo in listUser)
                    {
                        var record = new WorkflowRecord();
                        record.OrderId = orderId;
                        record.StepNumber = lcmx.StepNumber;
                        record.Approver = userInfo.Id;
                        record.Status = lcmx.StepNumber == minIndex ? ApproveStatusEnum.InProgress : ApproveStatusEnum.NotApproved;
                        listRecord.Add(record);
                    }
                }
            }
            else
            {
                //按职工ID
                var record = new WorkflowRecord();
                record.OrderId = orderId;
                record.StepNumber = lcmx.StepNumber;
                record.Approver = lcmx.UserID ?? 0;
                record.Status = lcmx.StepNumber == minIndex ? ApproveStatusEnum.InProgress : ApproveStatusEnum.NotApproved;
                listRecord.Add(record);
            }
        }

        if (listRecord != null)
        {
            await _repRecord.AsInsertable(listRecord).ExecuteCommandAsync();
        }

        return new Tuple<int, string>(1, addWorkflowOrderInput.ApprovalNumber);
    }




}

