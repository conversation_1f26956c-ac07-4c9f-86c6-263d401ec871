﻿using System;

namespace Admin.NET.Application;

    /// <summary>
    /// 收支明细输出参数
    /// </summary>
    public class warerevenueOutput
    {
       /// <summary>
       /// 主键Id
       /// </summary>
       public long Id { get; set; }
    
       /// <summary>
       /// 收支时间
       /// </summary>
       public DateTime? revenueTime { get; set; }
    
       /// <summary>
       /// 来往单位
       /// </summary>
       public string contactunits { get; set; }
    
       /// <summary>
       /// 关联单号
       /// </summary>
       public string ordernumber { get; set; }
    
       /// <summary>
       /// 收支科目
       /// </summary>
       public string? subject { get; set; }
    
       /// <summary>
       /// 二级科目
       /// </summary>
       public string? levelsubject { get; set; }
    
       /// <summary>
       /// 收入金额
       /// </summary>
       public decimal? Incomeamount { get; set; }
    
       /// <summary>
       /// 支出金额
       /// </summary>
       public decimal? expenditureamount { get; set; }
    
       /// <summary>
       /// 经办人
       /// </summary>
       public long? handledby { get; set; }
    
       /// <summary>
       /// 备注
       /// </summary>
       public string? notes { get; set; }
    /// <summary>
    /// 付款类型 1收入，2支出
    /// </summary>
    public int? revenueType { get; set; }
    /// <summary>
    /// 经办人名字
    /// </summary>
    public string handledbyName { get; set; }
    /// <summary>
    /// 交易账户
    /// </summary>
    public string TradingName { get; set; }
}
 

