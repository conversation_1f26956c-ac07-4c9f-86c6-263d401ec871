﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;

namespace Admin.NET.Application;
/// <summary>
/// 唯一码库服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WarehouseuniquecodeService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<Warehouseuniquecode> _rep;
    public WarehouseuniquecodeService(SqlSugarRepository<Warehouseuniquecode> rep)
    {
        _rep = rep;
    }

    /// <summary>
    /// 分页查询唯一码库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WarehouseuniquecodeOutput>> Page(WarehouseuniquecodeInput input)
    {
        var query= _rep.AsQueryable()
                    .WhereIF(!string.IsNullOrWhiteSpace(input.TradeName), u => u.TradeName.Contains(input.TradeName.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.UniqueCode), u => u.UniqueCode.Contains(input.UniqueCode.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Supplier), u => u.Supplier.Contains(input.Supplier.Trim()))

                    .Select<WarehouseuniquecodeOutput>(x=>new WarehouseuniquecodeOutput {
                        TradeCode = x.Warehousegoods.Code,
                         TradeName = x.Warehousegoods.Name,
                          UniqueCode = x.UniqueCode
                    })
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加唯一码库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddWarehouseuniquecodeInput input)
    {
        var entity = input.Adapt<Warehouseuniquecode>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除唯一码库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpPost]
    //[ApiDescriptionSettings(Name = "Delete")]
    //public async Task Delete(DeleteWarehouseuniquecodeInput input)
    //{
    //    await _rep.FakeDeleteAsync(entity);   //假删除
    //}

    /// <summary>
    /// 更新唯一码库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateWarehouseuniquecodeInput input)
    {
        var entity = input.Adapt<Warehouseuniquecode>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取唯一码库
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    //[HttpGet]
    //[ApiDescriptionSettings(Name = "Detail")]
    //public async Task<Warehouseuniquecode> Get([FromQuery] QueryByIdWarehouseuniquecodeInput input)
    //{
    //}

    /// <summary>
    /// 获取唯一码库列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WarehouseuniquecodeOutput>> List([FromQuery] WarehouseuniquecodeInput input)
    {
        return await _rep.AsQueryable().Select<WarehouseuniquecodeOutput>().ToListAsync();
    }





}

