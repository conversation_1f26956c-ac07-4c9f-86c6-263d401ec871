﻿using System;

namespace Admin.NET.Application;

/// <summary>
/// 批次表输出参数
/// </summary>
public class warehousebatchOutput
{
   /// <summary>
   /// 主键Id
   /// </summary>
   public long Id { get; set; }

   /// <summary>
   /// 库存ID
   /// </summary>
   public long InventoryId { get; set; }

   /// <summary>
   /// 批次号
   /// </summary>
   public string Batchnumber { get; set; }

   /// <summary>
   /// 良品数量
   /// </summary>
   public int? GoodProductNum { get; set; }

   /// <summary>
   /// 次品数量
   /// </summary>
   public int? RejectNum { get; set; }

   /// <summary>
   /// 生产日期
   /// </summary>
   public DateTime? ProduceTime { get; set; }

   /// <summary>
   /// 保质期
   /// </summary>
   public int WarrantyTime { get; set; }
   /// <summary>
   /// 过期预警（天）
   /// </summary>
   public int? ExpiryReminder { get; set; }

   /// 到期时间
   /// </summary>
   public DateTime ExpirationTime { get; set; }

   /// <summary>
   /// 成本
   /// </summary>
   public decimal? Cost { get; set; }

   /// <summary>
   /// 保质期状态（-1 未设置 0-正常 1-临期 2-过期）
   /// </summary>
   public int? ShelflifeStatus { get; set; }

}


