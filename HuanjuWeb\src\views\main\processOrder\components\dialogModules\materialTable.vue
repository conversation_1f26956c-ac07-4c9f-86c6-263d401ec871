<template>
    <el-collapse-item name="materialList">
        <template #title>
            <div style="width: 100%; display: flex; justify-content: space-between; align-items: center;">
                <el-text size="large">原料列表</el-text>
            </div>
        </template>

        <editable-table ref="tableRef" :table-data="props.tableData" @update:table-data="handleUpdate"
            :columns="tableColumns" :loading="loading" :show-pagination="false" :required-fields="['actQuantity']"
            validation-message="实际数量不能为空" style="height: calc(30vh - var(--el-collapse-header-height));">

            <template #actQuantity-edit="{ row }">
                <el-input-number v-model="row.actQuantity" :min="0" :precision="2" size="small" style="width: 100%"
                    controls-position="right" />
            </template>
            <template #unitPrice-edit="{ row }">
                <el-input-number v-model="row.unitPrice" :min="0" :precision="2" size="small" style="width: 100%"
                    controls-position="right" />
            </template>
        </editable-table>
    </el-collapse-item>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import EditableTable from '/@/components/editableTable/index.vue';
import { getWarehouseStockList } from '/@/api/main/processOrder';
import { ElMessage } from 'element-plus';
import { cloneDeep } from 'lodash-es';

const props = defineProps({
    tableData: {
        type: Array,
        required: true,
        default: () => []
    },
    activeItems: {
        type: Array,
        required: true,
        default: () => []
    },
    materialWarehouseId: {
        type: String,
        required: true
    }
});

const emit = defineEmits(['update:tableData']);

const loading = ref(false);
const tableRef = ref();

// 表格列配置
const tableColumns = [
    { type: 'index', label: '序号' },
    { prop: 'warehousegoods.name', label: '原料名称', showOverflowTooltip: true },
    { prop: 'warehousegoods.code', label: '原料编码', showOverflowTooltip: true },
    { prop: 'warehousegoods.specs', label: '规格型号', showOverflowTooltip: true },
    { prop: 'warehousegoods.warehouseGoodsUnit.name', label: '单位', showOverflowTooltip: true },
    { prop: 'unitPrice', label: '单价', showOverflowTooltip: true },
    { prop: 'totalPrice', label: '总价', showOverflowTooltip: true },
    { prop: 'baseQuantity', label: '基准数量', showOverflowTooltip: true },
    {
        prop: 'actQuantity',
        label: '实际数量',
        slot: 'actQuantity',
        editable: true,
        width: 110,
        showOverflowTooltip: true
    },
    { type: 'operation', label: '操作', width: 140, fixed: 'right' }
];

interface MaterialItem {
    actQuantity: number;
    baseQuantity: number;
    [key: string]: any;
}

const warehouseStockList = ref({});

watch(() => props.materialWarehouseId, (v) => {
    if (v) {
        getWarehouseStockList({ Id: props.materialWarehouseId }).then(res => {
            warehouseStockList.value = res.data.result;
            const newTableData = cloneDeep(props.tableData);
            // 设置单价
            for (let i = 0; i < newTableData.length; i++) {
                const stock = warehouseStockList.value.find(x => x.tradeID === newTableData[i].warehousegoods.id);
                newTableData[i].unitPrice = stock?.currentCost ?? 0
                newTableData[i].maxQty = stock?.goodProduct ?? 0  // 改为对比良品数
                newTableData[i].vacancy = !!!stock ? false : newTableData[i].warehousegoods.vacancy
                newTableData[i].actQuantity = 0
            }
            emit('update:tableData', newTableData);
        });
    }
}, { immediate: true });

// 检查表格有效性
const valid = () => {
    return tableRef.value.valid();
};

// 重置编辑状态
const resetEditStatus = () => {
    if (tableRef.value) {
        tableRef.value.resetEditStatus();
    }
};

const handleUpdate = (val: MaterialItem[], index: number) => {
    // 检查是否是编辑完成后的状态（tableRef不在编辑状态）
    if (tableRef.value) {
        // 找出修改后的行（与原始数据对比）
        const updatedData = val;

        // 找到修改过的行
        let changedRowIndex = index;
        let ratio = 1;

        // 如果找到了修改过的行，根据比例更新其他行
        if (changedRowIndex !== -1) {

            // 创建一个新数组来存储更新后的数据
            const newData = cloneDeep(updatedData);

            // 检查实际数量是否大于良品库存数量
            if (newData[changedRowIndex].vacancy !== true && newData[changedRowIndex].actQuantity > newData[changedRowIndex].maxQty) {
                ElMessage.error(`${newData[changedRowIndex].warehousegoods.name}的实际数量不能大于良品库存数量${newData[changedRowIndex].maxQty}`);
                newData[changedRowIndex].actQuantity = 0;
                newData[changedRowIndex].totalPrice = 0;

            } else {
                newData[changedRowIndex].totalPrice = newData[changedRowIndex].actQuantity * newData[changedRowIndex].unitPrice;
            }

            // 计算比例：实际数量/基准数量
            if (newData[changedRowIndex].baseQuantity && newData[changedRowIndex].baseQuantity !== 0) {
                ratio = newData[changedRowIndex].actQuantity / newData[changedRowIndex].baseQuantity;
            }

            // 更新其他行的实际数量
            for (let i = 0; i < newData.length; i++) {
                if (i !== changedRowIndex && newData[i].baseQuantity) {
                    newData[i].actQuantity = Number((newData[i].baseQuantity * ratio).toFixed(2));
                    if (newData[i].vacancy !== true && newData[i].actQuantity > newData[i].maxQty) {
                        ElMessage.error(`${newData[i].warehousegoods.name}的实际数量不能大于良品库存数量${newData[i].maxQty}`);
                        newData[i].actQuantity = 0;
                        newData[i].totalPrice = 0;
                        newData[changedRowIndex].actQuantity = 0;
                        newData[changedRowIndex].totalPrice = 0;
                    } else {
                        newData[i].totalPrice = newData[i].actQuantity * newData[i].unitPrice;
                    }
                }
            }

            // 发送更新后的数据
            emit('update:tableData', newData);
            return;
        }
    }

    // 如果不是编辑完成后的状态或没有找到修改的行，直接更新数据
    emit('update:tableData', val);
}

// 暴露方法
defineExpose({
    loading,
    valid,
    resetEditStatus
});
</script>

<style scoped>
/* 确保编辑中的输入框宽度合适 */
.el-input,
.el-select,
.el-input-number {
    width: 100%;
}

/* 隐藏分页组件 */
:deep(.material-table .el-pagination) {
    display: none !important;
}
</style>