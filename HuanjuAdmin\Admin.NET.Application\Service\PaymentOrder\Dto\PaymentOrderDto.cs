﻿using Admin.NET.Application.Enum;

namespace Admin.NET.Application;

    /// <summary>
    /// 付款单输出参数
    /// </summary>
    public class PaymentOrderDto
    {
        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceiptNo { get; set; }
        
        /// <summary>
        /// 单位名称
        /// </summary>
        public string? UnitName { get; set; }
        
        /// <summary>
        /// 合同编号
        /// </summary>
        public string? ContractNum { get; set; }
        
        /// <summary>
        /// 销售单号
        /// </summary>
        public string? SalesOrder { get; set; }
        
        /// <summary>
        /// 单据金额
        /// </summary>
        public decimal? DocumentAmount { get; set; }
        
        /// <summary>
        /// 已收金额
        /// </summary>
        public decimal? AmountReceived { get; set; }
        
        /// <summary>
        /// 已开票金额
        /// </summary>
        public decimal? InvoicedAmount { get; set; }
        
        /// <summary>
        /// 成本类型
        /// </summary>
        public string? IncomeType { get; set; }
        
        /// <summary>
        /// 支出名目
        /// </summary>
        public string? IncomeCategory { get; set; }
        
        /// <summary>
        /// 付款状态
        /// </summary>
        public PaymentStatusEnum PaymentStatus { get; set; }
        
        /// <summary>
        /// 交易账户
        /// </summary>
        public long? Trading { get; set; }
        
        /// <summary>
        /// 发票状态
        /// </summary>
        public int? InvoiceStatus { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }
        
    }
