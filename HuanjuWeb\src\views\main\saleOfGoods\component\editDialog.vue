﻿<template>
	<div class="saleOfGoods-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="1000" draggable=""  :close-on-click-modal="false">
			<el-button type="primary" class="mb10" icon="ele-Plus" @click="addrkdDetail"> 
				新增商品明细 </el-button>
			<el-table :data="tableData" style="width: 100%" tooltip-effect="light">
						<el-table-column type="index" label="序号" width="55" align="center"  />
						<el-table-column prop="goodsId" label="商品" width="200"  show-overflow-tooltip="">
							<template #default="scope">
								<el-select clearable filterable v-model="scope.row.goodsId" placeholder="请选择商品" 	@change="getGoodsDetail(scope.row, scope.row.goodsId)">
									<el-option v-for=" (item, index) in warehousegoodsDropdownList" :key="index"
										:value="item.value" :label="item.label" />

								</el-select>
							</template>
						</el-table-column>
				<el-table-column prop="productCode" label="商品编码" show-overflow-tooltip="" width="109" />
				<el-table-column prop="brandName" label="品牌" show-overflow-tooltip="" width="109" />
				<el-table-column prop="specsName" label="规格" show-overflow-tooltip="" width="109" />
				<el-table-column prop="unit" label="单位"    show-overflow-tooltip="">
					<template #default="scope">
						<el-select clearable filterable v-model="scope.row.unit" placeholder=" ">
							<el-option v-for=" (item, index) in WarehouseUnit" :key="index" :value="item.value"
								:label="item.label" />
						</el-select>
					</template>
				</el-table-column>




						<el-table-column prop="puchQty" label="数量"  show-overflow-tooltip="" width="150" > 
							<template #default="scope">
								<el-input-number v-model="scope.row.puchQty" @change="recalculateAmount(scope.row)" clearable />
							</template>
						</el-table-column>
						<el-table-column prop="puchPrice" label="单价"  show-overflow-tooltip="" width="100" >
							<template #default="scope">
								{{ scope.row.puchPrice = scope.row.puchAmt / scope.row.puchQty || 0 }}
							</template>
						</el-table-column>
						<el-table-column prop="puchAmt" label="销售金额"  show-overflow-tooltip="" width="150" >
							<template #default="scope">
								<el-input-number v-model="scope.row.puchAmt" @change="recalculateAmount(scope.row)" clearable />
							
							</template>
						</el-table-column>
						<el-table-column label="操作" width="70" align="center" fixed="right" show-overflow-tooltip="">
							<template #default="scope">
								<el-button icon="ele-Delete" size="small" text="" type="primary"
									@click="deletePurchaseDetail(scope.$index)" v-auth="'warehousePurchaseMX:delete'"> 删除
								</el-button>
							</template>
						</el-table-column> 
					</el-table>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { ref,onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { addSaleOfGoods, updateSaleOfGoods } from "/@/api/main/saleOfGoods";
import { WarehousegoodsDropdown } from '/@/api/main/warehousegoods';
import { WarehouseGoodsUnit } from '/@/api/main/warehouseInrecord';
import { useGoodsStore } from '/@/stores/goods'
//父级传递来的参数
var props = defineProps({
	title: {
      type: String,
	  default: "",
    },
	orderId: {
	  type: String,
	  default: "",
	}
});
const tableData = ref<any[]>([]);
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const loading = ref(false);
const ruleForm = ref<any>({});
	//自行添加其他规则
	const rules = ref<FormRules>({
});
const addrkdDetail = () => {
	//debugger;
	tableData.value.push({
		// id: null,
		// TradeName: null,
		// ContractNum: null,
		id: null,
		goodsId: null,  // or appropriate initial value
		productCode:null,
		brandName:null,
		unit: null,
		puchPrice: null,
		puchQty: null,
		puchAmt: null,
		specsName: null
	});
};

const goodsStore = useGoodsStore()

// 商品信息
const warehousegoodsDropdownList = ref<any>([]);
const getWarehousegoodsDropdownList = async () => {
	await goodsStore.fetchGoodsList();
	warehousegoodsDropdownList.value = goodsStore.dropdownList;
};
getWarehousegoodsDropdownList();
const WarehouseUnit = ref<any>([]);
	const  WareUnit=async () => {
          var res = await WarehouseGoodsUnit();
          WarehouseUnit.value=res.data.result?? [];
    }
WareUnit();
const getGoodsDetail = (row: any, val: any) => {
	let obj = goodsStore.goodsList.find((v: { value: any; }) => {
		return v.value == val
	})
	row.tradename = obj?.label || ""
	row.brandName = obj?.brand
	row.barcode = obj?.id
	row.productCode = obj?.code
	row.specsName = obj?.specs
	row.unit = obj?.unit || ""
	row.auxiliaryunit = obj?.auxiliaryunit || ""
};
const recalculateAmount = (row: any) => {
	row.puchPrice = row.puchAmt / row.puchQty 
};
const deletePurchaseDetail = (index: number) => {
	// alert(tableDataMX.value.length);
	tableData.value.splice(index, 1);
	// alert(tableDataMX.value.length);
};
// 打开弹窗
const openDialog = (row: any) => {
	tableData.value.push(JSON.parse(JSON.stringify(row)));
	isShowDialog.value = true;
};

// 关闭弹窗
const closeDialog = () => {
  emit("reloadTable", props.orderId);
  isShowDialog.value = false;
    setTimeout(() => {
		loading.value = false;
	},500)
};

// 取消
const cancel = () => {
  isShowDialog.value = false;
  loading.value = false;
};

// 提交
const submit = async () => {
	loading.value = true;
	const reqData = {
		listMx: tableData.value
	}
	const res = await addSaleOfGoods(reqData);
	if (res.data.code != 200) {
		loading.value = false;
		ElMessage({
				message: res.data.message,
				type: "error",
			});
	} else {
		ElMessage({
				message: '添加成功',
				type: "success",
			});
		
		closeDialog();
	}
    //   let values = ruleForm.value;
    //   if (ruleForm.value.id != undefined && ruleForm.value.id > 0) {
    //     await updateSaleOfGoods(values);
    //   } else {
    //     await addSaleOfGoods(tableData.value);
    //   }
    //   closeDialog();
};





// 页面加载时
onMounted(async () => {
});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>




