<template>
	<div class="salesContract-container">
		<el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="queryParams" ref="queryForm" :inline="true">
				<el-form-item label="销售人">
					<el-input v-model="queryParams.salesperson" clearable="" placeholder="请输入销售人" class="w120" />
				</el-form-item>
				<el-form-item label="销售单号">
					<el-input v-model="queryParams.salesOrder" clearable="" placeholder="请输入销售单号" class="w120" />
				</el-form-item>
				<el-form-item label="合同编码">
					<el-input v-model="queryParams.contractCode" clearable="" placeholder="请输入合同编码" class="w120" />
				</el-form-item>
				<el-form-item label="签订时间">
					<el-date-picker placeholder="请选择签订时间" value-format="YYYY/MM/DD" type="daterange" v-model="queryParams.signingTimeRange" style="width: 250px" />
				</el-form-item>
				<el-form-item label="客户名称">
					<el-input v-model="queryParams.customerName" clearable="" placeholder="请输入客户名称" class="w120" />
				</el-form-item>
				<el-form-item label="合同状态">
					<el-select v-model="queryParams.contractStatus" multiple placeholder="请选择合同状态" clearable>
						<el-option
							v-for="item in contractStatusList"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'salesContract:page'"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" icon="ele-Plus" @click="openAddSalesContract" v-auth="'salesContract:add'"> 新增 </el-button>
					<el-button type="primary" icon="ele-Plus" @click="processSalesContract"> 提交 </el-button>
					
					<!--           <el-button type="primary" icon="ele-Plus" @click="printSalesContract" v-auth="'salesContract:add'"> 打印
          </el-button> -->
					<!-- <el-button type="primary" icon="ele-Plus" @click="discontinueAddSalesContract"> 中止
          </el-button> -->
					
					<el-button type="primary" icon="ele-Plus" @click="revocationButtonClick"> 撤回 </el-button>
					<el-button type="primary" icon="ele-Plus" @click="copySalesContract"> 复制 </el-button>
					<el-button type="primary" icon="ele-Plus" @click="inheritSalesContract"> 分配 </el-button>
				</el-form-item>
			</el-form>
		</el-card>
		<splitpanes class="default-theme" horizontal style="height: 78vh" @resized="handleTz">
			<pane :size="topSize" ref="topCardRef">
				<el-card class="top-Card" shadow="hover">
					<!--     <el-table :data="tableData" style="width: 100%" v-loading="loading" tooltip-effect="light" row-key="id" border=""> -->

					<el-table
						ref="taskTableRef"
						:data="tableData"
						class="top-table"
						:row-class-name="rowClassName"
						v-loading="loading"
						tooltip-effect="light"
						row-key="id"
						border=""
						@row-click="handleRowClick"
						highlight-current-row
						v-model:selection="selectedRows"
						@selection-change="changeSelect"
						stripe
						:height="tableHeight"
					>
						<!-- (selection: Array<any>) => selectedRows = selection -->
						<el-table-column type="selection" width="40" />
						<el-table-column type="Id" label="Id" width="55" align="center" v-if="false" />
						<el-table-column type="index" label="序号" width="55" align="center" />
						<el-table-column prop="salesperson" label="销售人" show-overflow-tooltip="" />
						<el-table-column prop="salesOrder" label="销售单号" width="100" show-overflow-tooltip="" />
						<el-table-column prop="contractCode" label="合同编码" show-overflow-tooltip="" />
						<!--        <el-table-column prop="contractStatus" label="合同状态"  show-overflow-tooltip="" /> -->
						<el-table-column prop="contractStatus" label="合同状态" show-overflow-tooltip="">
							<template #default="scope">
								<el-tag type="danger" v-if="scope.row.contractStatus === 0">洽谈</el-tag>
								<el-tag type="danger" v-if="scope.row.contractStatus === 1">待审核</el-tag>
								<el-tag type="danger" v-if="scope.row.contractStatus === 2">已签约</el-tag>
								<el-tag type="danger" v-if="scope.row.contractStatus === 3">履约中</el-tag>
								<el-tag type="danger" v-if="scope.row.contractStatus === 4">已完成</el-tag>
								<el-tag type="danger" v-if="scope.row.contractStatus === 5">中止</el-tag>
							</template>
						</el-table-column>

						<el-table-column prop="totalAmt" label="总金额" show-overflow-tooltip="" />
						<el-table-column prop="discountAmt" label="优惠金额" show-overflow-tooltip="" />
						<el-table-column prop="contractAmount" label="合同金额" show-overflow-tooltip="" />
						<el-table-column prop="signingTime" label="签订时间" show-overflow-tooltip="" />
						<el-table-column prop="endTime" label="结束时间" show-overflow-tooltip="" />
						<el-table-column prop="customerName" label="客户名称" show-overflow-tooltip="" />
						<el-table-column prop="goodsInfo" label="商品信息" show-overflow-tooltip="" />
						<el-table-column prop="contacts" label="联系人" show-overflow-tooltip="" />
						<el-table-column prop="tel" label="电话" show-overflow-tooltip="" />
						<el-table-column prop="address" label="地址" show-overflow-tooltip="" />
						<!-- <el-table-column prop="amountReceived" label="已收金额" show-overflow-tooltip="" />
        <el-table-column prop="invoicedAmount" label="已开票金额" show-overflow-tooltip="" /> -->
						<el-table-column prop="outOfStock" label="是否缺货" show-overflow-tooltip="">
							<template #default="scope">
								<el-tag v-if="scope.row.outOfStock"> 是 </el-tag>
								<el-tag type="danger" v-else=""> 否 </el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="notes" label="备注" show-overflow-tooltip="" />
						<!--      <el-table-column prop="annex" label="附件"  show-overflow-tooltip="" /> -->
						<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip="" v-if="auth('salesContract:edit') || auth('salesContract:delete')">
							<template #default="scope">
								<el-button icon="ele-Edit" size="small" text="" type="primary" @click="openEditSalesContract(scope.row)" v-auth="'salesContract:edit'"> 编辑 </el-button>
								<el-button icon="ele-Delete" size="small" text="" type="primary" @click="delSalesContract(scope.row)" v-auth="'salesContract:delete'"> 删除 </el-button>
							</template>
						</el-table-column>
					</el-table>
					<el-pagination
						v-model:currentPage="tableParams.page"
						v-model:page-size="tableParams.pageSize"
						:total="tableParams.total"
						:page-sizes="[10, 20, 50, 100]"
						small=""
						background=""
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
						layout="total, sizes, prev, pager, next, jumper"
					/>
					<editDialog ref="editDialogRef" :title="editSalesContractTitle" @reloadTable="handleQuery" />
					<editDialog4 ref="editDialogRef4" :title="editSalesContractTitle" @reloadTable="handleQuery" />
				</el-card>
			</pane>

			<pane :size="BomSize" ref="bomCardRef">
				<el-card class="bom-card"
					><!-- type="primary" icon="ele-Search" -->

					<el-tabs v-model="activeTab">
						<el-tab-pane label="商品明细" name="商品明细">
							<div>
								<editDialog2 ref="editDialogRef2" :title="editSalesContractTitle" @reloadTable="handleQuery" :bomHeight="bomHeight" :outOrderId="outOrderId" />
							</div>
						</el-tab-pane>
						<el-tab-pane label="履约计划" name="履约计划">
              <div>
                <editDialog1 
                  ref="editDialogRef1" 
                  :title="editSalesContractTitle" 
                  @reloadTable="handleQuery" 
                  :bomHeight="bomHeight" 
                  :outOrderId="outOrderId" 
                  :outOrder="outOrder"
                  :contractStatus="currentContractStatus" 
                />
              </div>
            </el-tab-pane>
						<el-tab-pane label="合同附件" name="合同附件" disabled>
							<div>
								<editDialog3 ref="editDialogRef3" :title="editSalesContractTitle" @reloadTable="handleQuery" :bomHeight="bomHeight" />
							</div>
						</el-tab-pane>
					</el-tabs>

					<!-- <el-button :class="{ active: index === cur }" v-for="(item, index) in tabList" :key="index"
        @click="toggleTab(index)">{{ item.name }}
      </el-button> 
       <div v-show="cur == 0">
        <editDialog1 ref="editDialogRef1" :title="editSalesContractTitle" @reloadTable="handleQuery" />
      </div>
      <div v-show="cur == 1">
        <editDialog2 ref="editDialogRef2" :title="editSalesContractTitle" @reloadTable="handleQuery" />
      </div>
      <div v-show="cur == 2">
        <editDialog3 ref="editDialogRef3" :title="editSalesContractTitle" @reloadTable="handleQuery" />
      </div> -->
				</el-card>
			</pane>
		</splitpanes>
	</div>
</template>
<script lang="ts"  name="salesContract" setup>
import { ref, onMounted, getCurrentInstance, watch } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { auth } from '/@/utils/authFunction';
//import { formatDate } from '/@/utils/formatTime';
import useCounter from '/@/stores/counter';
import editDialog from '/@/views/main/salesContract/component/editDialog.vue';
import { pageSalesContract, deleteSalesContract, updateSalesContract, Commitexamine, RevocationSalesContract } from '/@/api/main/salesContract';
import { addSalesContract } from '/@/api/main/salesContract';
import editDialog1 from '/@/views/main/salesperFormanceplan/index.vue';
import editDialog2 from '/@/views/main/saleOfGoods/index.vue';
import editDialog3 from '/@/views/main/salesAtta/index.vue';
import editDialog4 from '/@/views/main/salesContract/component/inherit.vue';
import { cwd } from 'process';
import { Splitpanes, Pane } from 'splitpanes';
import 'splitpanes/dist/splitpanes.css';
import ElementPlus from 'element-plus';

interface TableItem {
	row: {
		isSelected: boolean;
		[key: string]: any;
	};
}

const counterStore = useCounter();
const editDialogRef1 = ref();
const editDialogRef3 = ref();
const editDialogRef2 = ref();
const editDialogRef4 = ref();
const selectedRows = ref<any[]>([]);
const taskTableRef = ref();
const multipleSelection = ref<any>(null);
const batchmultipleSelection = ref<any[]>([]);
// 在 ref 定义区域添加
const currentContractStatus = ref<number>();
const topSize = ref('50%');
const BomSize = ref('50%');
const tableHeight = ref('280');
const bomHeight = ref('170');
const pagesInstance = getCurrentInstance();
const cur = ref(0);
// tabs切换文字信息
const tabList = ref([
	{
		name: '商品明细',
	},
	{
		name: '履约计划',
	},
	{
		name: '合同附件',
	},
]);
const orderDetail = ref('');
const outOrderId = ref('');
const outOrder = ref('');
const activeTab = ref('商品明细');
const route = useRoute();
const initialized = ref(false);

// 定义查询参数,添加contractStatus数组
const queryParams = ref<{
	contractStatus: number[];
	[key: string]: any;
}>({
	contractStatus: []
});

// 添加合同状态选项列表
const contractStatusList = ref([
	{ label: '洽谈', value: 0 },
	{ label: '待履约', value: 1 },
	{ label: '已签约', value: 2 },
	{ label: '履约中', value: 3 },
	{ label: '已完成', value: 4 },
	{ label: '中止', value: 5 }
]);

// 修改初始化过滤条件方法
const initializeFilters = () => {
	if (initialized.value) return;
	
	const statusFromRoute = route.query.contractStatus;
	if (statusFromRoute) {
		queryParams.value = {
			contractStatus: Array.isArray(statusFromRoute) 
				? statusFromRoute.map(Number)
				: typeof statusFromRoute === 'string' 
					? [Number(statusFromRoute)]
					: []
		};
	} else {
		queryParams.value = {
			contractStatus: []
		};
	}
	
	initialized.value = true;
};

// 修改 onMounted
onMounted(() => {
	initializeFilters();
	handleQuery();
});

// 优化路由监听，避免不必要的查询
watch(
	() => route.query,
	(newQuery, oldQuery) => {
		// 只在 contractStatus 发生变化时才重新查询
		if (initialized.value && 
			JSON.stringify(newQuery.contractStatus) !== JSON.stringify(oldQuery?.contractStatus)) {
			initializeFilters();
		}
	}
);

// 拖动改变表格高度
const handleTz = (value) => {
	topSize.value = value[0].size;
	BomSize.value = value[1].size;
	const height = window.innerHeight - 220;
	tableHeight.value = (parseFloat(pagesInstance.refs.topCardRef.style.height) / 100) * height - 80;
	bomHeight.value = (parseFloat(pagesInstance?.refs.bomCardRef.style.height) / 100) * height - 160;
};
const changeSelect = (selection: any, row: any) => {
	// debugger;
	batchmultipleSelection.value = selection;

	for (const row of tableData.value) {
		row.isSelected = false;
	}
	for (const selectedRow of selection) {
		const foundRow = tableData.value.find((row) => row.id === selectedRow.id);
		if (foundRow) {
			foundRow.isSelected = true;
		}
	}

	// multipleSelection.value = selection[selection.length - 1];
	// orderDetail.value = multipleSelection.value.salesOrder;
	// if (selection.length > 1) {
	//   let del_row = selection.shift();
	//   taskTableRef.value.toggleRowSelection(del_row, false);
	//   }
	//    editDialogRef2.value.handleQuery(multipleSelection.value.id);
	//    editDialogRef1.value.handleQuery(multipleSelection.value.id);
	//    editDialogRef3.value.handleQuery(multipleSelection.value.id);
	//    outOrderId.value = multipleSelection.value.id
	//    outOrder.value = multipleSelection.value.salesOrder
};
const rowClassName = (row: TableItem) => {
	return row.row.isSelected ? 'current-row' : '';
};

const isRouterAlive = ref(true);
const toggleTab = (index: any) => {
	//debugger;
	isRouterAlive.value = false;
	if (multipleSelection.value == null) {
		ElMessage.warning('请先选中要提交的记录');
	}
	switch (index) {
		case 1:
			cur.value = index;
			break;
		case 0:
			cur.value = index;
			break;
		case 2:
			cur.value = index;
			break;
		default:
			break;
	}
};

const tableData = ref<any>();
const editSalesContractTitle = ref('');
const editDialogRef = ref();
const loading = ref(false);

// 查询操作
const handleQuery = async (editedId?: number) => {
	loading.value = true;
	try {
		let contractStatusParam = '';
		if (queryParams.value.contractStatus?.length > 0) {
			contractStatusParam = queryParams.value.contractStatus.join(',');
		}

		const params = {
			...queryParams.value,
			contractStatus: contractStatusParam
		};
		
		const res = await pageSalesContract(Object.assign(params, tableParams.value));
		tableData.value = res.data.result?.items ?? [];
		tableParams.value.total = res.data.result?.total;
		
		// 查询完成后，如果有编辑的行ID，自动选中该行
		if (editedId && tableData.value.length > 0) {
			// 等待DOM更新后再选中行
			setTimeout(() => {
				const index = tableData.value.findIndex(item => item.id === editedId);
				if (index >= 0) {
					selectTableRow(index);
					handleRowClick(tableData.value[index]);
				}
			}, 100);
		}
	} finally {
		loading.value = false;
	}
};
// 重置查询条件
const resetQuery = () => {
	queryParams.value = {
		contractStatus: []
	};
	handleQuery();
};
// 打开新增页面
const openAddSalesContract = () => {
	editSalesContractTitle.value = '添加销售合约';
	editDialogRef.value.openDialog({});
};

// 打开编辑页面
const openEditSalesContract = (row: any) => {
	if (row.contractStatus != 0) {
		ElMessage.warning(row.salesOrder + ' 状态不正确，无法编辑');
		return;
	}
	editSalesContractTitle.value = '编辑销售合约';
	editDialogRef.value.openDialog(row);
};

// 删除
const delSalesContract = (row: any) => {
	if (row.contractStatus != 0) {
		ElMessage.warning(row.salesOrder + ' 状态不正确，无法删除');
		return;
	}
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await deleteSalesContract(row);
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};
let tableDataRow = ref<any>([]);
const handleRowClick = async (row: any) => {
	currentContractStatus.value = row.contractStatus;
	tableDataRow.value = row;
	editDialogRef2.value?.handleQuery(row.id);
	editDialogRef1.value?.handleQuery(row.id);
	editDialogRef3.value?.handleQuery(row.id);
	outOrderId.value = row.id;
	outOrder.value = row.salesOrder;
};
const selectTableRow = (index: number) => {
	if(taskTableRef.value) {
		taskTableRef.value.setCurrentRow(tableData.value[index]);
	}
};
// 改变页面容量
const handleSizeChange = (val: number) => {
	tableParams.value.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	tableParams.value.page = val;
	handleQuery();
};

// 打开新增页面
const openAddSalesperFormanceplan = () => {
	editSalesperFormanceplanTitle.value = '添加销售合约';
	editDialogRef.value.openDialog({});
};

// 打开编辑页面
const openEditSalesperFormanceplan = (row: any) => {
	editSalesperFormanceplanTitle.value = '编辑销售合约';
	editDialogRef.value.openDialog(row);
};

// 打开审核页面
const processSalesContract = (row: any) => {
	//editSalesperFormanceplanTitle.value = '编辑履约计划';
	//editDialogRef.value.openDialog(row);
	if (batchmultipleSelection.value.length == 0) {
		ElMessage.warning('请先选中要提交的记录');
		return;
	}
	ElMessageBox.confirm(`确定要提交吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const res = await Commitexamine(batchmultipleSelection.value);
			if (res.data.code != 200) {
				ElMessage.warning(res.data.message);
				return;
			}
			ElMessage.success('提交成功');
			handleQuery();
		})
		.catch(() => {});
};

// 打开复制页面
const copySalesContract = (row: any) => {
	row = multipleSelection.value;
	editSalesperFormanceplanTitle.value = '复制销售合约';
	editDialogRef.value.openDialog(row, '复制');
};

// 打印
const printSalesContract = (row: any) => {
	editSalesperFormanceplanTitle.value = '编辑销售合约';
	editDialogRef.value.openDialog(row);
};

//

// 中止
const discontinueAddSalesContract = (row: any) => {
	//debugger;
	const listPurchaseIds = batchmultipleSelection.value;
	/*   for (let i = 0; i < selectedRows.value.length; i++) {
      const item = selectedRows.value[i];
      listPurchaseIds.value.push(item);
    }
    // //debugger;
    if (listPurchaseIds.value.length == 0) {
      ElMessage.warning("请先选中要提交的记录");
      return;
    } */
	ElMessageBox.confirm(`确定要中止吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			//listPurchaseIds.contractStatus = 5;
			let values = listPurchaseIds;
			await updateSalesContract(values);
			ElMessage.success('中止成功');
			handleQuery();
		})
		.catch(() => {});
};

// 继承
const inheritSalesContract = (row: any) => {
	//debugger;
	let id = multipleSelection.value.id;
	/*   const listPurchaseIds = ref<number[]>([]); //if(index==1){
    for (let i = 0; i < selectedRows.value.length; i++) {
      id = selectedRows.value[i].id;
      listPurchaseIds.value.push(selectedRows.value[i]);
    }
    if (listPurchaseIds.value.length == 0) {
      ElMessage.warning("请先选中要提交的记录");
      return;
    } */
	editSalesperFormanceplanTitle.value = '继承销售合约';
	editDialogRef4.value.openDialog({ id });
};

// 删除
const delSalesperFormanceplan = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await deleteSalesperFormanceplan(row);
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};
// 点击标签页
// const handleClick = (tab) => {
//   if(tab.props.name == '合同合同附件'){

//   }
// };
// 撤回商品采购
const revocationButtonClick = () => {
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < batchmultipleSelection.value.length; i++) {
		const item = batchmultipleSelection.value[i];
		if (item.contractStatus == 2) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.salesOrder + ' 状态不正确，无法撤回');
			return;
		}
	}
	if (listPurchaseIds.value.length == 0) {
		ElMessage.warning('请先选中要撤回的记录');
		return;
	}
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要撤回吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await RevocationSalesContract(listPurchaseIds.value);
			handleQuery();
			ElMessage.success('撤回成功');
		})
		.catch(() => {});
};

const tableParams = ref({
	page: 1,
	pageSize: 10,
	total: 0
});
</script>


<style scoped lang='scss'>
// 隐藏全选按钮
:deep(.el-table th.el-table__cell:nth-child(1) .cell) {
	visibility: hidden;
}

.top-Card {
	margin: 8px 0;
	height: 100%;
}

.bom-card {
	height: 100%;
	overflow: auto;
}

@media (max-height: 870px) {
	.default-theme {
		height: calc(80vh - 50px) !important;
	}
}

@media (max-height: 766px) {
	.default-theme {
		height: calc(80vh - 80px) !important;
	}
}

@media (max-height: 640px) {
	.default-theme {
		height: calc(80vh - 140px) !important;
	}
}
</style>