﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.ChannelsECWarehouseGetResponse.Types;

namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 商品出库表
    /// </summary>
    [SugarTable("warehouseout","商品出库表")]
    [Tenant("1300000000001")]
    public class Warehouseout  : EntityTenant
    {
        /// <summary>
        /// 出库单号
        /// </summary>
        [SugarColumn(ColumnDescription = "出库单号", Length = 20)]
        public string? OutOrder { get; set; }
        /// <summary>
        /// 客户Id
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "客户Id")]
        public long CustomId { get; set; }
        /// <summary>
        /// 仓库
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(CustomId))]
        public Pubcustom Pubcustoms { get; set; }
        /// <summary>
        /// 出库数量
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "出库数量")]
        public int OutCount { get; set; }
        /// <summary>
        /// 实际出库数量
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "实际出库数量")]
        public int TrueOutCount { get; set; }
        /// <summary>
        /// 物流单号
        /// </summary>
        [SugarColumn(ColumnDescription = "物流单号", Length = 50)]
        public string? TrackingNumber { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 255)]
        public string? Remark { get; set; }

        /// <summary>
        /// 出库状态
        /// </summary>
        [SugarColumn(ColumnDescription = "出库状态")]
        public int OutboundStatus { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        public long WarehouseId { get; set; }

        /// <summary>
        /// 上级单号
        /// </summary>
        public string? SuperiorNum { get; set; }

        /// <summary>
        /// 出库类型
        /// </summary>
        public int? Outboundtype { get; set; }
        /// <summary>
        /// 商品信息
        /// </summary>
        [SugarColumn(ColumnDescription = "商品信息", Length = 1000)]
        public string? GoodsInfo { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        [SugarColumn(ColumnDescription = "总金额")]
        public decimal? TotalAmt { get; set; }

        /// <summary>
        /// 优惠金额
        /// </summary>
        [SugarColumn(ColumnDescription = "优惠金额")]
        public decimal? DiscountAmt { get; set; }

        /// <summary>
        /// 实际金额
        /// </summary>
        [SugarColumn(ColumnDescription = "实际金额")]
        public decimal? ActualAmt { get; set; }
    }
}