﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 公用序号
    /// </summary>
    [SugarTable("puborder", "公用序号")]
    [Tenant("1300000000001")]
    public class PubOrder
    {

        /// <summary>
        /// 前缀
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "前缀", IsPrimaryKey = true, Length = 10)]
        public string Prefix { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "备注", Length = 20)]
        public string Remark { get; set; }

        /// <summary>
        /// 租户Id
        /// </summary>
        [SugarColumn(ColumnDescription = "租户Id", IsPrimaryKey = true)]//, IsOnlyIgnoreUpdate = true
        public long? TenantId { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "序号")]
        public int SN { get; set; }
    }
}