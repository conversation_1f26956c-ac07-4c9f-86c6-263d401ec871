﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 加工单配置产出
    /// </summary>
    [SugarTable("ProcessOrderSchemeProduce","")]
    [Tenant("1300000000001")]
    public class ProcessOrderSchemeProduce  : EntityBaseId
    {
        /// <summary>
        /// 加工单配置Id
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "加工单配置Id")]
        public long ProcessOrderSchemeId { get; set; }
        /// <summary>
        /// 产品商品id
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "产品商品id")]
        public long WarehouseGoodsId { get; set; }

        /// <summary>
        /// 商品
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(WarehouseGoodsId))]
        public Warehousegoods Warehousegoods { get; set; }
       
        /// <summary>
        /// 数量
        /// </summary>
        [SugarColumn(ColumnDescription = "数量")]
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [SugarColumn(ColumnDescription = "单价")]
        public decimal? UnitPrice { get; set; }
    }
}