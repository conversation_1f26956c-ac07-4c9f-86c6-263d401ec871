{"name": "vue-next-admin", "version": "2.4.33", "description": "vue3 vite next admin template", "author": "lyt_20201208", "license": "MIT", "scripts": {"dev": "vite", "build": "node --max_old_space_size=1024000 ./node_modules/vite/bin/vite.js build", "lint-fix": "eslint --fix --ext .js --ext .jsx --ext .vue src/"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@microsoft/signalr": "^6.0.10", "@sv-print/vue3": "^0.1.8", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "animate.css": "^4.1.1", "axios": "^1.7.9", "cnpm": "^9.4.0", "countup.js": "^2.6.0", "cropperjs": "^1.5.13", "echarts": "^5.4.2", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.3.3", "js-cookie": "^3.0.1", "js-table2excel": "^1.0.3", "jsplumb": "^2.15.6", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "moment": "^2.29.4", "monaco-editor": "^0.38.0", "nprogress": "^0.2.0", "pinia": "^2.0.34", "pnpm": "^8.6.0", "print-js": "^1.6.0", "qrcodejs2-fixes": "^0.0.2", "qs": "^6.11.1", "screenfull": "^6.0.2", "sortablejs": "^1.15.0", "splitpanes": "^3.1.5", "sv-print": "^0.1.8", "vform3-builds": "^3.0.8", "vite": "^5.3.5", "vite-plugin-vue-setup-extend": "^0.4.0", "vue": "^3.2.47", "vue-clipboard3": "^2.0.0", "vue-demi": "^0.13.11", "vue-grid-layout": "^3.0.0-beta1", "vue-i18n": "^9.2.2", "vue-json-pretty": "^2.2.3", "vue-plugin-hiprint": "0.0.57-beta27", "vue-router": "^4.1.6", "vue-signature-pad": "^3.0.2", "vue-splitpane": "^1.0.6", "vue3-tree-org": "^4.1.1", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@types/lodash-es": "^4.17.7", "@types/node": "^18.15.11", "@types/nprogress": "^0.2.0", "@types/sortablejs": "^1.15.1", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "@vitejs/plugin-vue": "^4.1.0", "@vue/compiler-sfc": "^3.2.47", "eslint": "^8.38.0", "eslint-plugin-vue": "^9.10.0", "increase-memory-limit": "^1.0.7", "less": "^4.2.0", "prettier": "^2.8.7", "sass": "^1.61.0", "typescript": "^5.0.4", "vite": "^4.2.1", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-eslint-parser": "^9.1.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": "https://gitee.com/lyt-top/vue-next-admin/issues"}, "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus", "vue-next-admin", "next-admin"], "repository": {"type": "git", "url": "https://gitee.com/lyt-top/vue-next-admin.git"}}