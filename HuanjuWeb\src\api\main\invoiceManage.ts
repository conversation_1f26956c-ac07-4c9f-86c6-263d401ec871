﻿import request from '/@/utils/request';
enum Api {
	AddInvoiceManage = '/api/invoiceManage/add',
	DeleteInvoiceManage = '/api/invoiceManage/delete',
	UpdateInvoiceManage = '/api/invoiceManage/update',
	PageInvoiceManage = '/api/invoiceManage/page',
	pullInvoiceManage = '/api/invoiceManage/pull',
	downloadInvoiceManage = '/api/invoiceManage/download',
	SendCaptchaManage = '/api/InvoiceManage/sendCaptcha',
	sendLoginDppt = '/api/invoiceManage/loginDppt',
	jumpToInvoice = '/api/invoiceManage/jumpToInvoice'
}

// 增加发票管理
export const addInvoiceManage = (params?: any) =>
	request({
		url: Api.AddInvoiceManage,
		method: 'post',
		data: params,
	});

// 删除发票管理
export const deleteInvoiceManage = (params?: any) =>
	request({
		url: Api.DeleteInvoiceManage,
		method: 'post',
		data: params,
	});

// 编辑发票管理
export const updateInvoiceManage = (params?: any) =>
	request({
		url: Api.UpdateInvoiceManage,
		method: 'post',
		data: params,
	});

// 分页查询发票管理
export const pageInvoiceManage = (params?: any) =>
	request({
		url: Api.PageInvoiceManage,
		method: 'post',
		data: params,
	});
// 发票同步
export const pullInvoiceManage = (params?: any) =>
	request({
		url: Api.pullInvoiceManage,
		method: 'post',
		data: params,
	});
// 发票下载
export const downloadInvoiceManage = (params?: any) =>
	request({
		url: Api.downloadInvoiceManage,
		method: 'post',
		data: params,
	});
// 发送验证码
export const SendCaptchaManage = (params?: any) =>
	request({
		url: Api.SendCaptchaManage,
		method: 'get'
	});
// d单独登录
export const sendLoginDppt = (params?: any) =>
	request({
		url: Api.sendLoginDppt,
		method: 'post',
		data: params,
	});

//开票跳转
export const jumpToInvoice = (params?: any) =>
	request({
		url: Api.jumpToInvoice,
		method: 'post',
		data: params,
	});
