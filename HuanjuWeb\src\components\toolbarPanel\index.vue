<template>
    <el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="localQueryParams" ref="queryForm" :inline="true">
				<!-- 通过props动态渲染表单项 -->
				<el-form-item v-for="(item, index) in formItems" :key="index" :label="item.label">
					<!-- 输入框类型 -->
					<el-input 
						v-if="item.type === 'input'" 
						v-model="localQueryParams[item.prop]" 
						:clearable="item.clearable !== false" 
						:placeholder="item.placeholder || '请输入' + item.label" 
						:class="item.className || 'w120'" 
					/>
					
					<!-- 日期选择器类型 -->
					<el-date-picker 
						v-else-if="item.type === 'daterange'" 
						:placeholder="item.placeholder || '请选择' + item.label" 
						value-format="YYYY/MM/DD" 
						type="daterange" 
						v-model="localQueryParams[item.prop]" 
						:style="item.style || 'width: 250px'" 
					/>
					
					<!-- 下拉选择类型 -->
					<el-select 
						v-else-if="item.type === 'select'" 
						v-model="localQueryParams[item.prop]" 
						:multiple="item.multiple" 
						:placeholder="item.placeholder || '请选择' + item.label" 
						:clearable="item.clearable !== false"
					>
						<el-option
							v-for="option in item.options"
							:key="option.value"
							:label="option.label"
							:value="option.value"
						/>
					</el-select>
				</el-form-item>
				
				<!-- 查询和重置按钮 -->
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'salesContract:page'"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
				</el-form-item>
				
				<!-- 操作按钮组 -->
				<el-form-item>
					<el-button v-for="(btn, idx) in operationButtons" :key="idx" 
						:type="btn.type || 'primary'" 
						:icon="btn.icon" 
						@click="btn.handler" 
						v-auth="btn.auth">
						{{ btn.text }}
					</el-button>
				</el-form-item>
			</el-form>
		</el-card>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, reactive, watch, onMounted } from 'vue';

// 定义props
const props = defineProps({
  // 表单项配置
  formItems: {
    type: Array as () => FormItem[],
    required: true
  },
  // 操作按钮配置
  operationButtons: {
    type: Array as () => OperationButton[],
    default: () => []
  },
  // 查询参数
  queryParams: {
    type: Object,
    required: true
  }
});

// 创建本地查询参数副本
const localQueryParams = reactive({...props.queryParams});

// 监听props变化更新本地数据
watch(() => props.queryParams, (newVal) => {
  Object.assign(localQueryParams, newVal);
}, { deep: true });

// 定义事件
const emit = defineEmits(['query', 'reset', 'update:queryParams']);

// 查询方法
const handleQuery = () => {
  // 同步回父组件
  emit('update:queryParams', localQueryParams);
  emit('query', localQueryParams);
};

// 重置方法
const resetQuery = () => {
  // 重置本地数据
  Object.keys(localQueryParams).forEach(key => {
    localQueryParams[key] = undefined;
  });
  // 同步回父组件
  emit('update:queryParams', localQueryParams);
  emit('reset');
};
</script>

<style scoped>
.query-form {
  height: auto;
}

.query-form :deep(.el-card__body) {
  padding-bottom: 0;
  height: auto;
}
</style>