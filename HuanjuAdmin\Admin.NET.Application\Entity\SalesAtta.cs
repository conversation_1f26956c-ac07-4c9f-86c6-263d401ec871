﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("SalesAtta","")]
    [Tenant("1300000000001")]
    public class SalesAtta  : EntityBase
    {
        /// <summary>
        /// 附件地址
        /// </summary>
        [SugarColumn(ColumnDescription = "附件地址", Length = 500)]
        public string? Address { get; set; }
        /// <summary>
        /// 销售ID
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "销售单号")]
        public string SalesID { get; set; }
        /// <summary>
        /// 附件名称
        /// </summary>
        [SugarColumn(ColumnDescription = "附件名称", Length = 200)]
        public string? AttaName { get; set; }
    }
}