﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;

namespace Admin.NET.Application;
/// <summary>
/// 入库记录服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class InboundRecordService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<InboundRecord> _rep;
    public InboundRecordService(SqlSugarRepository<InboundRecord> rep)
    {
        _rep = rep;
    }

    /// <summary>
    /// 分页查询入库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<InboundRecordOutput>> Page(InboundRecordInput input)
    {
        var query= _rep.AsQueryable()
                    .WhereIF(input.Id>0, u => u.Id == input.Id)
                    .WhereIF(input.WarehouseIncordMxId>0, u => u.WarehouseIncordMxId == input.WarehouseIncordMxId)
                    .WhereIF(input.InBoundCount>0, u => u.InBoundCount == input.InBoundCount)
                    .WhereIF(input.PrintCount>0, u => u.PrintCount == input.PrintCount)
                    .WhereIF(input.WarehouseBatchId>0, u => u.WarehouseBatchId == input.WarehouseBatchId)

                    .Select<InboundRecordOutput>()
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加入库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddInboundRecordInput input)
    {
        var entity = input.Adapt<InboundRecord>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除入库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteInboundRecordInput input)
    {
        var entity = input.Adapt<InboundRecord>();
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新入库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateInboundRecordInput input)
    {
        var entity = input.Adapt<InboundRecord>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取入库记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<InboundRecord> Get([FromQuery] QueryByIdInboundRecordInput input)
    {
        return await _rep.GetByIdAsync(input.Id);
    }

    /// <summary>
    /// 获取入库记录列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<InboundRecordOutput>> List([FromQuery] InboundRecordInput input)
    {
        return await _rep.AsQueryable().Select<InboundRecordOutput>().ToListAsync();
    }

}

