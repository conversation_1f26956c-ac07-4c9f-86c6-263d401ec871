<template>
	<div class="paymentOrder-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="500" draggable="" :close-on-click-modal="false">
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" :rules="rules">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20" class="mb20">
						<el-form-item label="发票类型" prop="invoiceType">
							<el-select v-model="ruleForm.invoiceType" placeholder="请选择发票类型" clearable>
								<el-option v-for="item in invoiceTypes" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
		<custom-dialog v-model="isShowDialogZY" :redirect-url="redirectUrl" @dialogClosed="handleDialogClosed" />
	</div>
</template>
  
  <script lang="ts" setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormRules } from 'element-plus';
import { jumpToInvoice } from '/@/api/main/invoiceManage';
import CustomDialog from './CustomDialog.vue';

const props = defineProps({
	title: {
		type: String,
		default: '',
	},
});

const emit = defineEmits(['reloadTable']);

const ruleFormRef = ref();
const isShowDialog = ref(false);
const isShowDialogZY = ref(false);
const loading = ref(false);
const ruleForm = ref<any>({});
const selectedIds = ref<number[]>([]);
const redirectUrl = ref('');

const invoiceTypes = [
	{ value: '81', label: '数电发票（增值税专用发票）' },
	{ value: '82', label: '数电发票（普通发票）' },
	{ value: '85', label: '数电纸质发票（增值税专用发票）' },
	{ value: '86', label: '数电纸质发票（普通发票）' },
];

const rules = ref<FormRules>({
	invoiceType: [{ required: true, message: '请选择发票类型', trigger: 'change' }],
});

// 打开弹窗
const openDialog = (ids: number[], row: any = {}) => {
	selectedIds.value = ids;
	ruleForm.value = JSON.parse(JSON.stringify(row));
	isShowDialog.value = true;
};

const closeDialog = () => {
	emit('reloadTable');
	isShowDialog.value = false;
	ruleForm.value = {};
	selectedIds.value = [];
	loading.value = false;
};

const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
	ruleForm.value = {};
	selectedIds.value = [];
};

const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			loading.value = true;
			try {
				const params = {
					listIds: selectedIds.value,
					invoiceType: ruleForm.value.invoiceType,
				};
				const response = await jumpToInvoice(params);

				console.log('Response:', response); // 添加调试日志

				// 检查 response.result 是否为有效的 URL
				if (response && response.data && response.data.result && typeof response.data.result === 'string') {
					redirectUrl.value = response.data.result;
					isShowDialogZY.value = true;
				} else {
					console.error('Invalid response.result:', response); // 添加调试日志
				}
			} catch (error) {
				loading.value = false;
				console.error('Error during submission:', error); // 添加调试日志
				// ElMessage.error('操作失败');
			}
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: 'error',
			});
		}
	});
};

const handleDialogClosed = () => {
	loading.value = false;
	redirectUrl.value = '';
	isShowDialogZY.value = false;
	closeDialog();
};

defineExpose({ openDialog });
</script>
  
  <style scoped>
.dialog-footer {
	padding-top: 20px;
	text-align: right;
}
.mb20 {
	margin-bottom: 20px;
}
</style>