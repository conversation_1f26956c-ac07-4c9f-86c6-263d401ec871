<template>
	<div>
		<el-form ref="ruleFormRef" :model="state.ruleForm" size="large" :rules="state.rules" class="login-content-form">
			<el-form-item class="login-animation1" prop="account">
				<el-input ref="accountRef" text placeholder="请输入账号" v-model="state.ruleForm.account" clearable
					autocomplete="off" @keyup.enter="handleSignIn">
					<template #prefix>
						<el-icon><ele-User /></el-icon>
					</template>
				</el-input>
			</el-form-item>

			<el-form-item class="login-animation2" prop="password">
				<el-input ref="passwordRef" :type="state.isShowPassword ? 'text' : 'password'" placeholder="请输入密码"
					v-model="state.ruleForm.password" autocomplete="off" @keyup.enter="handleSignIn">
					<template #prefix>
						<el-icon><ele-Unlock /></el-icon>
					</template>
					<template #suffix>
						<i class="iconfont el-input__icon login-content-password"
							:class="state.isShowPassword ? 'icon-yincangmima' : 'icon-xianshimima'"
							@click="state.isShowPassword = !state.isShowPassword"></i>
					</template>
				</el-input>
			</el-form-item>

			<el-form-item class="login-animation3" prop="captcha" v-show="state.captchaEnabled">
				<el-col :span="15">
					<el-input ref="codeRef" text maxlength="4" :placeholder="$t('message.account.accountPlaceholder3')"
						v-model="state.ruleForm.code" clearable autocomplete="off" @keyup.enter="handleSignIn">
						<template #prefix>
							<el-icon><ele-Position /></el-icon>
						</template>
					</el-input>
				</el-col>
				<el-col :span="1"></el-col>
				<el-col :span="8">
					<div class="login-content-code">
						<img class="login-content-code-img" @click="getCaptcha" width="130px" height="38px"
							:src="state.captchaImage" style="cursor: pointer" />
					</div>
				</el-col>
			</el-form-item>

			<el-form-item class="login-animation4">
				<el-button type="primary" class="login-content-submit" round v-waves @click="handleSignIn"
					:loading="state.loading.signIn">
					<span>{{ $t('message.account.accountBtnText') }}</span>
				</el-button>
			</el-form-item>

			<div class="font12 mt30 login-animation4 login-msg">
				{{ $t('message.mobile.msgText') }}
			</div>
		</el-form>

		<div class="dialog-header">
			<el-dialog v-model="state.rotateVerifyVisible" :show-close="false" :close-on-click-modal="false">
				<DragVerifyImgRotate ref="dragRef" :imgsrc="state.rotateVerifyImg"
					v-model:isPassing="state.isPassRotate" text="请按住滑块拖动" successText="验证通过"
					handlerIcon="fa fa-angle-double-right" successIcon="fa fa-hand-peace-o"
					@passcallback="passRotateVerify" />
			</el-dialog>
		</div>
	</div>
</template>

<script lang="ts" setup name="loginAccount">
import { reactive, computed, ref, onMounted, defineAsyncComponent, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, InputInstance, FormInstance } from 'element-plus';
import { useI18n } from 'vue-i18n';
import { initBackEndControlRoutes } from '/@/router/backEnd';
import { Session,Local } from '/@/utils/storage';
import { formatAxis } from '/@/utils/formatTime';
import { NextLoading } from '/@/utils/loading';
import { clearTokens, feature, getAPI } from '/@/utils/axios-utils';
import { SysAuthApi } from '/@/api-services/api';
import _ from 'lodash-es';

// 旋转图片滑块组件
import verifyImg from '/@/assets/logo-mini.svg';
const DragVerifyImgRotate = defineAsyncComponent(() => import('/@/components/dragVerify/dragVerifyImgRotate.vue'));

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// Refs
const ruleFormRef = ref<FormInstance>();
const accountRef = ref<InputInstance>();
const passwordRef = ref<InputInstance>();
const codeRef = ref<InputInstance>();
const dragRef = ref();

// State
const state = reactive({
	isShowPassword: false,
	ruleForm: {
		account: '',
		password: '',
		code: '',
		codeId: 0,
	},
	rules: {
		account: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
		password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
	},
	loading: {
		signIn: false,
	},
	captchaImage: '',
	rotateVerifyVisible: false,
	rotateVerifyImg: verifyImg,
	secondVerEnabled: false,
	captchaEnabled: false,
	isPassRotate: false,
});

// 初始化
onMounted(async () => {
	clearTokens(); // 清除旧的token
	await initLoginConfig();
	await getCaptcha();
	nextTick(() => {
		handleThirdPartyLogin();
	})
});

// 获取登录配置
const initLoginConfig = async () => {
	try {
		const res = await getAPI(SysAuthApi).apiSysAuthLoginConfigGet();
		state.secondVerEnabled = res.data.result?.secondVerEnabled ?? true;
		state.captchaEnabled = res.data.result?.captchaEnabled ?? true;
	} catch (error) {
		console.error('Failed to get login config:', error);
	}
};

// 获取验证码
const getCaptcha = async () => {
	try {
		state.ruleForm.code = '';
		const res = await getAPI(SysAuthApi).apiSysAuthCaptchaGet();
		state.captchaImage = 'data:text/html;base64,' + res.data.result?.img;
		state.ruleForm.codeId = res.data.result?.id;
	} catch (error) {
		console.error('Failed to get captcha:', error);
	}
};

// 获取时间
const currentTime = computed(() => {
	return formatAxis(new Date());
});

// 登录处理
const onSignIn = async () => {
	if (!ruleFormRef.value) return;

	try {
		await ruleFormRef.value.validate();

		state.loading.signIn = true;
		const [err, res] = await feature(getAPI(SysAuthApi).apiSysAuthLoginPost(state.ruleForm));

		if (err) {
			await getCaptcha();
			return;
		}

		if (!res?.data.result?.accessToken) {
			await getCaptcha();
			ElMessage.error('登录失败，请检查账号！');
			return;
		}

		// 保存token
		Session.set('token', res.data.result.accessToken);
		// 🔧 修复：同时保存到LocalStorage，确保API请求能获取到token
		Local.set('access-token', res.data.result.accessToken);
		
		// 保存RefreshToken（如果有）
		if (res.data.result.refreshToken) {
			Local.set('x-access-token', res.data.result.refreshToken);
		}

		// 初始化路由
		const isNoPower = await initBackEndControlRoutes();
		await signInSuccess(isNoPower);

	} catch (error) {
		console.error('Sign in error:', error);
		ElMessage.error(error instanceof Error ? error.message : '登录失败');
	} finally {
		state.loading.signIn = false;
	}
};

// 处理第三方登录Token
const handleThirdPartyLogin = async () => {
	const params = route.query.params;
	const redirect = route.query.redirect;

	// 🔧 修复：第三方登录应该在路由守卫中统一处理，这里只需要检查是否需要清理URL
	if (params && typeof params === 'string') {
		console.log('⚠️ [登录页面] 检测到URL中仍有第三方登录参数，但Token处理应该已在路由守卫完成');

		const tokenData = JSON.parse(decodeURIComponent(params));
		if (tokenData.token && tokenData.refreshToken) {
			console.log('🔑 [路由守卫] 检测到第三方登录Token，正在处理...');

			// 保存Token到焕炬系统的Cookie存储
			Session.set('token', tokenData.token);
			Session.set('refreshToken', tokenData.refreshToken);
			Local.set("access-token", tokenData.token);

			// 初始化路由
			const isNoPower = await initBackEndControlRoutes();

			await signInSuccess(isNoPower, redirect ? decodeURIComponent(redirect) : '/');
			return;
		}
	}

	console.log('ℹ️ 没有检测到第三方登录Token，显示正常登录表单');
};

// 登录成功处理
const signInSuccess = async (isNoPower: boolean, redirect?: string) => {
	if (isNoPower) {
		ElMessage.warning('抱歉，您没有登录权限');
		clearTokens();
		return;
	}

	// 跳转处理
	const currentTimeInfo = currentTime.value;

	await router.push(redirect ? String(redirect) : '/');
	ElMessage.success(`${currentTimeInfo}，${t('message.signInText')}`);
	NextLoading.start();
};

// 打开旋转验证
const openRotateVerify = () => {
	state.rotateVerifyVisible = true;
	state.isPassRotate = false;
	dragRef.value?.reset();
};

// 通过旋转验证
const passRotateVerify = () => {
	state.rotateVerifyVisible = false;
	state.isPassRotate = true;
	onSignIn();
};

// 登录验证
const handleSignIn = _.debounce(async () => {
	if (!state.ruleForm.account) {
		accountRef.value?.focus();
		return;
	}

	if (!state.ruleForm.password) {
		passwordRef.value?.focus();
		return;
	}

	if (state.captchaEnabled && !state.ruleForm.code) {
		codeRef.value?.focus();
		return;
	}

	state.secondVerEnabled ? openRotateVerify() : onSignIn();
}, 300, { leading: true, trailing: false });

</script>

<style lang="scss" scoped>
.dialog-header {
	:deep(.el-dialog) {
		.el-dialog__header {
			display: none;
		}

		.el-dialog__wrapper {
			position: absolute !important;
		}

		.v-modal {
			position: absolute !important;
		}

		width: unset !important;
	}
}

.login-content-form {
	margin-top: 20px;

	@for $i from 0 through 4 {
		.login-animation#{$i} {
			opacity: 0;
			animation-name: error-num;
			animation-duration: 0.5s;
			animation-fill-mode: forwards;
			animation-delay: calc($i/10) + s;
		}
	}

	.login-content-password {
		display: inline-block;
		width: 20px;
		cursor: pointer;

		&:hover {
			color: #909399;
		}
	}

	.login-content-code {
		display: flex;
		align-items: center;
		justify-content: space-around;

		.login-content-code-img {
			width: 100%;
			height: 40px;
			line-height: 40px;
			background-color: #ffffff;
			border: 1px solid rgb(220, 223, 230);
			cursor: pointer;
			transition: all ease 0.2s;
			border-radius: 4px;
			user-select: none;

			&:hover {
				border-color: #c0c4cc;
				transition: all ease 0.2s;
			}
		}
	}

	.login-content-submit {
		width: 100%;
		letter-spacing: 2px;
		font-weight: 300;
		margin-top: 15px;
	}

	.login-msg {
		color: var(--el-text-color-placeholder);
	}
}
</style>