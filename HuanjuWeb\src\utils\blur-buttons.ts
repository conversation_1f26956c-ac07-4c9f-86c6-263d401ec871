import { App } from 'vue';

export function setupButtonBlur(app: App) {
  app.mixin({
    mounted() {
      this.$nextTick(() => {
        const buttons = this.$el.querySelectorAll ? this.$el.querySelectorAll('.el-button') : [];
        buttons.forEach((btn: HTMLElement) => {
          btn.addEventListener('click', () => {
            setTimeout(() => {
              btn.blur();
            }, 50);
          });
        });
      });
    }
  });
} 