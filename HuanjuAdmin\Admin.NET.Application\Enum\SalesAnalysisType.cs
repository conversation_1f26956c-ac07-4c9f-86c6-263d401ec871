﻿// MIT License
//
// Copyright (c) 2021-present <PERSON><PERSON><PERSON><PERSON><PERSON>, Daming Co.,Ltd and Contributors
//
// 电话/微信：18020030720 QQ群1：87333204 QQ群2：252381476

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Enum;

[Description("销售分析枚举")]
public enum SalesAnalysisType
{

    /// <summary>
    /// 商品
    /// </summary>
    [Description("商品")]
    Commodity = 0,

    /// <summary>
    /// 客户
    /// </summary>
    [Description("客户")]
    Custom = 1,

    /// <summary>
    /// 销售
    /// </summary>
    [Description("销售")]
    Sale = 2,
}
