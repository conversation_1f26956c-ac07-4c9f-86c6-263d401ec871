# 出入库明细良品次品区分功能实现说明

## 功能概述

本次更新为出入库明细系统添加了良品次品区分功能，并完善了出入库红冲时根据良品次品走不同逻辑的处理机制。

## 主要修改内容

### 1. 数据库实体类修改

#### 1.1 WarehouseInrecordMX（入库明细表）
- **新增字段**：
  - `GoodProduct` (bool): 是否良品，默认值为 true
  - 实际入库数量使用现有的 `RcvQty` 字段，无需新增 `TrueInCount` 字段

#### 1.2 OutInBoundRecord（出入库记录视图）
- **新增字段**：
  - `GoodProduct` (bool?): 是否良品

#### 1.3 OutboundRecord（出库记录表）
- **新增字段**：
  - `GoodProduct` (bool?): 是否良品

#### 1.4 InboundRecord（入库记录表）
- **新增字段**：
  - `GoodProduct` (bool?): 是否良品

### 2. DTO类修改

#### 2.1 输出DTO类
- `OutInBoundOutput`: 添加 `GoodProduct` 和 `ProductTypeDesc` 字段
- `OutboundRecordOutput`: 添加 `GoodProduct` 和 `ProductTypeDesc` 字段
- `InboundRecordOutput`: 添加 `GoodProduct` 和 `ProductTypeDesc` 字段
- `WarehouseoutMXOutput`: 添加 `GoodProduct` 和 `ProductTypeDesc` 字段
- `WarehouseInrecordMXDto`: 添加 `GoodProduct` 和 `ProductTypeDesc` 字段，实际入库数量使用现有的 `RcvQty` 字段

#### 2.2 输入DTO类
- `OutboundRecordBaseInput`: 添加 `GoodProduct` 字段
- `InboundRecordBaseInput`: 添加 `GoodProduct` 字段
- `WarehouseInrecordMXBaseInput`: 添加 `GoodProduct` 字段，实际入库数量使用现有的 `RcvQty` 字段

### 3. 业务逻辑修改

#### 3.1 出库红冲逻辑优化
在 `OutboundRecordService.RedInkOutboundRecord` 方法中：
- 红冲记录保持与原记录相同的良品次品标识
- 备注中包含产品类型信息
- 根据良品次品类型分别处理库存和批次信息

#### 3.2 入库红冲逻辑完善
在 `OutboundRecordService.RedInkInboundRecord` 方法中：
- 恢复了完整的入库红冲功能（之前被注释）
- 红冲记录保持与原记录相同的良品次品标识
- 备注中包含产品类型信息
- 根据良品次品类型分别处理库存和批次信息

## 问题修复记录

### 修复日期：2024年

#### 问题描述
入库无论选择良品还是次品，出入库记录都显示次品的问题。

#### 根本原因
1. **入库记录创建时缺少GoodProduct字段设置**：
   - 在 `WarehouseStoreService.InsertBatchOutInBound` 方法中，创建 `InboundRecord` 时没有设置 `GoodProduct` 字段
   - 导致所有入库记录的 `GoodProduct` 字段都为默认值（null），在显示时被解释为次品

2. **出库记录创建时缺少GoodProduct字段设置**：
   - 在 `WarehouseoutService.Outbound` 方法中，创建 `OutboundRecord` 时没有设置 `GoodProduct` 字段
   - 导致所有出库记录的 `GoodProduct` 字段都为默认值（null），在显示时被解释为次品

3. **字段名不一致问题**：
   - `WarehouseoutMXOutput` DTO类中使用了 `goodProduct`（小写开头）
   - 实体类中使用的是 `GoodProduct`（大写开头）
   - 前端代码中也使用了 `goodProduct`
   - 导致字段映射失败

#### 修复内容

1. **修复入库记录创建逻辑**：
   ```csharp
   // 在 WarehouseStoreService.InsertBatchOutInBound 方法中添加
   inBoundRecord.GoodProduct = isProduct; // 设置良品次品标识
   ```

2. **修复出库记录创建逻辑**：
   ```csharp
   // 在 WarehouseoutService.Outbound 方法中添加
   outboundRecord.GoodProduct = item.GoodProduct; // 设置良品次品标识
   ```

3. **统一字段名**：
   - 将 `WarehouseoutMXOutput.goodProduct` 改为 `GoodProduct`
   - 将前端代码中的 `goodProduct` 改为 `GoodProduct`
   - 将 `WarehouseStoreService` 中的查询映射字段名统一为 `GoodProduct`

#### 修复后的数据流程

1. **入库流程**：
   - 用户在入库明细中选择良品/次品 → `WarehouseInrecordMX.GoodProduct` 字段
   - 入库操作时创建入库记录 → `InboundRecord.GoodProduct` 字段正确设置
   - 出入库记录视图正确显示良品/次品状态

2. **出库流程**：
   - 用户在出库明细中选择良品/次品 → `WarehouseoutMX.GoodProduct` 字段
   - 出库操作时创建出库记录 → `OutboundRecord.GoodProduct` 字段正确设置
   - 出入库记录视图正确显示良品/次品状态

3. **红冲流程**：
   - 红冲时保持与原记录相同的良品次品标识
   - 备注中包含详细的产品类型信息

#### 测试验证
添加了完整的单元测试用例：
- 测试入库记录GoodProduct字段设置
- 测试出库记录GoodProduct字段设置
- 测试字段名一致性
- 测试ProductTypeDesc属性计算逻辑

### 4. 前端界面修改

#### 4.1 出入库记录页面
- 添加产品类型列，显示良品/次品标识
- 使用不同颜色的标签区分良品（绿色）和次品（橙色）

#### 4.2 红冲功能
- 支持出库记录红冲
- 支持入库记录红冲
- 红冲时保持原记录的良品次品属性

## 数据库迁移

执行 `AddGoodProductFields.sql` 脚本完成数据库结构升级：
1. 为相关表添加 `GoodProduct` 字段
2. 更新现有数据的良品次品标识
3. 重建出入库记录视图

## 使用说明

### 1. 入库操作
- 在入库明细中可以选择商品是否为良品
- 良品默认为 true，次品设置为 false
- 入库后会在出入库记录中正确显示产品类型

### 2. 出库操作
- 在出库明细中可以选择出库商品是否为良品
- 系统会根据库存中的良品/次品数量进行验证
- 出库后会在出入库记录中正确显示产品类型

### 3. 红冲操作
- 在出入库记录页面选择记录进行红冲
- 红冲记录会保持与原记录相同的良品次品标识
- 红冲会正确调整库存中的良品/次品数量

### 4. 库存管理
- 库存表分别记录良品数量和次品数量
- 可配数只计算良品库存
- 支持良品次品之间的转换

## 注意事项

1. **数据一致性**：确保入库明细、出入库记录、库存数据中的良品次品标识保持一致
2. **字段命名**：所有相关字段统一使用 `GoodProduct`（大写开头）命名
3. **前后端同步**：前端字段名必须与后端DTO保持一致
4. **测试覆盖**：每次修改后都要进行完整的功能测试，确保良品次品显示正确
