﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;

namespace Admin.NET.Application;
/// <summary>
/// 交易账户服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class TradingAccountsService : IDynamicApiController, ITransient
{
    private readonly UserManager _userManager;
    private readonly SqlSugarRepository<TradingAccounts> _rep;
    public TradingAccountsService(SqlSugarRepository<TradingAccounts> rep, UserManager userManager)
    {
        _userManager = userManager;
        _rep = rep;
    }

    /// <summary>
    /// 分页查询交易账户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<TradingAccountsOutput>> Page(TradingAccountsInput input)
    {
        var query = _rep.AsQueryable()
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.BankCode), u => u.BankCode.Contains(input.BankCode.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.BankName), u => u.BankName.Contains(input.BankName.Trim()))

                    .Select<TradingAccountsOutput>()
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加交易账户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddTradingAccountsInput input)
    {
        var isExit = await _rep.IsAnyAsync(x => x.Name == input.Name);
        if (isExit) throw new System.Exception("账户名称已存在");
        var entity = input.Adapt<TradingAccounts>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除交易账户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteTradingAccountsInput input)
    {
        var entity = input.Adapt<TradingAccounts>();
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新交易账户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateTradingAccountsInput input)
    {
        var entity = input.Adapt<TradingAccounts>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取交易账户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<TradingAccounts> Get([FromQuery] QueryByIdTradingAccountsInput input)
    {
        return await _rep.GetByIdAsync(input.Id);
    }

    /// <summary>
    /// 获取交易账户列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<TradingAccountsOutput>> List([FromQuery] TradingAccountsInput input)
    {
        return await _rep.AsQueryable().Select<TradingAccountsOutput>().ToListAsync();
    }


    /// <summary>
    /// 获取交易账户
    /// </summary>
    [ApiDescriptionSettings(Name = "TradingAccountDropdown"), HttpGet]
    public async Task<dynamic> TradingAccountDropdown()
    {
        return await _rep.Context.Queryable<TradingAccounts>()
                .Where(x => x.IsDelete == false && x.TenantId == _userManager.TenantId && x.Status == true)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id
                }
                ).ToListAsync();
    }
}

