<template>
    <el-dialog v-model="dialogVisible" title="加工单方案详情配置" width="50%" :before-close="handleClose">
        <el-form :model="form" label-width="100px" :inline="true" :rules="rules" ref="formRef">
            <el-card style="margin-bottom: 8px;">
                <el-form-item label="方案编号" prop="schemeNo">
                    <el-input v-model="form.schemeNo" placeholder="请输入方案编号" style="width: 160px" disabled readonly />
                </el-form-item>
                <el-form-item label="方案名称" prop="schemeName">
                    <el-input v-model="form.schemeName" placeholder="请输入方案名称" style="width: 160px"/>
                </el-form-item>
                <el-form-item label="原料仓库" prop="materialWarehouseId">
                    <el-select v-model="form.materialWarehouseId" placeholder="请选择原料仓库" style="width: 160px" filterable clearable
                     @change="handleMaterialWarehouseChange"> 
                        <el-option v-for="item in warehouseList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="成品仓库" prop="produceWarehouseId">
                    <el-select v-model="form.produceWarehouseId" placeholder="请选择成品仓库" style="width: 160px" filterable clearable>
                        <el-option v-for="item in warehouseList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-card>
            <el-collapse v-model="activeItems">
                <material-table ref="materialTableRef" v-model:tableData="form.materialList"
                    :active-items="activeItems" :warehouse-id="form.materialWarehouseId" />
                <produce-table ref="produceTableRef" v-model:tableData="form.produceList" :active-items="activeItems" :warehouse-id="form.produceWarehouseId" />
            </el-collapse>
        </el-form>
        <template #footer>
            <div style="display: flex; justify-content: flex-end; gap: 10px;">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submitting">提交</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, provide, reactive, nextTick } from 'vue';
import materialTable from './materialTable.vue';
import produceTable from './produceTable.vue';
import { OrderTableData } from '../../types/type';
import { getOneProcessOrderScheme, addProcessOrderScheme, updateProcessOrderScheme,getWarehouseList } from '/@/api/main/processOrderScheme';
import { ElMessage, ElMessageBox } from 'element-plus';

const formRef = ref(null);
const materialTableRef = ref(null);
const produceTableRef = ref(null);
const submitting = ref(false);

const emit = defineEmits(['handleSubmit']);

const form = ref<OrderTableData>({
    id: 0,
    schemeName: '',
    schemeNo: '',
    createTime: '',
    updateTime: '',
    status: 0,
    materialList: [],
    produceList: [],
    materialWarehouseId: undefined,
    produceWarehouseId: undefined,
});

// 表单验证规则
const rules = reactive({
    schemeName: [
        { required: true, message: '请输入方案名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度应为2到50个字符', trigger: 'blur' }
    ],
});

const dialogVisible = ref(false);
const activeItems = ref(['materialList', 'produceList']);
const warehouseList = ref([{ id: '', name: '' }]);

const handleClose = () => {
    dialogVisible.value = false;
};

// 获取原料仓库和成品仓库的选项
const getWarehouseOptions = async () => {
    getWarehouseList({}).then((res: any) => {
        warehouseList.value = res.data.result;
    });
};

const materialWarehouseId = ref('');
const handleMaterialWarehouseChange = (val: string) => {
    if (form.value.materialList.length > 0) {
        ElMessageBox.confirm('切换原料仓库后，原料列表将清空，是否继续？').then(() => {
            form.value.materialList = [];
            materialTableRef.value.resetEditStatus();
            materialWarehouseId.value = val;
        }).catch(() => {
            // 恢复原料仓库
            form.value.materialWarehouseId = materialWarehouseId.value;
        });
    }
};

// 提交表单
const handleSubmit = () => {
    if (formRef.value) {
        formRef.value.validate((valid: boolean) => {
            if (valid) {
                // 验证原料列表和产出列表是否为空
                if (form.value.materialList.length === 0) {
                    ElMessage.warning('原料列表不能为空');
                    return;
                }

                if (form.value.produceList.length === 0) {
                    ElMessage.warning('产出列表不能为空');
                    return;
                }

                // 验证表格是否有未完成的编辑
                if (!materialTableRef.value.valid()) {
                    return;
                }

                if (!produceTableRef.value.valid()) {
                    return;
                }

                // 开始提交
                submitting.value = true;

                // 模拟API调用
                (form.value.id === 0
                    ? addProcessOrderScheme(form.value)
                    : updateProcessOrderScheme(form.value)
                )
                .then(res => {
                    ElMessage.success('提交成功');
                    emit('handleSubmit');
                    dialogVisible.value = false;
                }).finally(() => {
                    submitting.value = false;
                });
            } else {
                ElMessage.error('请完善表单信息');
                return false;
            }
        });
    }
};


const openDialog = async (id?: number) => {
    dialogVisible.value = true;
    provide('reset', dialogVisible);

    // 重置表单状态
    if (formRef.value) {
        formRef.value.resetFields();
    }

    form.value = {
        id: 0,
        schemeName: '',
        schemeNo: '',
        createTime: '',
        updateTime: '',
        status: 0,
        materialList: [],
        produceList: [],
        materialWarehouseId: '',
        produceWarehouseId: '',
    };

    // 重置表格编辑状态
    nextTick(() => {
        if (materialTableRef.value) {
            materialTableRef.value.resetEditStatus();
        }

        if (produceTableRef.value) {
            produceTableRef.value.resetEditStatus();
        }

        getWarehouseOptions();

        if (id) {
            if (materialTableRef.value) {
                materialTableRef.value.loading = true;
            }
            if (produceTableRef.value) {
                produceTableRef.value.loading = true;
            }
            getOneProcessOrderScheme({id: id}).then(res => {
                form.value = res.data.result;
                // 加载数据后再次确保重置编辑状态
                nextTick(() => {
                    if (materialTableRef.value) {
                        materialTableRef.value.resetEditStatus();
                        materialTableRef.value.loading = false;
                    }

                    if (produceTableRef.value) {
                        produceTableRef.value.resetEditStatus();
                        produceTableRef.value.loading = false;
                    }
                });
            });
        }
    });
}

defineExpose({
    openDialog
});
</script>

<style scoped lang="scss">
:deep(.el-card__body) {
    padding: 8px;
}

:deep(.el-dialog__footer) {
    padding-top: 10px;
    border-top: 1px solid var(--el-border-color-lighter);
}
</style>