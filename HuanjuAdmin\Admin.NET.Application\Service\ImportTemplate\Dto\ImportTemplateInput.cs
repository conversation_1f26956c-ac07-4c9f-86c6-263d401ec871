﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 导入模板基础输入参数
    /// </summary>
    public class ImportTemplateBaseInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public virtual long Id { get; set; }
        
        /// <summary>
        /// 模板名称
        /// </summary>
        public virtual string? Name { get; set; }
        
        /// <summary>
        /// 版本
        /// </summary>
        public virtual int? Version { get; set; }
        
        /// <summary>
        /// 文件路径
        /// </summary>
        public virtual string? FilePath { get; set; }
        
    }

    /// <summary>
    /// 导入模板分页查询输入参数
    /// </summary>
    public class ImportTemplateInput : BasePageInput
    {
        /// <summary>
        /// 模板名称
        /// </summary>
        public string? Name { get; set; }
        
    }

    /// <summary>
    /// 导入模板增加输入参数
    /// </summary>
    public class AddImportTemplateInput : ImportTemplateBaseInput
    {
    }

    /// <summary>
    /// 导入模板删除输入参数
    /// </summary>
    public class DeleteImportTemplateInput : BaseIdInput
    {
    }

    /// <summary>
    /// 导入模板更新输入参数
    /// </summary>
    public class UpdateImportTemplateInput : ImportTemplateBaseInput
    {
    }

    /// <summary>
    /// 导入模板主键查询输入参数
    /// </summary>
    public class QueryByIdImportTemplateInput : DeleteImportTemplateInput
    {

    }
