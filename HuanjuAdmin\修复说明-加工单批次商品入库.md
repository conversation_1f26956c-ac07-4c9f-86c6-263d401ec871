# 加工单批次商品入库问题修复说明

## 问题描述
加工单的入库功能中，如果商品是批次商品，没有写入批次信息，导致批次商品的入库记录缺失批次数据。

## 问题分析
在 `ProcessOrderService.Add` 方法中，创建 `Warehousing` 对象时，无论商品是否为批次商品，都直接设置了空的批次列表：
```csharp
batchs = new List<AddwarehousebatchInput>(),
```

这导致批次商品在入库时没有生成相应的批次记录。

## 修复方案
在加工单入库逻辑中，增加对批次商品的检查和默认批次信息生成：

### 修改文件
- `HuanjuAdmin/Admin.NET.Application/Service/ProcessOrder/ProcessOrderService.cs`

### 修改内容
在 `Add` 方法的入库逻辑中，增加以下代码：

```csharp
// 为批次商品生成默认批次信息
foreach (var item in inrecordMXList)
{
    // 获取商品信息以检查是否为批次商品
    var goods = await _rep.Context.Queryable<Warehousegoods>()
        .Where(g => g.Id == item.goodsId)
        .FirstAsync();
        
    if (goods?.isbatch == true)
    {
        // 为批次商品生成默认批次信息
        var defaultBatch = new AddwarehousebatchInput
        {
            Batchnumber = $"{timeStr}{goodsIdSuffix}", // 加工单批次号：年月日时分 + 商品ID后4位
            GoodProductNum = item.quantity, // 良品数量等于入库数量
            RejectNum = 0, // 次品数量为0
            ProduceTime = DateTime.Now.Date, // 生产日期为当前日期
            WarrantyTime = goods.ExpirationDate ?? 365, // 保质期，默认365天
            ExpiryReminder = goods.ExpiryReminder ?? 30, // 过期提醒，默认30天
            ExpirationTime = DateTime.Now.Date.AddDays(goods.ExpirationDate ?? 365), // 到期时间
            ShelflifeStatus = 0 // 正常状态
        };
        
        item.batchs = new List<AddwarehousebatchInput> { defaultBatch };
    }
}
```

## 修复逻辑说明

### 1. 批次商品检查
- 通过查询 `Warehousegoods` 表的 `isbatch` 字段判断商品是否为批次商品
- 只有当 `goods?.isbatch == true` 时才生成批次信息

### 2. 默认批次信息生成
- **批次号规则**：`{年份后两位+月日时分}{商品ID后4位}`，完全参考前端逻辑，确保唯一性且不超过数据库字段长度限制
- **良品数量**：等于入库数量，因为加工单产出的都是良品
- **次品数量**：设为0
- **生产日期**：当前日期
- **保质期**：使用商品设置的保质期，默认365天
- **过期提醒**：使用商品设置的过期提醒天数，默认30天
- **到期时间**：生产日期 + 保质期天数
- **保质期状态**：0（正常状态）

### 3. 数据流程
1. 创建入库单和明细
2. 提交入库单
3. 获取入库明细列表
4. 检查每个商品是否为批次商品
5. 为批次商品生成默认批次信息
6. 调用库存服务进行入库处理

## 测试验证
1. 创建一个批次商品
2. 创建加工单，选择该批次商品作为产出商品
3. 执行加工单，检查入库后是否生成了批次记录
4. 验证批次信息是否正确（批次号、数量、日期等）

## 影响范围
- 仅影响加工单的入库功能
- 不影响其他入库方式（采购入库、手动入库等）
- 向前兼容，不影响已有数据

## 编译状态
✅ 编译成功，无错误

## 部署建议
1. 备份数据库
2. 部署新版本代码
3. 测试加工单入库功能
4. 验证批次商品的批次信息生成是否正常 