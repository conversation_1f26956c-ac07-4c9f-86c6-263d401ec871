﻿<template>
	<div class="warehousegoods-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="700" draggable="" :close-on-click-modal="false">
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="ruleForm.id" />
					</el-form-item>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="商品名称" prop="name">
							<el-input v-model="ruleForm.name" placeholder="请输入商品名称" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="品牌" prop="brand">
							<el-input v-model="ruleForm.brand" placeholder="请输入品牌" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="编码" prop="code">
							<el-input v-model="ruleForm.code" placeholder="请输入编码" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="规格" prop="specs">
							<el-input v-model="ruleForm.specs" placeholder="请输入规格" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="单位" prop="unit">
							<el-select filterable v-model="ruleForm.unit" placeholder="">
								<el-option v-for="(item, index) in WarehouseUnit" :key="index" :value="item.value" :label="item.label"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="辅助单位" prop="auxiliaryunit">
							<el-select clearable filterable v-model="ruleForm.auxiliaryunit" placeholder="">
								<el-option v-for="(item, index) in WarehouseUnit" :key="index" :value="item.value" :label="item.label" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="包装比例" prop="convertCount">
							<el-input v-model="ruleForm.convertCount" placeholder="请输入包装比例" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="是否负库存" prop="vacancy">
							<el-switch v-model="ruleForm.vacancy" />
						</el-form-item>
					</el-col>

					<!-- 					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="包装比例" prop="Packagingratio">
							<el-input v-model="ruleForm.Packagingratio" placeholder="请输入包装比例" clearable />
						</el-form-item>
					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="商品条码" prop="barcode">
							<el-input v-model="ruleForm.barcode" placeholder="请输入商品条码" clearable />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="是否唯一码" prop="isuniquecode">
							<el-switch v-model="ruleForm.isuniquecode" />
							<!-- 							<el-select clearable filterable v-model="ruleForm.isuniquecode" placeholder="请选择">
								<el-option v-for=" (item, index) in counterStore.hasList" :key="index" :value="item.value"
									:label="item.label" />

							</el-select> -->
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="是否批次" prop="isbatch">
							<!-- 				<el-select clearable filterable v-model="ruleForm.isbatch" placeholder="请选择">
								<el-option v-for=" (item, index) in counterStore.hasList" :key="index" :value="item.value"
									:label="item.label" />

							</el-select> -->
							<el-switch v-model="ruleForm.isbatch" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="ruleForm.remark" placeholder="请输入备注" clearable />
						</el-form-item>
					</el-col>

					<template v-if="ruleForm.isbatch">
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="保质期(天)" prop="expirationDate">
								<el-input-number v-model="ruleForm.expirationDate" placeholder="请输入保质期(天)" clearable />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="过期提醒(天)" prop="expiryReminder">
								<el-input-number v-model="ruleForm.expiryReminder" placeholder="请输入过期提醒(天)" clearable />
							</el-form-item>
						</el-col>
					</template>
					<!-- 					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="是否公用" prop="isCommunal">
							<el-switch v-model="ruleForm.isCommunal" />

						</el-form-item>

					</el-col> -->
					<!-- 					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="库存数量" prop="inventoryCount">
							<el-input-number v-model="ruleForm.inventoryCount" placeholder="请输入库存数量" :disabled="ruleForm.id"
								clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="库存告警(低)" prop="alarmMin">
							<el-input-number v-model="ruleForm.alarmMin" placeholder="请输入库存告警(低)" clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="库存告警(高)" prop="alarmMax">
							<el-input-number v-model="ruleForm.alarmMax" placeholder="请输入库存告警(高)" clearable />

						</el-form-item>

					</el-col> -->
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import useCounter from '/@/stores/counter';
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormRules } from 'element-plus';
import { addwarehousegoods, updatewarehousegoods } from '/@/api/main/warehousegoods';
import { WarehouseGoodsUnit } from '/@/api/main/warehouseInrecord';
import { forEach } from 'lodash-es';
import { useGoodsStore } from '/@/stores/goods';

const goodsStore = useGoodsStore();
const isShowDialog = ref(false);
const ruleFormRef = ref();
const loading = ref(false);
const ruleForm = ref<any>({});
const rules = ref<FormRules>({
	name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
	unit: [{ required: true, message: '请选择单位', trigger: 'blur' }],
});

//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: '',
	},
});

//父级传递来的函数，用于回调
const emit = defineEmits(['reloadTable']);
const WarehouseUnit = ref<any>([]);

const WareUnit = async () => {
	debugger;
	var res = await WarehouseGoodsUnit();
	WarehouseUnit.value = res.data.result;
};

// 打开弹窗
const openDialog = (row: any) => {
	debugger;
	ruleForm.value = JSON.parse(JSON.stringify(row));
	isShowDialog.value = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit('reloadTable');
	isShowDialog.value = false;
	setTimeout(() => {
		loading.value = false;
	}, 500);
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};

// 提交
const submit = async () => {
	try {
		const valid = await ruleFormRef.value.validate();
		if (valid) {
			loading.value = true;
			
			if (ruleForm.value.id) {
				await updatewarehousegoods(ruleForm.value);
			} else {
				await addwarehousegoods(ruleForm.value);
			}
			
			// 保存成功后刷新商品列表缓存
			await goodsStore.refreshGoodsList();
			
			ElMessage.success('保存成功');
			closeDialog();
		}
	} catch (error) {
		console.error('保存失败:', error);
		ElMessage.error('保存失败');
	} finally {
		loading.value = false;
	}
};

// 页面加载时
onMounted(async () => {
	WareUnit();
});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>




