<template>
	<div class="home-container layout-pd">
		<el-row :gutter="15" class="home-card-one mb15">
			<el-col :xs="24" :sm="10" :md="10" :lg="16" :xl="16">
				<div class="home-card-item">
					<div class="home-card-item-title">数据面板</div>
					<div class="home-monitor">
						<div class="flex-warp">
							<div class="flex-warp">
								<div class="flex-warp-item" v-for="(v, k) in state.homeThree" :key="k" :class="`flex-warp-item-${k % 5}`">
									<div class="flex-warp-item-box custom-link" :class="`home-animation${k}`" @click="handleItemClick(v)">
										<div class="flex-margin">
											<i :class="v.icon" :style="{ color: v.iconColor }"></i>
											<span class="pl15">{{ v.title }}</span>
											<div class="mt15">{{ v.num }}</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</el-col>

			<el-col :xs="24" :sm="8" class="pl15">
				<el-card shadow="hover" class="notice-list">
					<template #header>
						<div class="notice-header">
							<el-tabs v-model="state.activeTab" class="notice-tabs">
								<el-tab-pane label="系统消息" name="system">
									<div class="notice-list-box">
										<ul class="notice-list-ul">
											<li v-for="(item, index) in state.systemMessages" :key="index" class="notice-list-li" :class="{'unread': item.readStatus === 0}">
												<div class="notice-list-li-title" @click="handleNoticeClick(item)">
													<span class="notice-type">{{ item.sysNotice.type === 1 ? '[通知]' : '[公告]' }}</span>
													{{ item.sysNotice.title }}
												</div>
												<div class="notice-time">{{ item.sysNotice.publicTime }}</div>
											</li>
										</ul>
									</div>
								</el-tab-pane>
								<el-tab-pane label="公司消息" name="company">
									<div class="notice-list-box">
										<ul class="notice-list-ul">
											<li v-for="(item, index) in state.companyMessages" :key="index" class="notice-list-li" :class="{'unread': item.readStatus === 0}">
												<div class="notice-list-li-title" @click="handleNoticeClick(item)">
													<span class="notice-type">{{ item.sysNotice.type === 1 ? '[通知]' : '[公告]' }}</span>
													{{ item.sysNotice.title }}
												</div>
												<div class="notice-time">{{ item.sysNotice.publicTime }}</div>
											</li>
										</ul>
									</div>
								</el-tab-pane>
							</el-tabs>
							<div class="notice-more">
								<el-button link type="primary" @click="goToNotice">查看更多>></el-button>
							</div>
						</div>
					</template>
				</el-card>
			</el-col>
		</el-row>

		<el-row :gutter="15" class="home-card-two mb15">
			<el-col :xs="24" :sm="12" :md="12" :lg="16" :xl="16">
				<el-card class="my-card">
					<template #header>
						<div class="data-overview-header">
							<div class="title">数据概览</div>
						</div>
					</template>
					<el-row :gutter="15" class="personal-recommend-row">
						<el-col :sm="8" v-for="(v, k) in state.recommendList.slice(0, 3)" :key="k" class="personal-recommend-col">
							<div class="personal-recommend" :style="{ 'background-color': v.bg }">
								<div class="recommend-icon">
									<SvgIcon :name="v.icon" :size="40" :style="{ color: v.iconColor }" />
								</div>
								<div class="personal-recommend-auto">
									<div class="personal-recommend-title">
										{{ v.title }}
									</div>
									<!-- 根据类型使用不同的显示模板 -->
									<div class="personal-recommend-content" v-if="v.type === 'amount'">
										<div class="content-item">
											<div class="total-amount">
												<span class="value">{{ v.total }}</span>
											</div>
										</div>
									</div>
									<div class="personal-recommend-content" v-else>
										<div class="content-item">
											<div class="month-value">
												<span class="label">本月:</span>
												<span class="value">{{ v.month }}</span>
											</div>
											<div class="year-value">
												<span class="label">本年:</span>
												<span class="value">{{ v.year }}</span>
											</div>
										</div>
									</div>
								</div>
							</div>
						</el-col>
					</el-row>
				</el-card>
				<el-card class="mt15">
					<div class="chart-content">
						<div class="content-left">
							<div class="content-left-title">
								<div class="title-group">
									<span class="tit">商品销售趋势</span>
									<div class="btn-group">
										<el-radio-group v-model="state.overviewType" size="small">
											<el-radio-button label="quantity">销售量</el-radio-button>
											<el-radio-button label="amount">销售额</el-radio-button>
										</el-radio-group>
										<el-radio-group v-model="state.overviewTimePerson" size="small">
											<el-radio-button label="month">本月</el-radio-button>
											<el-radio-button label="year">本年</el-radio-button>
										</el-radio-group>
									</div>
								</div>
							</div>
							<div class="content-left-chart">
								<div class="chart-wrapper">
									<div style="width: 100%; height: 320px" ref="homeBarRef"></div>
								</div>
							</div>
						</div>

						<!-- <div class="content-right">
							<div class="content-right-title">
								<div class="tit">商品销售量占比</div>
								<div>
									<el-radio-group v-model="state.overviewTimeGoods" class="btn mr5">
										<el-radio-button label="month">本月</el-radio-button>
										<el-radio-button label="year">本年</el-radio-button>
									</el-radio-group>
								</div>
							</div>
							<div class="content-right-chart">
								<div class="chart-wrapper">
									<div style="width: 100%; height: 320px" ref="homePieRef"></div>
								</div>
							</div>
						</div> -->
					</div>
				</el-card>
			</el-col>

			<el-col :xs="24" :sm="8" class="pl15">
				<el-card class="bulletin">
					<template #header>
						<div class="bulletin-top">
							<div class="title">优选商城</div>
							<el-button class="bulletin-more" type="text">查看更多>></el-button>
						</div>
					</template>
					<div class="bulletin-center">
						<div class="bulletin-item">
							<div class="item-list">
								<img src="/@/assets/mall/product1.png" alt="产品1" />
								<div class="list-item">
									<p>电脑租赁</p>
									<span>咨询电话:17557292866(微信同号)</span>
								</div>
							</div>
						</div>
						<div class="bulletin-item">
							<div class="item-list">
								<img src="/@/assets/mall/product2.png" alt="产品2" />
								<div class="list-item">
									<p>办公耗材销售</p>
									<span>咨询电话:17557292866(微信同号)</span>
								</div>
							</div>
						</div>
						<div class="bulletin-item">
							<div class="item-list">
								<img src="/@/assets/mall/product3.png" alt="产品3" />
								<div class="list-item">
									<p>办公设备维修</p>
									<span>咨询电话:17557292866(微信同号)</span>
								</div>
							</div>
						</div>
					</div>
				</el-card>

				<el-card class="download-center mt15">
					<template #header>
						<div class="download-title">
							<span>下载中心</span>
						</div>
					</template>

					<div class="download-list">
						<div class="icon-cent clickable">
							<div class="icon">
								<el-icon>
									<Iphone />
								</el-icon>
							</div>
							<span class="text">焕炬App</span>
						</div>
						<div class="icon-cent clickable">
							<div class="icon2">
								<el-icon>
									<Document />
								</el-icon>
							</div>
							<span class="text">操作手册</span>
						</div>
						<div class="icon-cent clickable" @click="handleImportTemplate">
							<div class="icon3">
								<el-icon>
									<FolderAdd />
								</el-icon>
							</div>
							<span class="text">导入模版</span>
						</div>
					</div>
				</el-card>
			</el-col>
		</el-row>

		<el-dialog 
			v-model="state.dialogVisible" 
			title="消息详情" 
			draggable 
			width="769px" 
			:close-on-click-modal="false"
		>
			<p v-html="state.dialogContent"></p>
			<template #footer>
				<span class="dialog-footer">
					<el-button type="primary" @click="state.dialogVisible = false">确认</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="home">
import { reactive, onMounted, ref, watch, nextTick, onActivated, markRaw, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import { newsInfoList, recommendList } from './list';
import { Iphone, Document, FolderAdd } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { getHomeThreeData, getHomeRecommendData, getSalesTrend } from '/@/api/system/home';
import { getAPI } from '/@/utils/axios-utils';
import { SysNoticeApi } from '/@/api-services/api';

// 定义变量内容
const router = useRouter();
const homePieRef = ref();
const homeBarRef = ref();
const storesTagsViewRoutes = useTagsViewRoutes();
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const { isTagsViewCurrenFull } = storeToRefs(storesTagsViewRoutes);

const handleImportTemplate = () => {
	router.push('/system/importtemplate');
};

// 1. 首先定义数据结构
interface SalesTrendData {
	xAxis: string[];
	series: {
		goodsName: string;
		data: number[];
	}[];
}

// 2. 定义响应式数据
const salesData = ref<SalesTrendData>({
	xAxis: [],
	series: [],
});

const state = reactive({
	newsInfoList,
	recommendList,
	global: {
		homeChartOne: null,
		MonSalesperson: null,
		MonProductSales: null,
		dispose: [null, '', undefined],
	} as any,
	noticeLists: [
		{
			title: '系统公告',
			links: '/SystemlistLink',
		},
		{
			title: '公司公告',
			links: '/NoticelistLink',
		},
	],
	homeThree: [
		{
			icon: 'iconfont icon-yangan',
			title: '待入库',
			num: '/',
			key: 'pendingInbound',
			iconColor: '#F72B3F',
			link: '/wareHouse/warehouseinrecord',
			query: {
				inhouseStatus: ['0', '1', '2'],
			},
		},
		{
			icon: 'iconfont icon-zaosheng',
			title: '待出库',
			num: '/',
			key: 'pendingOutbound',
			iconColor: '#FBD4A0',
			link: '/wareHouse/warehouseout',
			query: {
				outBoundStatus: ['0', '1', '2'],
			},
		},
		{
			icon: 'iconfont icon-wendu',
			title: '待收款',
			num: '/',
			key: 'pendingReceipt',
			iconColor: '#91BFF8',
			link: '/financial/receipt',
			query: {
				paymentStatus: ['0', '1', '2'], // 待提交、待收款、部分收款
			},
		},
		{
			icon: 'iconfont icon-shidu',
			title: '待付款',
			num: '/',
			key: 'pendingPayment',
			iconColor: '#88D565',
			link: '/financial/paymentorder',
			query: {
				paymentStatus: ['0', '1', '2'], // 待提交、待付款、部分收付款
			},
		},
		{
			icon: 'iconfont icon-shidu',
			title: '待开票',
			num: '/',
			key: 'pendingInvoice',
			iconColor: '#88D565',
			link: '/financial/receipt',
			query: {
				invoiceStatus: ['1', '3'], // 待开、申开
			},
		},
		{
			icon: 'iconfont icon-zaosheng',
			title: '缺货',
			num: '/',
			key: 'pendingStockOut',
			iconColor: '#FBD4A0',
			link: '/wareHouse/warehousestore',
			query: { stockStatus: 'stockOut' },
		},
		{
			icon: 'iconfont icon-zaosheng',
			title: '临期/到期',
			num: '/',
			key: 'expiryWarning',
			iconColor: '#FBD4A0',
			link: '/wareHouse/warehousestore',
			query: { stockStatus: 'expiry' },
		},
		{
			icon: 'iconfont icon-zaosheng',
			title: '库存预警',
			num: '/',
			key: 'stockWarning',
			iconColor: '#FBD4A0',
			link: '/wareHouse/warehousestore',
			query: { stockStatus: 'warning' },
		},
		{
			icon: 'iconfont icon-zaosheng',
			title: '待履约',
			num: '/',
			key: 'pendingSalesZX',
			iconColor: '#FBD4A0',
			link: '/sales/salescontract',
			query: { contractStatus: ['1'] },
		},
		{
			icon: 'iconfont icon-zaosheng',
			title: '待签约',
			num: '/',
			key: 'pendingSalesGJ',
			iconColor: '#FBD4A0',
			link: '/sales/salescontract',
			query: { contractStatus: ['0'] },
		},
	],
	myCharts: [] as EmptyArrayType,
	charts: {
		theme: '',
		bgColor: '',
		color: '#303133',
	},

	overviewTimePerson: 'month',
	overviewTimeGoods: 'month',
	overviewType: 'quantity',
	activeTab: 'system',
	systemMessages: [] as any[],
	companyMessages: [] as any[],
	dialogVisible: false,
	dialogContent: '',
});

// // 饼图
// const initPieChart = () => {
// 	//饼图
// 	if (!state.global.dispose.some((b: any) => b === state.global.MonSalesperson)) state.global.MonSalesperson.dispose();
// 	state.global.MonSalesperson = markRaw(echarts.init(homePieRef.value, state.charts.theme));
// 	var getname = ['商品A', '商品B', '商品C', '商品D', '其他商品'];
// 	var getvalue = [22, 10, 5, 30, 50];
// 	var data = [];
// 	for (var i = 0; i < getname.length; i++) {
// 		data.push({ name: getname[i], value: getvalue[i] });
// 	}
// 	const colorList = ['#51A3FC', '#36C78B', '#FEC279', '#968AF5', '#E790E8'];
// 	const option = {
// 		tooltip: {
// 			trigger: 'item',
// 			formatter: '{a} <br/>{b} : {c} ({d}%)',
// 		},
// 		legend: {
// 			bottom: 0,
// 			left: 'center',
// 			data: getname,
// 		},
// 		series: [
// 			{
// 				type: 'pie',
// 				radius: '65%',
// 				center: ['50%', '50%'],
// 				//   selectedMode: 'single',
// 				data: data,
// 				label: {
// 					normal: {
// 						show: false,
// 					},
// 				},
// 				labelLine: {
// 					normal: {
// 						show: false,
// 					},
// 				},
// 				emphasis: {
// 					itemStyle: {
// 						shadowBlur: 10,
// 						shadowOffsetX: 0,
// 						shadowColor: 'rgba(0, 0, 0, 0.5)',
// 					},
// 				},
// 			},
// 		],
// 	};
// 	state.global.MonSalesperson.setOption(option);
// 	state.myCharts.push(state.global.MonSalesperson);
// };

// 3. 修改初始化图表函数
const initBarChart = async () => {
	if (!state.global.dispose.some((b: any) => b === state.global.MonProductSales)) state.global.MonProductSales.dispose();

	state.global.MonProductSales = markRaw(echarts.init(homeBarRef.value, state.charts.theme));

	try {
		// 获取销售趋势数据
		const res = await getSalesTrend({
			type: state.overviewType,
			timeRange: state.overviewTimePerson,
		});

		if (res.data.code === 200) {
			salesData.value = res.data.result;

			// 使用获取到的数据初始化图表
			const option = {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'line',
					},
				},
				legend: {
					data: salesData.value.series.map((item) => item.goodsName),
				},
				grid: {
					left: '3%',
					right: '4%',
					bottom: '3%',
					containLabel: true,
				},
				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: salesData.value.xAxis,
				},
				yAxis: {
					type: 'value',
				},
				series: salesData.value.series.map((item) => ({
					name: item.goodsName,
					type: 'line',
					smooth: true,
					symbolSize: 6,
					symbol: 'circle',
					data: item.data,
					areaStyle: {
						color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
							{ offset: 0, color: 'rgba(58,77,233,0.3)' },
							{ offset: 1, color: 'rgba(58,77,233,0.1)' },
						]),
					},
					lineStyle: {
						width: 2,
					},
				})),
			};

			state.global.MonProductSales.setOption(option);
		}
	} catch (error) {
		console.error('获取销售趋势数据失败:', error);
	}
};

// 4. 确保在组件挂载后初始化图表
onMounted(() => {
	nextTick(() => {
		initBarChart();
	});
});

// 5. 可选：添加窗口大小变化时重绘图表
window.addEventListener('resize', () => {
	if (state.global.MonProductSales) {
		state.global.MonProductSales.resize();
	}
});

// 6. 可选：在组件卸载时清理
onUnmounted(() => {
	if (state.global.MonProductSales) {
		state.global.MonProductSales.dispose();
	}
	window.removeEventListener('resize', () => {
		if (state.global.MonProductSales) {
			state.global.MonProductSales.resize();
		}
	});
});

// 批量设置 echarts resize
const initEchartsResizeFun = () => {
	nextTick(() => {
		for (let i = 0; i < state.myCharts.length; i++) {
			setTimeout(() => {
				state.myCharts[i].resize();
			}, i * 1000);
		}
	});
};
// 批量设置 echarts resize
const initEchartsResize = () => {
	window.addEventListener('resize', initEchartsResizeFun);
};
// 页面加载时
onMounted(() => {
	initEchartsResize();
	getHomeNums();
	getRecommendData();
	getMessages();
});
// 由于页面缓存原因，keep-alive
onActivated(() => {
	initEchartsResizeFun();
	getHomeNums(); // 页面重新激活时调用
	getRecommendData();
});
// 监听 pinia 中的 tagsview 开启全屏变化，重新 resize 图表，防止不出现/大小不变等
watch(
	() => isTagsViewCurrenFull.value,
	() => {
		initEchartsResizeFun();
	}
);
// 监听 pinia 中是否开启深色主题
watch(
	() => themeConfig.value.isIsDark,
	(isIsDark) => {
		nextTick(() => {
			state.charts.theme = isIsDark ? 'dark' : '';
			state.charts.bgColor = isIsDark ? 'transparent' : '';
			state.charts.color = isIsDark ? '#dadada' : '#303133';
			// setTimeout(() => {
			// 	initLineChart();
			// }, 500);
			// setTimeout(() => {
			// 	initPieChart();
			// }, 700);
			setTimeout(() => {
				initBarChart();
			}, 1000);
		});
	},
	{
		deep: true,
		immediate: true,
	}
);

// 监听销售额/销售量切换
watch(
	() => state.overviewType,
	() => {
		initBarChart();
	}
);

// 监听时间范围切换
watch(
	() => state.overviewTimePerson,
	() => {
		initBarChart();
	}
);

// 通用跳转处理函数
const handleItemClick = (item) => {
	if (item.num === '/') return; // 当 num 为 '/' 时直接返回，不执行跳转

	router.push({
		path: item.link,
		query: item.query || {},
	});
};

// 添加获取首页数据方法
const getHomeNums = async () => {
	try {
		const res = await getHomeThreeData();
		if (res.data.code === 200) {
			// 更新数据
			state.homeThree.forEach((item) => {
				if (item.key && res.data.result[item.key] !== undefined) {
					item.num = res.data.result[item.key]?.toString();
				}
			});
		}
	} catch (error) {
		console.error('获取首页数据失败:', error);
	}
};

// 添加时间切换处理函数
const handleOverviewTimeChange = (value: string) => {
	// 这里可以根据选择的时间范围重新获取数据
	console.log('Time range changed:', value);
	// TODO: 调用相应的API获取数据
};

// 获取推荐数据
const getRecommendData = async () => {
	try {
		const res = await getHomeRecommendData();
		if (res.data.code === 200) {
			// 定义样式配置
			const styles = [
				{
					icon: 'ele-Food',
					bg: '#48D18D',
					iconColor: '#64d89d',
				},
				{
					icon: 'ele-ShoppingCart',
					bg: '#8595F4',
					iconColor: '#92A1F4',
				},
				{
					icon: 'ele-School',
					bg: '#F95959',
					iconColor: '#F86C6B',
				},
			];

			// 合并后端数据和样式配置
			state.recommendList = res.data.result.map((item: any, index: number) => ({
				...item,
				...styles[index],
			}));
		}
	} catch (error) {
		console.error('获取数据概览失败:', error);
	}
};

// 获取消息列表
const getMessages = async () => {
	try {
		// 获取系统消息
		const systemRes = await getAPI(SysNoticeApi).apiSysNoticePageReceivedGet(
			undefined, // title
			undefined, // type
			1, // messageLevel - 系统消息
			1, // page
			5, // pageSize
			undefined, // field
			undefined, // order
			undefined  // descStr
		);
		state.systemMessages = systemRes.data.result?.items || [];

		// 获取公司消息
		const companyRes = await getAPI(SysNoticeApi).apiSysNoticePageReceivedGet(
			undefined, // title
			undefined, // type
			2, // messageLevel - 公司消息
			1, // page
			5, // pageSize
			undefined, // field
			undefined, // order
			undefined  // descStr
		);
		state.companyMessages = companyRes.data.result?.items || [];
	} catch (error) {
		console.error('获取消息列表失败:', error);
	}
};

// 消息点击处理
const handleNoticeClick = async (notice: any) => {
	state.dialogContent = notice.sysNotice.content;
	state.dialogVisible = true;

	// 设置已读
	if(notice.readStatus === 0) {
		notice.readStatus = 1;
		await getAPI(SysNoticeApi).apiSysNoticeSetReadPost({ id: notice.sysNotice.id });
	}
};

// 前往通知中心
const goToNotice = () => {
	router.push('/dashboard/notice');
};

// 在组件挂载时获取消息
onMounted(() => {
	getMessages();
});
</script>

<style scoped lang="scss">
@import '../../theme/mixins/index.scss';
$homeNavLengh: 8;

.custom-link {
	text-decoration: none; /* 移除下划线 */
	color: inherit; /* 继承父元素的颜色 */
	display: block; /* 使链接成为块级元素，以便可以设置宽度和高度 */
	width: 100%; /* 设置宽度为100%，以填满父容器 */
	height: 100%; /* 设置高度为100%，以填满父容器 */
}

.home-container {
	overflow: hidden;

	.home-card-one,
	.home-card-two,
	.home-card-three {
		.home-card-item {
			width: 100%;
			height: 130px;
			border-radius: 4px;
			transition: all ease 0.3s;
			padding: 20px;
			overflow: hidden;
			background: var(--el-color-white);
			color: var(--el-text-color-primary);
			border: 1px solid var(--next-border-color-light);

			&:hover {
				box-shadow: 0 2px 12px var(--next-color-dark-hover);
				transition: all ease 0.3s;
			}

			&-icon {
				width: 70px;
				height: 70px;
				border-radius: 100%;
				flex-shrink: 1;

				i {
					color: var(--el-text-color-placeholder);
				}
			}

			&-title {
				font-size: 15px;
				font-weight: bold;
			}
		}
	}

	.home-card-one {
		.home-card-item {
			height: 100%;
			width: 100%;
			overflow: hidden;

			.home-monitor {
				height: 100%;
				display: flex;

				align-items: center;
				align-content: center;

				.flex-warp {
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
				}

				.flex-warp-item {
					width: 20%;
					height: 90px;
					display: flex;

					align-items: flex-start;
					align-content: center;

					.flex-warp-item-box {
						margin: auto;
						text-align: center;
						color: var(--el-text-color-primary);
						display: flex;
						border-radius: 5px;
						background: var(--next-bg-color);
						cursor: pointer;
						transition: all 0.3s ease;

						&:hover {
							background: var(--el-color-primary-light-9);
							transition: all 0.3s ease;
						}
					}

					@for $i from 0 through $homeNavLengh {
						.home-animation#{$i} {
							opacity: 0;
							animation-name: error-num;
							animation-duration: 0.5s;
							animation-fill-mode: forwards;
							animation-delay: calc($i/10) + s;
						}
					}
				}
			}
		}

		.notice-list {
			height: 100%;

			.notice-header {
				position: relative;
				display: flex;
				width: 100%;
				
				.notice-tabs {
					flex: 1;
					width: calc(100% - 80px); // 减去右侧按钮的宽度
					
					:deep(.el-tabs__header) {
						margin-bottom: 10px;
					}
				}

				.notice-more {
					width: 80px; // 设置固定宽度
					position: absolute;
					top: 0;
					right: 0;
					height: 40px;
					line-height: 40px;

					.el-button {
						font-size: 13px;
						padding: 0;
						height: 40px;
						color: #337ecc;
					}
				}
			}

			.notice-list-box {
				height: 200px;
				overflow-y: auto;
				width: 100%;
				
				.notice-list-ul {
					list-style: none;
					padding: 0;
					margin: 0;
					width: 100%;
					
					.notice-list-li {
						padding: 8px 12px; // 增加左右内边距
						border-bottom: 1px dashed var(--el-border-color-lighter);
						width: 100%;
						display: flex;
						flex-direction: column;
						position: relative; // 添加相对定位用于放置红点
						
						// 未读消息样式
						&.unread {
							.notice-list-li-title {
								font-weight: bold;
								color: var(--el-color-primary);
							}
							
							// 未读红点
							&::before {
								content: '';
								position: absolute;
								left: 2px; // 调整红点位置
								top: 16px; // 调整红点位置与文字居中
								width: 6px;
								height: 6px;
								background-color: #f56c6c;
								border-radius: 50%;
							}
						}
						
						.notice-list-li-title {
							cursor: pointer;
							color: var(--el-text-color-primary);
							width: 100%;
							word-break: break-all;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							
							.notice-type {
								color: var(--el-text-color-secondary);
								margin-right: 5px;
							}
							
							&:hover {
								color: var(--el-color-primary);
							}
						}
						
						.notice-time {
							color: var(--el-text-color-secondary);
							font-size: 12px;
							margin-top: 4px;
						}
					}
				}
			}
		}
	}

	.my-card {
		.data-overview-header {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.title {
				color: var(--333333, #333);
				font-size: 15px;
				font-weight: bold;
			}
		}

		.personal-recommend-row {
			padding: 10px 0;

			.personal-recommend-col {
				.personal-recommend {
					position: relative;
					height: 120px;
					padding: 20px;
					border-radius: 8px;
					display: flex;
					align-items: center;
					transition: all 0.3s;

					&:hover {
						transform: translateY(-3px);
						box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
					}

					.recommend-icon {
						margin-right: 15px;
						display: flex;
						align-items: center;
						justify-content: center;
						width: 50px;
						height: 50px;
						border-radius: 10px;
						background: rgba(255, 255, 255, 0.2);
					}

					.personal-recommend-auto {
						flex: 1;

						.personal-recommend-title {
							color: #fff;
							font-size: 16px;
							font-weight: 500;
							margin-bottom: 12px;
						}

						.personal-recommend-content {
							.content-item {
								.month-value,
								.year-value {
									display: flex;
									align-items: center;
									margin-bottom: 8px;

									.label {
										color: rgba(255, 255, 255, 0.85);
										font-size: 14px;
										margin-right: 8px;
									}

									.value {
										color: #fff;
										font-size: 18px;
										font-weight: 600;
									}
								}
							}
						}
					}
				}
			}
		}
	}

	.chart-content {
		display: flex;
		align-items: flex-start;
		gap: 8px;

		.content-left {
			width: 100%;

			.content-left-title {
				padding: 16px;
				border-bottom: 1px solid var(--el-border-color-lighter);

				.title-group {
					display: flex;
					align-items: center;
					gap: 24px;

					.tit {
						font-size: 15px;
						font-weight: bold;
						color: var(--el-text-color-primary);
						white-space: nowrap;
					}

					.btn-group {
						display: flex;
						gap: 12px;

						:deep(.el-radio-group) {
							.el-radio-button__inner {
								padding: 4px 16px;
							}
						}
					}
				}
			}

			.content-left-chart {
				padding: 16px;

				.chart-wrapper {
					width: 100%;
					height: 320px;
				}
			}
		}

		.content-right {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 6px;
			width: 430px;

			.content-right-title {
				display: flex;
				height: 40px;
				padding: 8px 8px 8px 0px;
				justify-content: space-between;
				align-items: center;
				align-self: stretch;
				background: #f9f9f9;

				.tit {
					color: var(--333333100, #333);
					font-feature-settings: 'clig' off, 'liga' off;
					font-family: PingFang SC;
					font-size: 14px;
					font-style: normal;
					font-weight: 500;
					line-height: 20px;
					text-transform: capitalize;
				}

				.btn {
					display: flex;
					align-items: flex-start;
					text-align: right;
				}
			}

			.content-right-chart {
				width: 100%;
			}
		}
	}

	.bulletin {
		.bulletin-top {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.title {
				color: var(--333333, #333);
				font-size: 16px;
				font-weight: 600;
			}

			.bulletin-more {
				color: var(--el-text-color-secondary);
				font-size: 13px;

				&:hover {
					color: var(--el-color-primary);
					cursor: pointer;
				}
			}
		}

		.bulletin-center {
			display: flex;
			flex-direction: column;
			gap: 10px;
			padding: 10px 0;

			.bulletin-item {
				.item-list {
					display: flex;
					padding: 10px;
					align-items: center;
					gap: 15px;
					border-radius: 8px;
					border: 1px solid #de2910;
					background: #fffaf9;
					transition: all 0.3s ease;

					&:hover {
						transform: translateY(-2px);
						box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
					}

					img {
						width: 80px;
						height: 80px;
						border-radius: 6px;
						object-fit: cover;
					}

					.list-item {
						flex: 1;

						p {
							color: #de2910;
							font-size: 14px;
							font-weight: 500;
							margin-bottom: 8px;
						}

						span {
							color: #de2910;
							font-size: 13px;
							opacity: 0.8;
						}
					}
				}
			}
		}
	}

	.download-center {
		.download-title {
			display: inline-flex;
			align-items: flex-end;
			gap: 6px;

			span {
				color: var(--333333, #333);
				font-feature-settings: 'clig' off, 'liga' off;
				font-family: PingFang SC;
				font-size: 16px;
				font-style: normal;
				font-weight: 600;
				line-height: normal;
			}
		}

		.download-list {
			display: flex;
			justify-content: space-between;
			justify-items: center;
			align-items: center;
			align-self: stretch;
			padding: 0 16px;
			margin-top: 2%;

			.icon-cent {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 4px;
				border-radius: 8px;
				// background: #F6FAFB;
				align-content: center;

				&.clickable {
					cursor: pointer;
					transition: background-color 0.3s ease;

					&:hover {
						background-color: #e6e6e6;
					}
				}

				.icon {
					width: 40px;
					height: 40px;
					flex-shrink: 0;
					border-radius: 6.4px;
					background: #438ef5;
					text-align: center;
					line-height: 40px;
					font-size: 20px;
					color: #fff;
				}

				.icon2 {
					width: 40px;
					height: 40px;
					border-radius: 6.4px;
					background: #e96569;
					text-align: center;
					line-height: 40px;
					font-size: 20px;
					color: #fff;
				}

				.icon3 {
					width: 40px;
					height: 40px;
					flex-shrink: 0;
					border-radius: 6.4px;
					background: #8181f6;
					text-align: center;
					line-height: 40px;
					font-size: 20px;
					text-align: center;
					color: #fff;
				}

				.text {
					color: var(--333333, #333);
					font-feature-settings: 'clig' off, 'liga' off;
					font-family: PingFang SC;
					font-size: 14px;
					font-style: normal;
					text-align: center;
					line-height: 16px;
				}
			}
		}
	}

	.home-card-two,
	.home-card-three {
		.home-card-item {
			height: 400px;
			width: 100%;
			overflow: hidden;

			.home-monitor {
				height: 100%;

				.flex-warp-item {
					width: 25%;
					height: 111px;
					display: flex;

					.flex-warp-item-box {
						margin: auto;
						text-align: center;
						color: var(--el-text-color-primary);
						display: flex;
						border-radius: 5px;
						background: var(--next-bg-color);
						cursor: pointer;
						transition: all 0.3s ease;

						&:hover {
							background: var(--el-color-primary-light-9);
							transition: all 0.3s ease;
						}
					}

					@for $i from 0 through $homeNavLengh {
						.home-animation#{$i} {
							opacity: 0;
							animation-name: error-num;
							animation-duration: 0.5s;
							animation-fill-mode: forwards;
							animation-delay: calc($i/10) + s;
						}
					}
				}
			}
		}
	}

	.chart-wrapper {
		background: #fff;
		padding: 16px 16px 0;
		margin-bottom: 32px;
	}

	@media (max-width: 1024px) {
		.chart-wrapper {
			padding: 8px;
		}
	}
}

.total-amount {
	.value {
		color: #fff;
		font-size: 22px;
		font-weight: 600;
	}
}
</style>
