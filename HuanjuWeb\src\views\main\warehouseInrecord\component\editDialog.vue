﻿<template>
	<!-- -----------------入库单弹窗------------------- -->
	<div class="warehouseInrecord-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="1075" draggable="" :close-on-click-modal="false">
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px">
				<el-row>
					<el-form-item label="供应商" prop="supplierId">
						<el-select style="width: 150px" clearable filterable v-model="ruleForm.supplierId" placeholder="请选择供应商">
							<el-option v-for="(item, index) in pubSupplierDropdownList" :key="index" :value="item.value" :label="item.label" />
						</el-select>
					</el-form-item>

					<el-form-item label="仓库" prop="warehouseid" :rules="[{ required: true, message: '仓库不能为空', trigger: 'blur' }]">
						<el-select style="width: 150px" clearable filterable v-model="ruleForm.warehouseid" placeholder="请选择仓库">
							<el-option v-for="(item, index) in warehouseDropdownList" :key="index" :value="item.value" :label="item.label" />
						</el-select>
					</el-form-item>

					<el-form-item label="入库类型" prop="inhouseType" :rules="[{ required: true, message: '仓库不能为空', trigger: 'blur' }]">
						<el-select style="width: 150px" clearable filterable v-model="ruleForm.inhouseType" placeholder="请选择入库类型">
							<el-option v-for="(item, index) in counterStore.inhouseTypeList" :key="index" :value="item.value" :label="item.label" />
						</el-select>
					</el-form-item>
					<el-form-item label="备注">
						<el-input style="width: 150px" v-model="ruleForm.remark" placeholder="请输入备注" clearable />
					</el-form-item>
				</el-row>
			</el-form>

			<!-- 修改按钮和金额显示的布局 -->
			<div class="mb10 flex justify-between items-center" style="margin-top: 10px;">
				<div class="flex items-center gap-4">
					<!-- 左侧放置按钮 -->
					<el-button type="primary" icon="ele-Plus" @click="addrkdDetail">新增入库明细</el-button>
				</div>
				<div class="flex items-center gap-6">
					<!-- 右侧放置金额信息 -->
					<div class="amount-item">
						<span class="amount-label">总金额：</span>
						<span class="amount-value">{{ (ruleForm.totalAmt || 0).toFixed(2) }}</span>
					</div>
					<div class="amount-item">
						<span class="amount-label">优惠金额：</span>
						<el-input-number v-model="ruleForm.discountAmt" :min="0" :precision="2" :max="ruleForm.totalAmt || 0" @change="updateActualAmount" :controls="false" class="compact-input" />
					</div>
					<div class="amount-item">
						<span class="amount-label">实际金额：</span>
						<el-input-number v-model="ruleForm.actualAmt" :min="0" :precision="2" :max="ruleForm.totalAmt || 0" @change="updateDiscountAmount" :controls="false" class="compact-input" />
					</div>
				</div>
			</div>

			<el-table :data="tableData" tooltip-effect="light" row-key="id" border="" class="tcTable">
				<el-table-column type="index" label="序号" width="55" align="center" />

				<el-table-column prop="goodsId" label="商品" width="150" show-overflow-tooltip="">
					<template #default="scope">
						<el-select filterable v-model="scope.row.goodsId" placeholder="请选择商品" class="85" @change="getGoodsDetail(scope.row, scope.row.goodsId)">
							<el-option v-for="(item, index) in warehousegoodsDropdownList" :key="index" :value="item.value" :label="item.label" />
						</el-select>
					</template>
				</el-table-column>
				<el-table-column prop="productCode" label="商品编码" show-overflow-tooltip="" width="90" />
				<el-table-column prop="brandName" label="品牌" show-overflow-tooltip="" width="90" />
				<el-table-column prop="specsName" label="规格" show-overflow-tooltip="" width="90" />
				<el-table-column prop="unit" label="单位" width="75">
					<template #default="scope">
						<el-select clearable filterable disabled="" v-model="scope.row.unit" placeholder=" ">
							<el-option v-for="(item, index) in WarehouseUnit" :key="index" :value="item.value" :label="item.label" />
						</el-select>
					</template>
				</el-table-column>

				<el-table-column prop="documentNum" label="单据数量" width="138" show-overflow-tooltip="">
					<template #default="scope">
						<el-input-number v-model="scope.row.documentNum" placeholder="" clearable :min="0" @change="updateRowTotal(scope.row, 'documentNum')" class="w-full" />
					</template>
				</el-table-column>
				<el-table-column prop="unitprice" label="单价" width="138" show-overflow-tooltip="">
					<template #default="scope">
						<el-input-number v-model="scope.row.unitprice" placeholder="" type="number" :min="0" @change="updateRowTotal(scope.row, 'unitprice')" class="w-full" />
					</template>
				</el-table-column>

				<el-table-column prop="totalAmt" label="合计" show-overflow-tooltip="" width="138">
					<template #default="scope">
						<el-input-number v-model="scope.row.totalAmt" placeholder="" :min="0" :precision="2" @change="updateRowTotal(scope.row, 'totalAmt')" class="w-full" />
					</template>
				</el-table-column>

				<!-- <el-table-column prop="supplierId" label="供应商" width="200" show-overflow-tooltip="">
					<template #default="scope">
						<el-select clearable filterable v-model="scope.row.supplierId" placeholder="">
							<el-option v-for="(item, index) in pubSupplierDropdownList" :key="index" :value="item.value" :label="item.label" />
						</el-select>
					</template>
				</el-table-column> -->
				<el-table-column label="操作" width="70" align="center" fixed="right" show-overflow-tooltip="">
					<template #default="scope">
						<el-button icon="ele-Delete" size="small" text="" type="primary" @click="deleteRkdDetail(scope.$index)"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import useCounter from '/@/stores/counter';
import { ref, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
// import { getPubSupplierDropdown } from '/@/api/main/warehousePurchaseMX';
import { PubSupplierDropdown } from '/@/api/main/pubSupplier';
import { addWarehouseInrecord, updateWarehouseInrecord, WarehouseGoodsUnit } from '/@/api/main/warehouseInrecord';
import { expirationDate } from '/@/utils/formatTime';
import { WarehousegoodsDropdown } from '/@/api/main/warehousegoods';
import { getWarehouseDropdown } from '/@/api/main/warehousePurchase';
import { useGoodsStore } from '/@/stores/goods';

//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: '',
	},
});
//父级传递来的函数，用于回调
const emit = defineEmits(['reloadTable']);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const ruleForm = ref<any>({
	discountAmt: 0, // 优惠金额
	actualAmt: 0, // 实际金额
	totalAmt: 0, // 总金额
});
let tableMxDeleted = ref<any>([]);
const counterStore = useCounter();
const tableData = ref<any>([]);
const warehouseDropdownList = ref<any>([]);
const loading = ref(false);
const addrkdDetail = () => {
	tableData.value.push({
		goodsId: null,
		unit: null,
		puchPrice: null,
		puchQty: null,
		puchAmt: null,
		supplierId: null,
		InrecordId: ruleForm.value.id,
		yjsl: null,
		unitprice: 0,
		totalAmt: '0.00',
	});
	updateTotalAmount();
};

// 修改 updateTotalAmount 函数
const updateTotalAmount = () => {
	const total = tableData.value.reduce((sum, row) => sum + (parseFloat(row.totalAmt) || 0), 0);
	ruleForm.value.totalAmt = total;
	// 更新实际金额（总金额 - 优惠金额）
	ruleForm.value.actualAmt = Math.max(0, total - (ruleForm.value.discountAmt || 0));
};

// 添加优惠金额变化时的处理函数
const updateActualAmount = (value: number) => {
	ruleForm.value.discountAmt = value;
	ruleForm.value.actualAmt = Math.max(0, ruleForm.value.totalAmt - value);
};

// 添加实际金额变化时的处理函数
const updateDiscountAmount = (value: number) => {
	ruleForm.value.actualAmt = value;
	ruleForm.value.discountAmt = Math.max(0, ruleForm.value.totalAmt - value);
};

watch(
	tableData,
	() => {
		updateGoodsInfo();
	},
	{ deep: true }
);

// 修改 updateRowTotal 函数，添加 field 参数来标识哪个字段被修改
const updateRowTotal = (row: any, field: 'documentNum' | 'unitprice' | 'totalAmt') => {
	const documentNum = parseFloat(row.documentNum) || 0;
	const unitprice = parseFloat(row.unitprice) || 0;
	const totalAmt = parseFloat(row.totalAmt) || 0;

	// 根据修改的字段来决定计算逻辑
	switch (field) {
		case 'documentNum': // 修改数量
		case 'unitprice': // 修改单价
			row.totalAmt = (documentNum * unitprice).toFixed(2);
			break;
		case 'totalAmt': // 修改合计
			// 只在数量存在时,根据合计反推单价
			if (documentNum !== 0) {
				row.unitprice = (totalAmt / documentNum).toFixed(2);
			}
			// 如果数量为0,不做任何计算,保持当前状态
			break;
	}

	updateGoodsInfo();
	updateTotalAmount();
};

// 更新商品信息到表单
const updateGoodsInfo = () => {
    ruleForm.value.goodsInfo = tableData.value
        .filter((item: any) => !item.isDelete)
        .map((item: any) => {
            const goods = warehousegoodsDropdownList.value.find((g: any) => g.value === item.goodsId);
            return goods ? goods.label : '';
        })
        .filter(Boolean)
        .join(',');
};

const getWarehouseDropdownList = async () => {
	let list = await getWarehouseDropdown();
	warehouseDropdownList.value = list.data.result ?? [];
};
getWarehouseDropdownList();

const goodsStore = useGoodsStore();

// 获取商品详情
const getGoodsDetail = (row: any, val: any) => {
	const goodsInfo = goodsStore.getGoodsDetail(val);
	debugger;
	if (goodsInfo) {
		row.tradename = goodsInfo.name || goodsInfo.label || '';
		row.brandName = goodsInfo.brand;
		row.barcode = goodsInfo.id;
		row.productCode = goodsInfo.code;
		row.specsName = goodsInfo.specs;
		row.unit = goodsInfo.unit || '';
		row.auxiliaryunit = goodsInfo.auxiliaryunit || '';
		updateGoodsInfo();
	}
};

// 初始化时加载商品列表
onMounted(async () => {
	await goodsStore.fetchGoodsList();
});

const WarehouseUnit = ref<any>([]);

const WareUnit = async () => {
	var res = await WarehouseGoodsUnit();
	WarehouseUnit.value = res.data.result ?? [];
};
const pubSupplierDropdownList = ref<any>([]);
const getPubSupplierDropdownList = async () => {
	let list = await PubSupplierDropdown();
	pubSupplierDropdownList.value = list.data.result ?? [];
	console.log('供应商----', list);
};
getPubSupplierDropdownList();
// 商品
const warehousegoodsDropdownList = ref<any>([]);
const getWarehousegoodsDropdownList = async () => {
	await goodsStore.fetchGoodsList();
	warehousegoodsDropdownList.value = goodsStore.dropdownList;
};
getWarehousegoodsDropdownList();
// 弹窗里删除
const deleteRkdDetail = (index: number) => {
	var deleteRow = tableData.value[index];
	if (deleteRow.id > 0) {
		deleteRow.isDelete = true;
		tableMxDeleted.value.push(deleteRow);
	}
	tableData.value.splice(index, 1);
	updateTotalAmount();
};

// 自动计算过期时间
const changeDate = (row: any) => {
	if (row.productDate && row.shelflife && row.shelflifeUnit) {
		row.expires = expirationDate(row.productDate, row.shelflife, row.shelflifeUnit);
	}
};

// 打开弹窗
const openDialog = (row: any, data: any) => {
	ruleForm.value = JSON.parse(JSON.stringify(row));
	ruleForm.value.totalAmt = ruleForm.value.totalAmt || 0; // 确保 totalAmt 有一个初始值
	tableData.value = data;
	updateTotalAmount(); // 初始化总金额
	isShowDialog.value = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit('reloadTable', 'current', ruleForm.value);
	isShowDialog.value = false;
	setTimeout(() => {
		loading.value = false;
	}, 500);
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			loading.value = true;
			let values = ruleForm.value;

			if (values.warehouseid == undefined || values.inhouseType == undefined) {
				return ElMessage.warning('请选择仓库及入库类型');
			}
			let params = {
				AddWarehouseInrecordInput: values,
				listMx: tableData.value,
			};
			if (tableMxDeleted.value.length > 0) {
				params.listMx = params.listMx.concat(tableMxDeleted.value);
				tableMxDeleted.value = [];
			}
			await addWarehouseInrecord(params);
			
			// 清除相关缓存，确保明细数据能够刷新
			if (values.id) {
				// 导入缓存工具
				const { requestCache } = await import('/@/utils/request-cache');
				// 清除入库单明细的缓存
				requestCache.delete('warehouseInrecordMX/page', {
					inrecordId: values.id,
					page: 1,
					pageSize: 1000
				});
			}
			
			closeDialog();
		} else {
			loading.value = false;
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: 'error',
			});
		}
	});
};

WareUnit();

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<style lang="scss" scoped>
.el-form-item {
	height: 32px;
}

.showPop {
	margin-top: 8px;

	.addBtn {
		margin-bottom: 6px;
	}
}

.rkdTable {
	width: 100%;
	height: 400px;

	:deep(.el-date-editor) {
		width: 100% !important;
	}
}

.el-form-item--default {
	margin-bottom: 6px;
}

.flex {
	display: flex;
}

.justify-between {
	justify-content: space-between;
}

.items-center {
	align-items: center;
}

.text-lg {
	font-size: 1.125rem;
}

.font-bold {
	font-weight: 700;
}

.amount-item {
	display: flex;
	align-items: center;

	.amount-label {
		white-space: nowrap;
		color: #606266;
		margin-right: 8px;
	}

	.amount-value {
		font-size: 1.125rem;
		font-weight: 600;
		color: #409eff;
	}
}

.compact-input {
	width: 120px;

	:deep(.el-input-number__decrease),
	:deep(.el-input-number__increase) {
		display: none;
	}

	:deep(.el-input__wrapper) {
		padding: 0 8px;
	}

	:deep(.el-input__inner) {
		text-align: right;
	}
}

.gap-4 {
	gap: 1rem;
}

.gap-6 {
	gap: 1.5rem;
}
</style>




