﻿using System;

namespace Admin.NET.Application;

/// <summary>
/// 发票管理输出参数
/// </summary>
public class InvoiceManageOutput
{
    public long Id { get; set; }
    /// <summary>
    /// 时间
    /// </summary>
    public DateTime? Time { get; set; }

    /// <summary>
    /// 开票人
    /// </summary>
    public string? Contacts { get; set; }

    /// <summary>
    /// 发票性质
    /// </summary>
    public int? NatureInvoice { get; set; }

    /// <summary>
    /// 发票时间
    /// </summary>
    public DateTime? InvoiceDate { get; set; }

    /// <summary>
    /// 来往单位
    /// </summary>
    public string? ComeUnit { get; set; }

    /// <summary>
    /// 发票号码
    /// </summary>
    public string? InvoiceNum { get; set; }

    /// <summary>
    /// 未税金额
    /// </summary>
    public decimal? UntaxedMoney { get; set; }

    /// <summary>
    /// 税额
    /// </summary>
    public decimal? TaxAmount { get; set; }

    /// <summary>
    /// 发票金额
    /// </summary>
    public decimal? InvoiceValue { get; set; }

    /// <summary>
    /// 税率
    /// </summary>
    public string? TaxRate { get; set; }

    /// <summary>
    /// 收款单号
    /// </summary>
    public string? ReceiptNum { get; set; }

    /// <summary>
    /// 发票状态
    /// </summary>
    public int? InvoiceStatus { get; set; }
    /// <summary>
    /// 发票种类 蓝票0，红票1
    /// </summary>
    public int? InvoiceType { get; set; }
    /// <summary>
    /// 发票类型 普票0，专票1
    /// </summary>
    public int? InvoiceTypeLx { get; set; }
    /// <summary>
    /// 发票明细
    /// </summary>
    public List<AddInvoiceManageDetailInput> listMx { get; set; }

    /// <summary>
    /// 发票地址
    /// </summary>
    public string? InvoiceAddress { get; set; }
}


public class PullOutPut 
{
    public int code { get; set; } 
    public string message { get; set; } 
}


