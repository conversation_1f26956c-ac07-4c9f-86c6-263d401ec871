﻿using System;

namespace Admin.NET.Application;

/// <summary>
/// 商品出库输出参数
/// </summary>
public class WarehouseoutOutput
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }
    /// <summary>
    /// 仓库ID
    /// </summary>
    public long WarehouseId { get; set; }
    /// <summary>
    /// 出库单号
    /// </summary>
    public string? OutOrder { get; set; }
    /// <summary>
    /// 出库类型
    /// </summary>
    public int? Outboundtype { get; set; }

    /// <summary>
    /// 出库时间
    /// </summary>
    public DateTime? Deliverytime { get; set; }

    /// <summary>
    /// 审核状态
    /// </summary>
    public string? Auditstatus { get; set; }
    /// <summary>
    /// 客户Id
    /// </summary>
    public long CustomId { get; set; }
    /// <summary>
    /// 客户名称
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    public string? Contacts { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }


    /// <summary>
    /// 物流单号
    /// </summary>
    public string? TrackingNumber { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 出库状态
    /// </summary>
    public int? Outboundstatus { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string GoodsName { get; set; }

    /// <summary>
    /// 出库数量
    /// </summary>
    public int OutCount { get; set; }

    /// <summary>
    /// 实际出库数量
    /// </summary>
    public int TrueOutCount { get; set; }

    /// <summary>
    /// 上级单号
    /// </summary>
    public string? SuperiorNum { get; set; }

    /// <summary>
    /// 是否缺货
    /// </summary>
    public bool StockOrNot { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long CreateUserId { get; set; }
    /// <summary>
    /// 创建者
    /// </summary>
    public string CreateUserName { get; set; }
    /// <summary>
    /// 商品信息
    /// </summary>
    public string GoodsInfo { get; set; }
    /// <summary>
    /// 总金额
    /// </summary>
    public decimal? TotalAmt { get; set; }

    /// <summary>
    /// 优惠金额
    /// </summary>
    public decimal? DiscountAmt { get; set; }

    /// <summary>
    /// 实际金额
    /// </summary>
    public decimal? ActualAmt { get; set; }
}


