<template>
    <el-collapse-item name="materialList">
        <template #title>
            <div style="width: 100%; display: flex; justify-content: space-between; align-items: center;">
                <el-text size="large">原料列表</el-text>
                <el-button icon="ele-Plus" :loading="loading" text="" type="primary"
                    @click.stop="addMaterial">新增</el-button>
            </div>
        </template>

        <editable-table ref="tableRef" :table-data="props.tableData" @update:table-data="handleUpdate"
            :columns="tableColumns" :loading="loading" :show-pagination="false" :required-fields="['warehouseGoodsId']"
            validation-message="原料名称不能为空" style="height: calc(30vh - var(--el-collapse-header-height));">

            <template #materialId-edit="{ row }">
                <el-select v-model="row.warehouseGoodsId" size="small" style="width: 100%" filterable
                    @change="(val: any) => handleMaterialIdChange(row, val)">
                    <el-option v-for="item in warehouseGoodsList" :key="item.warehouseGoodsId"
                        :label="item.warehousegoods.name" :value="item.warehouseGoodsId">
                    </el-option>
                </el-select>
            </template>

            <template #materialId="{ row }">
                {{ row.warehousegoods.name }}
            </template>

            <template #quantity-edit="{ row }">
                <el-input-number v-model="row.quantity" :min="0" :precision="2" size="small" style="width: 100%"
                    controls-position="right" />
            </template>
        </editable-table>
    </el-collapse-item>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import EditableTable from '/@/components/editableTable/index.vue';
import { getWarehouseGoodsList, getWarehouseStoreList } from '/@/api/main/processOrderScheme';
import { ElMessage } from 'element-plus';


const props = defineProps({
    tableData: {
        type: Array,
        required: true,
        default: () => []
    },
    activeItems: {
        type: Array,
        required: true,
        default: () => []
    },
    warehouseId: {
        type: String,
        required: true,
        default: ''
    }
});

const emit = defineEmits(['update:tableData']);

const loading = ref(false);
const tableRef = ref();
const warehouseGoodsList = ref<any[]>([]);
const warehouseStockList = ref<any[]>([]);

// 表格列配置
const tableColumns = [
    { type: 'index', label: '序号' },
    { prop: 'warehouseGoodsId', label: '原料名称', slot: 'materialId', showOverflowTooltip: true, editable: true },
    { prop: 'warehousegoods.code', label: '原料编码', showOverflowTooltip: true, editable: false },
    { prop: 'warehousegoods.specs', label: '规格型号', showOverflowTooltip: true, editable: false },
    { prop: 'warehousegoods.warehouseGoodsUnit.name', label: '单位', editable: false },
    { prop: 'quantity', label: '数量', slot: 'quantity', width: 110, editable: true },
    { type: 'operation', label: '操作', width: 140, fixed: 'right' }
];

const getWarehouseGoodsOptions = () => {
    getWarehouseGoodsList({}).then((res: any) => {
        warehouseGoodsList.value = res.data.result;
    });
};

const handleMaterialIdChange = (row: any, val: any) => {
    setMaterialInfo(row, val);
};

const setMaterialInfo = (row: any, val: any) => {
    if (val > 0) {
        const material = warehouseGoodsList.value.find((item: any) => item.warehouseGoodsId === val);
        if (material) {
            row.warehousegoods = material.warehousegoods;
            // 设置库存信息用于校验
            const stock = warehouseStockList.value.find(x => x.tradeID === val);
            row.maxQty = stock?.goodProduct ?? 0;  // 使用良品数作为最大数量
            row.vacancy = !!!stock ? false : material.warehousegoods.vacancy;
        }
    }
};

watch(() => props.warehouseId, (val: string) => {
    if (val) {
        // 获取仓库商品列表
        getWarehouseStoreList({ WarehouseId: val }).then((res: any) => {
            warehouseGoodsList.value = res.data.result;
            warehouseStockList.value = res.data.result; // 保存库存数据用于校验
            
            // 更新现有数据的库存信息
            const newTableData = [...props.tableData];
            for (let i = 0; i < newTableData.length; i++) {
                if (newTableData[i].warehouseGoodsId) {
                    const stock = warehouseStockList.value.find(x => x.tradeID === newTableData[i].warehouseGoodsId);
                    newTableData[i].maxQty = stock?.goodProduct ?? 0;  // 使用良品数作为最大数量
                    newTableData[i].vacancy = !!!stock ? false : newTableData[i].warehousegoods?.vacancy;
                }
            }
            emit('update:tableData', newTableData);
        });
    }
}, { immediate: true });

// 添加新的原料行
const addMaterial = () => {
    if (!props.activeItems.includes('materialList')) {
        // eslint-disable-next-line vue/no-mutating-props
        props.activeItems.push('materialList');
    }

    // 使用通用组件的 addItem 方法
    tableRef.value.addItem({
        materialCode: '',
        materialName: '',
        specification: '',
        unit: '个',
        quantity: 0,
        maxQty: 0,
        vacancy: false
    });
};

// 检查表格有效性
const valid = () => {
    return tableRef.value.valid();
};

// 重置编辑状态
const resetEditStatus = () => {
    if (tableRef.value) {
        tableRef.value.resetEditStatus();
    }
};

const handleUpdate = (val: any) => {
    // 直接更新数据，不进行良品库存数量校验
    emit('update:tableData', val);
}

// 暴露方法
defineExpose({
    loading,
    valid,
    resetEditStatus
});
</script>

<style scoped>
/* 确保编辑中的输入框宽度合适 */
.el-input,
.el-select,
.el-input-number {
    width: 100%;
}

/* 隐藏分页组件 */
:deep(.material-table .el-pagination) {
    display: none !important;
}
</style>