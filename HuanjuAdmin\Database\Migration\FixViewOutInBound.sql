-- 修复View_OutInBound视图的脚本
-- 解决GoodProduct字段显示问题

-- 1. 删除现有视图
DROP VIEW IF EXISTS `View_OutInBound`;

-- 2. 重新创建修复后的视图
CREATE VIEW `View_OutInBound` AS
SELECT 
    -- 出库记录
    o.Id,
    '0' as Type, -- 0表示出库
    o.Id as ParentId,
    o.<PERSON>um,
    o.OutBoundCount as OutInCount,
    o.PrintCount,
    o.WarehouseBatchId,
    COALESCE(wb.Batchnumber, '') as BatchNumber,
    COALESCE(wo.OutOrder, '') as OrderNumber,
    wo.CustomId,
    NULL as SupplierId,
    COALESCE(pc.Name, '') as Company,
    COALESCE(pc.Phone, '') as TelPhone,
    wh.Id as WarehouseId,
    COALESCE(wh.Name, '') as WarehouseName,
    wg.Id as GoodsId,
    COALESCE(wg.Name, '') as GoodsName,
    COALESCE(wg.barcode, '') as BarCode,
    COALESCE(wg.Code, '') as Code,
    COALESCE(wg.Brand, '') as Brand,
    COALESCE(wg.Specs, '') as Specs,
    wu.Id as UnitId,
    COALESCE(wu.Name, '') as UnitName,
    COALESCE(cu.RealName, '') as CreateUserName,
    COALESCE(uu.RealName, '') as UpdateUserName,
    o.CreateTime,
    o.UpdateTime,
    o.TenantId,
    0 as IsDelete,
    o.GoodProduct -- 添加良品次品字段
FROM outboundrecord o
LEFT JOIN warehouseoutmx omx ON o.WarehouseOutMxId = omx.Id
LEFT JOIN warehouseout wo ON omx.OutId = wo.Id
LEFT JOIN warehousebatch wb ON o.WarehouseBatchId = wb.Id
LEFT JOIN pubcustom pc ON wo.CustomId = pc.Id
LEFT JOIN warehouse wh ON wo.WarehouseId = wh.Id
LEFT JOIN warehousegoods wg ON omx.goodsId = wg.Id
LEFT JOIN warehousegoodsunit wu ON omx.Unit = wu.Id
LEFT JOIN sysuser cu ON o.CreateUserId = cu.Id
LEFT JOIN sysuser uu ON o.UpdateUserId = uu.Id
WHERE o.IsDelete = 0

UNION ALL

SELECT 
    -- 入库记录
    i.Id,
    '1' as Type, -- 1表示入库
    i.Id as ParentId,
    i.OrderNum,
    i.InBoundCount as OutInCount,
    i.PrintCount,
    i.WarehouseBatchId,
    COALESCE(wb.Batchnumber, '') as BatchNumber,
    COALESCE(wi.OrderNumber, '') as OrderNumber,
    NULL as CustomId,
    wi.SupplierId,
    COALESCE(ps.Name, '') as Company,
    COALESCE(ps.Phone, '') as TelPhone,
    wh.Id as WarehouseId,
    COALESCE(wh.Name, '') as WarehouseName,
    wg.Id as GoodsId,
    COALESCE(wg.Name, '') as GoodsName,
    COALESCE(wg.barcode, '') as BarCode,
    COALESCE(wg.Code, '') as Code,
    COALESCE(wg.Brand, '') as Brand,
    COALESCE(wg.Specs, '') as Specs,
    wu.Id as UnitId,
    COALESCE(wu.Name, '') as UnitName,
    COALESCE(cui.RealName, '') as CreateUserName,
    COALESCE(uui.RealName, '') as UpdateUserName,
    i.CreateTime,
    i.UpdateTime,
    i.TenantId,
    0 as IsDelete,
    i.GoodProduct -- 添加良品次品字段
FROM inboundrecord i
LEFT JOIN warehouseinrecordmx imx ON i.WarehouseIncordMxId = imx.Id
LEFT JOIN warehouseinrecord wi ON imx.InrecordId = wi.Id
LEFT JOIN warehousebatch wb ON i.WarehouseBatchId = wb.Id
LEFT JOIN pubsupplier ps ON wi.SupplierId = ps.Id
LEFT JOIN warehouse wh ON wi.Warehouseid = wh.Id
LEFT JOIN warehousegoods wg ON imx.GoodsId = wg.Id
LEFT JOIN warehousegoodsunit wu ON imx.Unit = wu.Id
LEFT JOIN sysuser cui ON i.CreateUserId = cui.Id
LEFT JOIN sysuser uui ON i.UpdateUserId = uui.Id
WHERE i.IsDelete = 0;

SELECT 'View_OutInBound视图修复完成！' as message; 