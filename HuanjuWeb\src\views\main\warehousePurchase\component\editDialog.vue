<template>
	<div class="warehousePurchase-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="1150" draggable="" :close-on-click-modal="false">
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="ruleForm.id" />
					</el-form-item>
					<el-col :xs="24" :sm="7" :md="7" :lg="7" :xl="7" class="mb20">
						<el-form-item label="供应商" prop="supplierId">
							<el-select clearable filterable v-model="ruleForm.supplierId" placeholder="请选择供应商">
								<el-option v-for="(item, index) in pubSupplierDropdownList" :key="index" :value="item.value" :label="item.label" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="7" :md="7" :lg="7" :xl="7" class="mb20">
						<el-form-item label="仓库" prop="warehouseId" :rules="[{ required: true, message: '仓库不能为空', trigger: 'blur' }]">
							<el-select clearable filterable v-model="ruleForm.warehouseId" placeholder="请选择仓库">
								<el-option v-for="(item, index) in warehouseDropdownList" :key="index" :value="item.value" :label="item.label" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :sm="7" :md="7" :lg="7" :xl="7" class="mb20">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="ruleForm.remark" placeholder="请输入备注" clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>

			<!-- 修改按钮和金额显示的布局 -->
			<div class="mb10 flex justify-between items-center">
				<div class="flex items-center gap-4">
					<!-- 左侧放置按钮 -->
					<el-button type="primary" icon="ele-Plus" @click="addPurchaseDetail">新增采购明细</el-button>
				</div>
				<div class="flex items-center gap-6">
					<!-- 右侧放置金额信息 -->
					<div class="amount-item">
						<span class="amount-label">总金额：</span>
						<span class="amount-value">{{ (ruleForm.totalAmt || 0).toFixed(2) }}</span>
					</div>
					<div class="amount-item">
						<span class="amount-label">优惠金额：</span>
						<el-input-number v-model="ruleForm.discountAmt" :min="0" :precision="2" :max="ruleForm.totalAmt || 0" @change="updateActualAmount" :controls="false" class="compact-input" />
					</div>
					<div class="amount-item">
						<span class="amount-label">实际金额：</span>
						<el-input-number v-model="ruleForm.actualAmt" :min="0" :precision="2" :max="ruleForm.totalAmt || 0" @change="updateDiscountAmount" :controls="false" class="compact-input" />
					</div>
				</div>
			</div>

			<el-table :data="tableDataMX" tooltip-effect="light" border="" class="tcTable">
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="goodsId" label="商品" width="150" show-overflow-tooltip="">
					<template #default="scope">
						<el-select clearable filterable v-model="scope.row.goodsId" placeholder="请选择商品" @change="getGoodsDetail(scope.row, scope.row.goodsId)">
							<el-option v-for="(item, index) in warehousegoodsDropdownList" :key="index" :value="item.value" :label="item.label" />
						</el-select>
					</template>
				</el-table-column>

				<!-- 	<el-table-column prop="barcode" label="商品条码" show-overflow-tooltip="" width="109" /> -->
				<el-table-column prop="productCode" label="商品编码" show-overflow-tooltip="" width="109" />
				<el-table-column prop="brandName" label="品牌" show-overflow-tooltip="" width="109" />
				<el-table-column prop="specsName" label="规格" show-overflow-tooltip="" width="109" />
				<el-table-column prop="unit" label="单位" show-overflow-tooltip="">
					<template #default="scope">
						<el-select clearable filterable disabled="" v-model="scope.row.unit" placeholder=" ">
							<el-option v-for="(item, index) in WarehouseUnit" :key="index" :value="item.value" :label="item.label" />
						</el-select>
					</template>
				</el-table-column>

				<el-table-column prop="puchQty" label="数量" show-overflow-tooltip="" width="138">
					<template #default="scope">
						<el-input-number v-model="scope.row.puchQty" placeholder="" :min="0" :precision="2" @change="updateRowTotal(scope.row, 'puchQty')" class="w-full" />
					</template>
				</el-table-column>

				<el-table-column prop="puchPrice" label="单价" show-overflow-tooltip="" width="138">
					<template #default="scope">
						<el-input-number v-model="scope.row.puchPrice" placeholder="" :min="0" :precision="2" @change="updateRowTotal(scope.row, 'puchPrice')" class="w-full" />
					</template>
				</el-table-column>

				<el-table-column prop="puchAmt" label="合计" show-overflow-tooltip="" width="138">
					<template #default="scope">
						<el-input-number v-model="scope.row.puchAmt" placeholder="" :min="0" :precision="2" @change="updateRowTotal(scope.row, 'puchAmt')" class="w-full" />
					</template>
				</el-table-column>

				<!-- <el-table-column prop="supplierId" label="供应商" width="200" show-overflow-tooltip="">
					<template #default="scope">
						<el-select clearable filterable v-model="scope.row.supplierId" placeholder="请选择供应商">
							<el-option v-for="(item, index) in pubSupplierDropdownList" :key="index" :value="item.value" :label="item.label" />
						</el-select>
					</template>
				</el-table-column> -->
				<el-table-column label="操作" width="70" align="center" fixed="right" show-overflow-tooltip="">
					<template #default="scope">
						<el-button icon="ele-Delete" size="small" text="" type="primary" @click="deletePurchaseDetail(scope.$index)" v-auth="'warehousePurchaseMX:delete'"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>



<script lang="ts" setup>
import { useGoodsStore } from '/@/stores/goods';
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormRules } from 'element-plus';
import { addWarehousePurchase } from '/@/api/main/warehousePurchase';
import { getWarehouseDropdown } from '/@/api/main/warehousePurchase';
import { PubSupplierDropdown } from '/@/api/main/pubSupplier';
import { WarehouseGoodsUnit } from '/@/api/main/warehouseInrecord';
import { WarehousegoodsDropdown } from '/@/api/main/warehousegoods';
let tableDataMX = ref<any>([]);
const goodsStore = useGoodsStore();
let tableMxDeleted = ref<any>([]);
const loading = ref(false);

const addPurchaseDetail = () => {
	tableDataMX.value.push({
		id: null,
		goodsId: null, // or appropriate initial value
		unit: null,
		puchPrice: null,
		puchQty: null,
		puchAmt: null,
		supplierId: null,
		purchaseId: ruleForm.value.id,
		isDelete: false,
	});
	updateTotalAmount();
};

// 修改 updateTotalAmount 函数
const updateTotalAmount = () => {
	const total = tableDataMX.value.reduce((sum, row) => sum + (parseFloat(row.puchAmt) || 0), 0);
	ruleForm.value.totalAmt = total;
	// 更新实际金额（总金额 - 优惠金额）
	ruleForm.value.actualAmt = Math.max(0, total - (ruleForm.value.discountAmt || 0));
};

// 添加优惠金额变化时的处理函数
const updateActualAmount = (value: number) => {
	ruleForm.value.discountAmt = value;
	ruleForm.value.actualAmt = Math.max(0, ruleForm.value.totalAmt - value);
};

// 添加实际金额变化时的处理函数
const updateDiscountAmount = (value: number) => {
	ruleForm.value.actualAmt = value;
	ruleForm.value.discountAmt = Math.max(0, ruleForm.value.totalAmt - value);
};

watch(
	tableDataMX,
	() => {
		updateGoodsInfo();
	},
	{ deep: true }
);

// 修改 updateRowTotal 函数，添加 field 参数来标识哪个字段被修改
const updateRowTotal = (row: any, field: 'puchQty' | 'puchPrice' | 'puchAmt') => {
	const puchQty = parseFloat(row.puchQty) || 0;
	const puchPrice = parseFloat(row.puchPrice) || 0;
	const puchAmt = parseFloat(row.puchAmt) || 0;

	// 根据修改的字段来决定计算逻辑
	switch (field) {
		case 'puchQty': // 修改数量
		case 'puchPrice': // 修改单价
			row.puchAmt = (puchQty * puchPrice).toFixed(2);
			break;
		case 'puchAmt': // 修改合计
			// 只在数量存在时,根据合计反推单价
			if (puchQty !== 0) {
				row.puchPrice = (puchAmt / puchQty).toFixed(2);
			}
			// 如果数量为0,不做任何计算,保持当前状态
			break;
	}

	updateGoodsInfo();
	updateTotalAmount();
};

// 更新商品信息到表单
const updateGoodsInfo = () => {
    ruleForm.value.goodsInfo = tableDataMX.value
        .filter((item: any) => !item.isDelete)
        .map((item: any) => {
            const goods = warehousegoodsDropdownList.value.find((g: any) => g.value === item.goodsId);
            return goods ? goods.label : '';
        })
        .filter(Boolean)
        .join(',');
};

const getsum = (row: any) => {
	row.puchPrice = parseFloat(row.value).toFixed(2);
};

// 获取商品详情
const getGoodsDetail = (row: any, val: any) => {
	const goodsInfo = goodsStore.getGoodsDetail(val);
	if (goodsInfo) {
		row.tradename = goodsInfo.name || goodsInfo.label || '';
		row.brandName = goodsInfo.brand;
		row.barcode = goodsInfo.id;
		row.productCode = goodsInfo.code;
		row.specsName = goodsInfo.specs;
		row.unit = goodsInfo.unit || '';
		row.auxiliaryunit = goodsInfo.auxiliaryunit || '';
		updateGoodsInfo();
	}
};

const WarehouseUnit = ref<any>([]);
const WareUnit = async () => {
	var res = await WarehouseGoodsUnit();
	WarehouseUnit.value = res.data.result ?? [];
};
const deletePurchaseDetail = (index: number) => {
	var deleteRow = tableDataMX.value[index];
	// console.log(deleteRow);
	if (deleteRow.id > 0) {
		deleteRow.isDelete = true;
		tableMxDeleted.value.push(deleteRow);
	}
	tableDataMX.value.splice(index, 1);
	updateTotalAmount();
};
WareUnit();

const warehousegoodsDropdownList = ref<any>([]);
const getWarehousegoodsDropdownList = async () => {
	await goodsStore.fetchGoodsList();
	warehousegoodsDropdownList.value = goodsStore.dropdownList;
};
getWarehousegoodsDropdownList();

const pubSupplierDropdownList = ref<any>([]);
const getPubSupplierDropdownList = async () => {
	let list = await PubSupplierDropdown();
	pubSupplierDropdownList.value = list.data.result ?? [];
};
getPubSupplierDropdownList();

//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: '',
	},
});
//父级传递来的函数，用于回调
const emit = defineEmits(['reloadTable']);

const ruleFormRef = ref();
const isShowDialog = ref(false);
const ruleForm = ref<any>({
	discountAmt: 0, // 优惠金额
	actualAmt: 0, // 实际金额
	totalAmt: 0, // 总金额
	goodsInfo: '', // 商品信息
});
//自行添加其他规则
const rules = ref<FormRules>({});

// 打开弹窗
const openDialog = (row: any, rowMX: any, str: any) => {
    // 对row进行深拷贝
    ruleForm.value = JSON.parse(JSON.stringify(row));
    ruleForm.value.totalAmt = ruleForm.value.totalAmt || 0;
    
    if (str == '库存') {
        tableDataMX.value = [];
        rowMX.forEach((e: any) => {
            tableDataMX.value.push({
                id: null,
                goodsId: e.tradeID,
                unit: e.unit,
                productCode: e.productCode,
                brandName: e.brandName,
                specsName: e.specsName,
                puchQty: e.puchQty || 0,
                puchPrice: e.puchPrice || 0,
                puchAmt: e.puchAmt || 0,
                isDelete: false
            });
        });
    } else {
        // 如果不是库存，直接使用现有数据
        tableDataMX.value = rowMX ? JSON.parse(JSON.stringify(rowMX)) : [];
    }
    
    updateTotalAmount();
    updateGoodsInfo();  // 确保初始化时同步商品信息
    isShowDialog.value = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit('reloadTable');
	isShowDialog.value = false;
	setTimeout(() => {
		loading.value = false;
	}, 500);
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
            loading.value = true;
            try {
                // 确保 goodsInfo 已经更新
                updateGoodsInfo();
                
                let values = {
                    addWarehousePurchaseInput: {
                        ...ruleForm.value,
                        // 确保 goodsInfo 是字符串
                        goodsInfo: ruleForm.value.goodsInfo
                    },
                    listMx: tableDataMX.value
                };

                if (tableMxDeleted.value.length > 0) {
                    values.listMx = values.listMx.concat(tableMxDeleted.value);
                    tableMxDeleted.value = [];
                }

                await addWarehousePurchase(values);
                ElMessage.success('操作成功');
                closeDialog();
            } catch (error) {
                console.error('提交失败:', error);
                ElMessage.error('操作失败');
            } finally {
                loading.value = false;
            }
        } else {
            console.log('error submit!', fields);
        }
    });
};

const warehouseDropdownList = ref<any>([]);
const getWarehouseDropdownList = async () => {
	let list = await getWarehouseDropdown();
	warehouseDropdownList.value = list.data.result ?? [];
};
getWarehouseDropdownList();

// 页面加载时
onMounted(async () => {
	// 添加 goodsStore 初始化
	await goodsStore.fetchGoodsList();
});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>

<style lang="scss" scoped>
.el-form-item--default {
	margin-bottom: 6px;
}

.flex {
	display: flex;
}

.justify-between {
	justify-content: space-between;
}

.items-center {
	align-items: center;
}

.text-lg {
	font-size: 1.125rem;
}

.font-bold {
	font-weight: 700;
}

.amount-item {
	display: flex;
	align-items: center;

	.amount-label {
		white-space: nowrap;
		color: #606266;
		margin-right: 8px;
	}

	.amount-value {
		font-size: 1.125rem;
		font-weight: 600;
		color: #409eff;
	}
}

.compact-input {
	width: 120px;

	:deep(.el-input-number__decrease),
	:deep(.el-input-number__increase) {
		display: none;
	}

	:deep(.el-input__wrapper) {
		padding: 0 8px;
	}

	:deep(.el-input__inner) {
		text-align: right;
	}
}

.gap-4 {
	gap: 1rem;
}

.gap-6 {
	gap: 1.5rem;
}
</style>
