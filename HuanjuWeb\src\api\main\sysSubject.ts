﻿import request from '/@/utils/request';
enum Api {
	AddSubject = '/api/Subject/add',
	DeleteSubject = '/api/Subject/delete',
	UpdateSubject = '/api/Subject/update',
	PageSubject = '/api/Subject/page',
	ImportSubject = '/api/Subject/Import'
}

// 增加科目
export const addSubject = (params?: any) =>
	request({
		url: Api.AddSubject,
		method: 'post',
		data: params,
	});

// 删除科目
export const deleteSubject = (params?: any) =>
	request({
		url: Api.DeleteSubject,
		method: 'post',
		data: params,
	});

// 编辑科目
export const updateSubject = (params?: any) =>
	request({
		url: Api.UpdateSubject,
		method: 'post',
		data: params,
	});

// 分页查询科目
export const pageSubject = (params?: any) =>
	request({
		url: Api.PageSubject,
		method: 'post',
		data: params,
	});

//导入	
export const ImportSubject = (params?: any) =>
	request({
		url: Api.ImportSubject,
		method: 'post',
		data: params,
		headers: { 'Content-Type': 'multipart/form-data' },
		// 添加这行来防止浏览器自动设置 boundary
		transformRequest: [(data) => data]
	});

