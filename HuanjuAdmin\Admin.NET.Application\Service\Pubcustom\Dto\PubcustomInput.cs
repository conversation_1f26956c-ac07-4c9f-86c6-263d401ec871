﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 客户信息基础输入参数
    /// </summary>
    public class PubcustomBaseInput
    {
        /// <summary>
        /// 客户名称
        /// </summary>
        public virtual string Name { get; set; }
        
        /// <summary>
        /// 客户行业
        /// </summary>
        public virtual string? Type { get; set; }
        
        /// <summary>
        /// 联系人
        /// </summary>
        public virtual string Contacts { get; set; }
        
        /// <summary>
        /// 电话
        /// </summary>
        public virtual string Phone { get; set; }
        
        /// <summary>
        /// 公用标志
        /// </summary>
        public virtual bool IsCommunal { get; set; }
        
        /// <summary>
        /// 开户行
        /// </summary>
        public virtual string? BankName { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public virtual string? Remark { get; set; }
        
        /// <summary>
        /// 银行账号
        /// </summary>
        public virtual string? BankCode { get; set; }
        
        /// <summary>
        /// 税务登记号
        /// </summary>
        public virtual string? TaxId { get; set; }
        
        /// <summary>
        /// 地址
        /// </summary>
        public virtual string? Address { get; set; }
        
        /// <summary>
        /// 机构ID
        /// </summary>
        public virtual long OrgId { get; set; }
        
    }

    /// <summary>
    /// 客户信息分页查询输入参数
    /// </summary>
    public class PubcustomInput : BasePageInput
    {
        /// <summary>
        /// 客户名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 联系人
        /// </summary>
        public string Contacts { get; set; }
        
        /// <summary>
        /// 电话
        /// </summary>
        public string Phone { get; set; }
        
        /// <summary>
        /// 公用标志
        /// </summary>
        public bool IsCommunal { get; set; }
        
    }

    /// <summary>
    /// 客户信息增加输入参数
    /// </summary>
    public class AddPubcustomInput : PubcustomBaseInput
    {
    }

    /// <summary>
    /// 客户信息删除输入参数
    /// </summary>
    public class DeletePubcustomInput : BaseIdInput
    {
    }

    /// <summary>
    /// 客户信息更新输入参数
    /// </summary>
    public class UpdatePubcustomInput : PubcustomBaseInput
    {
        /// <summary>
        /// Id
        /// </summary>
        [Required(ErrorMessage = "Id不能为空")]
        public long Id { get; set; }
        
    }

    /// <summary>
    /// 客户信息主键查询输入参数
    /// </summary>
    public class QueryByIdPubcustomInput : DeletePubcustomInput
    {

    }
