﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 商品批次表
    /// </summary>
    [SugarTable("WarehouseBatch","")]
    [Tenant("1300000000001")]
    public class warehousebatch  : EntityBase
    {
        /// <summary>
        /// 库存ID
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "库存ID")]
        public long InventoryId { get; set; }
        /// <summary>
        /// 批次号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "批次号", Length = 200)]
        public string Batchnumber { get; set; }
        /// <summary>
        /// 良品数量
        /// </summary>
        [SugarColumn(ColumnDescription = "良品数量")]
        public int? GoodProductNum { get; set; }
        /// <summary>
        /// 次品数量
        /// </summary>
        [SugarColumn(ColumnDescription = "次品数量")]
        public int? RejectNum { get; set; }
        /// <summary>
        /// 生产日期
        /// </summary>
        [SugarColumn(ColumnDescription = "生产日期")]
        public DateTime? ProduceTime { get; set; }
        /// <summary>
        /// 保质期
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "保质期")]
        public int WarrantyTime { get; set; }
        /// <summary>
        /// 过期预警（天）
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "过期预警（天）")]
        public int? ExpiryReminder  { get; set; }
        /// <summary>
        /// 保质期状态（-1 未设置 0-正常 1-临期 2-过期）
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "保质期状态（-1 未设置 0-正常 1-临期 2-过期）")]
        public int? ShelflifeStatus  { get; set; }
        /// <summary>
        /// 到期时间
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "到期时间")]
        public DateTime ExpirationTime { get; set; }
        /// <summary>
        /// 成本
        /// </summary>
        [SugarColumn(ColumnDescription = "成本")]
        public decimal? Cost { get; set; }
        /// <summary>
        /// 租户Id
        /// </summary>
        [SugarColumn(ColumnDescription = "租户Id")]
        public long? TenantId { get; set; }
    }
}