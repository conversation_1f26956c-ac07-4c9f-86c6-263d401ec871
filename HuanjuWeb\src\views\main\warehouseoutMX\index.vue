﻿<template>
	<!-- 出库单下 -->
	<div class="warehouseoutMX-container">
		<el-card class="bomCard" shadow="hover">
			<el-form>
				<el-form-item class="bomForm">
					<span class="text-lg font-bold">出库单号：{{ props.outOrder }}</span>
					<el-button type="primary" icon="ele-Plus" @click="GetOutbound" v-auth="'warehouseout:add'" class="addBtn" :disabled="btnStatus"> 出库 </el-button>
					<el-button class="addBtn" type="primary" icon="ele-List" @click="OutBoundRecord" :disabled="btnRecord"> 出库记录 </el-button>
					<!-- <el-button class="addBtn" type="primary" icon="ele-Plus" :disabled="props.orderDetail == ''"
            @click="openAddWarehouseoutMX"> 新增 </el-button> -->
				</el-form-item>
			</el-form>
			<el-table :data="props.detailsData || []" :key="props.detailsData.length" style="width: 100%" v-loading="loading" tooltip-effect="light" row-key="id" border="" class="bomTable" :height="bomHeight">
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="outOrder" label="出库单号" width="120" show-overflow-tooltip="" />
				<!-- <el-table-column prop="warehouseId" label="仓库" show-overflow-tooltip="" >
          <template #default="scope">
          {{ filterSlots(scope.row.warehouseId, counterStore.warehouseList) }}
        </template>
</el-table-column> -->
				<el-table-column prop="tradename" label="商品名称" show-overflow-tooltip="" />
				<el-table-column prop="barcode" label="商品条码" width="90" show-overflow-tooltip="" />
				<el-table-column prop="productcode" label="商品编码" width="90" show-overflow-tooltip="" />
				<el-table-column prop="brand" label="品牌" width="90" show-overflow-tooltip="" />
				<el-table-column prop="specifications" label="规格" width="90" show-overflow-tooltip="" />
				<el-table-column label="是否良品" width="65" prop="goodProduct" show-overflow-tooltip="">
					<template #default="scope">
						<el-tag v-if="scope.row.goodProduct">良品</el-tag>
						<el-tag type="danger" v-else> 次品 </el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="vacancy" label="是否缺货" width="65" show-overflow-tooltip="">
					<template #default="scope">
						<el-tag type="danger" v-if="scope.row.vacancy">缺</el-tag>
						<el-tag v-else>否</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="unitName" label="单位" width="65" show-overflow-tooltip="" />
				<el-table-column prop="outCount" label="单据数量" width="65" show-overflow-tooltip="" />
				<el-table-column prop="unitprice" label="单价" width="65" show-overflow-tooltip="" />
				<el-table-column prop="totalAmt" label="合计" width="90"  show-overflow-tooltip="" />
				<el-table-column prop="trueOutCount" label="已出数量" width="65" show-overflow-tooltip="" />
				<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip="" v-if="auth('warehouseoutMX:edit') || auth('warehouseoutMX:delete')">
					<template #default="scope">
						<el-button 
							icon="ele-Edit" 
							size="small" 
							text="" 
							type="primary" 
							@click="(event) => { 
								event.stopPropagation(); 
								openEditWarehouseoutMX(scope.row);
							}" 
							v-auth="'warehouseoutMX:edit'">
							编辑
						</el-button>
						<el-button 
							icon="ele-Delete" 
							size="small" 
							text="" 
							type="primary" 
							@click="(event) => { 
								event.stopPropagation(); 
								delWarehouseoutMX(scope.row);
							}" 
							v-auth="'warehouseoutMX:delete'">
							删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<!-- <el-pagination v-model:currentPage="tableParams.page" v-model:page-size="tableParams.pageSize"
        :total="tableParams.total" :page-sizes="[10, 20, 50, 100]" small="" background=""
        @size-change="handleSizeChange" @current-change="handleCurrentChange"
        layout="total, sizes, prev, pager, next, jumper" /> -->
			<editDialog ref="editDialogRef" :title="editWarehouseoutMXTitle" @reloadTable="handleQuery" />
			<editDiaNum ref="editDiaNumRef" :title="editWarehouseOutNumTitle" @reloadTable="sxHandleQuery" />
			<outInBoundRecord ref="outBoundRecordRef" :title="outBoundRecordDialog" @reloadTable="sxHandleQuery" />
		</el-card>
	</div>
</template>

<script lang="ts" setup="" name="warehouseoutMX">
import { ref, watch } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { auth } from '/@/utils/authFunction';
//import { formatDate } from '/@/utils/formatTime';
import editDiaNum from '/@/views/main/warehouseout/component/editDiaNum.vue';
import editDialog from '/@/views/main/warehouseoutMX/component/editDialog.vue';
import { getOutBoundMxList, deleteWarehouseoutMX } from '/@/api/main/warehouseoutMX';
import outInBoundRecord from '/@/views/main/warehouseInrecordMX/component/OutInBoundRecord.vue';
import { listOutboundRecord } from '/@/api/main/outInBound';
// import ModifyRecord from './component/modifyRecord.vue'
const emit = defineEmits(['reloadTable']);
const editWarehouseOutNumTitle = ref('出库数量');
const outBoundRecordDialog = ref('');
const editDiaNumRef = ref();
const editDialogRef = ref();
const outBoundRecordRef = ref();
const loading = ref(false);
const btnStatus = ref(true); // 是否可以出库
const btnRecord = ref(true);

const tableData = ref<any>([]);
const tableParams = ref({
	page: 1,
	pageSize: 10,
	total: 0,
});
const editWarehouseoutMXTitle = ref('');

const props = defineProps({
	orderDetail: String,
	outOrder: String,
	outBtnStatus: Number,
	bomHeight: String,
	detailsData: {
		type: Array,
		default: () => []
	}
});

watch(
	() => props.orderDetail,
	(n) => {
		if (n) {
			handleQuery();
		} else {
			tableData.value = [];
		}
	}
);

watch(
	() => props.detailsData,
	(newData) => {
		if (newData) {
			console.log(`接收到新的明细数据，共 ${newData.length} 条`);
			// 不需要设置tableData了，因为我们直接用props.detailsData渲染
		}
	},
	{ deep: true }
);

// 监听出库状态变化
watch(
	() => props.outBtnStatus,
	(newStatus) => {
		console.log(`出库状态变化: ${newStatus}`);
		outBtnStatus();
	}
);

// 判断出库按钮是否可以使用
const outBtnStatus = () => {
	if (props.orderDetail) {
		// 设置出库记录按钮状态：待提交(0)或待出库(1)时禁用，其他状态启用
		if (props.outBtnStatus === 0 || props.outBtnStatus === 1) {
			btnRecord.value = true;
		} else {
			btnRecord.value = false;
		}
		
		// 设置出库按钮状态：待出库(1)或部分出库(2)时启用，其他状态禁用
		if (props.outBtnStatus === 1 || props.outBtnStatus === 2) {
			btnStatus.value = false; // false表示启用
		} else {
			btnStatus.value = true; // true表示禁用
		}
	} else {
		btnStatus.value = true;
		btnRecord.value = true;
	}
};
// 查询操作
const handleQuery = async () => {
	loading.value = true;
	let params = {
		page: tableParams.value.page,
		pageSize: tableParams.value.pageSize,
		OutId: props.orderDetail,
	};
	var res = await getOutBoundMxList(params);
	tableData.value = res.data.result ?? [];
	loading.value = false;
	outBtnStatus();
};
// 刷新数据
const sxHandleQuery = () => {
	handleQuery();
	emit('reloadTable');
};

const GetOutbound = (row: any) => {
	editWarehouseOutNumTitle.value = '商品出库';
	editDiaNumRef.value.openDialog(tableData.value);
};

const OutBoundRecord = async () => {
	if (!props.orderDetail) {
		return;
	}

	if (props.orderDetail) {
		let params = {
			type: '0',
			orderNumber: props.outOrder,
		};
		var res = await listOutboundRecord(params);
		if (res.data.result) {
			outBoundRecordDialog.value = '出库记录';
			outBoundRecordRef.value.openDialog(res.data.result);
		}
	}
};

// 打开编辑页面
const openEditWarehouseoutMX = (row: any) => {
	editWarehouseoutMXTitle.value = '编辑商品出库明细';
	row.OutId = props.orderDetail;
	editDialogRef.value.openDialog(row);
};

// 删除
const delWarehouseoutMX = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await deleteWarehouseoutMX(row);
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// // 改变页面容量
// const handleSizeChange = (val: number) => {
//   tableParams.value.pageSize = val;
//   handleQuery();
// };

// // 改变页码序号
// const handleCurrentChange = (val: number) => {
//   tableParams.value.page = val;
//   handleQuery();
// };

defineExpose({ handleQuery });
</script>
<style lang="scss" scoped>
.warehouseoutMX-container {
	height: 100%;
}

.bomCard {
	height: 100%;

	.bomForm {
		display: flex;
		margin-bottom: 6px;

		.el-input {
			width: 150px;
		}

		.addBtn {
			margin-left: 10px;
		}
	}

	.bomTable {
		margin-top: 6px;
	}
}

.text-lg {
	font-size: 1.125rem;
}

.font-bold {
	font-weight: 700;
}
</style>
