﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.InvoiceManage.Entity;

    public class RequestInvoice
    {
        public string key { get; set; }
        public string timestamp { get; set; }
        public string sign { get; set; }
    }
    public class ResponseInvoice
    {
        public int code { get; set; }
        public string message { get; set; }
        public string data { get; set; }
    }
    public class LoginDpptRequest
    {
        public string nsrsbh { get; set; }
        public string username { get; set; }
        public string password { get; set; }
        public string sms { get; set; }
    }

    public class TokenInfo
    {
        public string token { get; set; }
    }

    public class AsyncInvoiceRequest
    {
        public string username { get; set; }
        public string nsrsbh { get; set; }
        public string qqsj { get; set; }
        public string zzsj { get; set; }
        public string fplxdm { get; set; }
        public string ghdwmc { get; set; }
        public string ghdwdm { get; set; }
        public string page { get; set; }
        public string size { get; set; }
        public string gjbq { get; set; }

    }

    public class AsyncInvoiceResponse
    {
        public int code { get; set; }
        public string message { get; set; }
        public AsyncInvoiceResponseData data { get; set; }
    }
    public class AsyncInvoiceResponseData
    {
        public int count { get; set; }
        public AsyncInvoiceResponseDataKpxx kpxx { get; set; }
    }
    public class AsyncInvoiceResponseDataKpxx
    {
        public List<AsyncInvoiceResponseDataKpxxGroup> group { get; set; }
    }
    public class AsyncInvoiceResponseDataKpxxGroup
    {
        /// <summary>
        /// 开票日期
        /// </summary>
        public DateTime? kprq { get; set; }
        /// <summary>
        /// 发票类型代码
        /// </summary>
        public string fplxdm { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string fphm { get; set; }
        /// <summary>
        /// 合计金额
        /// </summary>
        public decimal? hjje { get; set; }
        /// <summary>
        /// 合计税额
        /// </summary>
        public decimal? hjse { get; set; }
        /// <summary>
        /// 价税合计
        /// </summary>
        public decimal? jshj { get; set; }
        /// <summary>
        /// 发票状态
        /// </summary>
        public string fpzt { get; set; }
        /// <summary>
        /// 开票人
        /// </summary>
        public string kpr { get; set; }
        /// <summary>
        /// 购方名称
        /// </summary>
        public string gfkpmc { get; set; }
        /// <summary>
        /// 购方税号
        /// </summary>
        public string gfkpsh { get; set; }
        /// <summary>
        /// 销售方名称
        /// </summary>
        public string xfkpmc { get; set; }
        /// <summary>
        /// 销售方税号
        /// </summary>
        public string xfkpsh { get; set; }
    }
    /// <summary>
    /// 修复发票请求参数
    /// </summary>
    public class RepairInvoiceRequest
    {
        public string username { get; set; }
        /// <summary>
        /// 纳税人识别号
        /// </summary>
        public string nsrsbh { get; set; }
        /// <summary>
        /// 标识（ 0/1 ）0 需要用户自己传需要修复的发票号码，1不需要传修复起始时间到截至时间内所有的发票
        /// </summary>
        public string bs { get; set; }
        /// <summary>
        /// 发票类型代码
        /// </summary>
        public string fplxdm { get; set; }
        /// <summary>
        /// 起始时间 yyyyMMdd
        /// </summary>
        public string qqsj { get; set; }
        /// <summary>
        /// 截至时间 yyyyMMdd
        /// </summary>
        public string zzsj { get; set; }
        /// <summary>
        /// 查询进项票标志：1 空或者不传 为销项票标志
        /// </summary>
        public string gjbq { get; set; }
        /// <summary>
        /// 回调
        /// </summary>
        public string callBackUrl { get; set; }
    }
    /// <summary>
    /// 修复发票请求响应
    /// </summary>
    public class RepairInvoiceResponse
    {
        public int code { get; set; }
        public string message { get; set; }
        public string data { get; set; }
    }
    public class KaiPiaoXM
    {


    }

    public class DownloadInvoiceResponse
    {
        public int code { get; set; }
        public string message { get; set; }
        public DownloadInvoiceResponseData data { get; set; }
    }

    public class DownloadInvoiceResponseData
    {
        public string pdfUrl { get; set; }
        public string ofdUrl { get; set; }
        public string xmlUrl { get; set; }
    }

    public class QuanDianLPRequest
    {
        /// <summary>
        /// 发票请求流水号
        /// </summary>
        public string fpqqlsh { get; set; }

        /// <summary>
        /// 发票类型代码
        /// </summary>
        public string fplxdm { get; set; }

        /// <summary>
        /// 开票类型 0正数发票 1负数发票
        /// </summary>
        public string kplx { get; set; }

        /// <summary>
        /// 销方识别号
        /// </summary>
        public string xhdwsbh { get; set; }

        /// <summary>
        /// 销方名称
        /// </summary>
        public string xhdwmc { get; set; }

        /// <summary>
        /// 销方地址电话
        /// </summary>
        public string xhdwdzdh { get; set; }

        /// <summary>
        /// 销方银行账号
        /// </summary>
        public string xhdwyhzh { get; set; }

        /// <summary>
        /// 购方名称
        /// </summary>
        public string ghdwmc { get; set; }

        /// <summary>
        /// 费用项目
        /// </summary>
        public List<FeiYongXM> fyxm { get; set; }
        /// <summary>
        /// 合计金额
        /// </summary>
        public string hjje { get; set; }
        /// <summary>
        /// 合计税额
        /// </summary>
        public string hjse { get; set; }
        /// <summary>
        /// 加税合计
        /// </summary>
        public string jshj { get; set; }
    }

    public class FeiYongXM
    {
        /// <summary>
        /// 发票行性质
        /// </summary>
        public string fphxz { get; set; }
        /// <summary>
        /// 商品名称
        /// </summary>
        public string spmc { get; set; }
        public string spsm { get; set; }
        public string ggxh { get; set; }
        public string dw { get; set; }
        public string spsl { get; set; }
        public string dj { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public string je { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public string sl { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public string se { get; set; }
        /// <summary>
        /// 含税标志
        /// </summary>
        public string hsbz { get; set; }
        /// <summary>
        /// 商品标志
        /// </summary>
        public string spbm { get; set; }
    }