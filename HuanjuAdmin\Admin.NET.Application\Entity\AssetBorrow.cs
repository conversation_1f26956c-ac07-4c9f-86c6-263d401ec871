﻿using System;
using SqlSugar;
using System.ComponentModel;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 固资领用表
    /// </summary>
    [SugarTable("AssetBorrow","固资领用表")]
    [Tenant("1300000000001")]
    public class AssetBorrow  : EntityTenant
    {
        /// <summary>
        /// 领用人ID
        /// </summary>
        [SugarColumn(ColumnDescription = "领用人")]
        public long UserId { get; set; }
        /// <summary>
        /// 领用人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(UserId))]
        public SysUser SysUser { get; set; }
        /// <summary>
        /// 固资Id
        /// </summary>
        [SugarColumn(ColumnDescription = "固资Id")]
        public long AssetId { get; set; }
        /// <summary>
        /// 固资
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(AssetId))]
        public AssetInventory AssetInventory { get; set; }
        /// <summary>
        /// 领用数量
        /// </summary>
        [SugarColumn(ColumnDescription = "领用数量")]
        public int BorrowCount { get; set; }
        /// <summary>
        /// 归还数量
        /// </summary>
        [SugarColumn(ColumnDescription = "归还数量")]
        public int ReturnCount { get; set; }
        /// <summary>
        /// 需要归还
        /// </summary>
        [SugarColumn(ColumnDescription = "需要归还")]
        public bool IsNdReturn { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注")]
        public string? Remark { get; set; }
    }
}