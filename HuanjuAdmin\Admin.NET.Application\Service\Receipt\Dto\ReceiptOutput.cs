﻿using Admin.NET.Application.Enum;
using System;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.ShopCouponGetResponse.Types.Result.Types.Coupon.Types.CouponDetail.Types.Discount.Types.DiscountCondidtion.Types;

namespace Admin.NET.Application;

/// <summary>
/// 收款单输出参数
/// </summary>
public class ReceiptOutput
{
    public long Id { get; set; }
    /// <summary>
    /// 收款单号
    /// </summary>
    public string? ReceiptNo { get; set; }

    /// <summary>
    /// 单位名称
    /// </summary>
    public string? UnitName { get; set; }

    /// <summary>
    /// 合同编号
    /// </summary>
    public string? ContractNum { get; set; }

    /// <summary>
    /// 上级单号
    /// </summary>
    public string? SuperiorOrder { get; set; }

    /// <summary>
    /// 单据金额
    /// </summary>
    public decimal? DocumentAmount { get; set; }

    /// <summary>
    /// 已收金额
    /// </summary>
    public decimal? AmountReceived { get; set; }

    /// <summary>
    /// 已开票金额
    /// </summary>
    public decimal? InvoicedAmount { get; set; }

    /// <summary>
    /// 收入类型
    /// </summary>
    public string? IncomeType { get; set; }

    /// <summary>
    /// 收入名目
    /// </summary>
    public string? IncomeCategory { get; set; }

    /// <summary>
    /// 收款状态
    /// </summary>
    public int? PaymentStatus { get; set; }

    /// <summary>
    /// 交易账户
    /// </summary>
    public long? Trading { get; set; }

    /// <summary>
    /// 交易账户
    /// </summary>
    public string TradingName { get; set; }

    /// <summary>
    /// 发票状态
    /// </summary>
    public int? InvoiceStatus { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    public string? CreateUserName { get; set; }
    /// <summary>
    /// 摘要
    /// </summary>
    public string? Abstract { get; set; }
    /// <summary>
    /// 发票号码
    /// </summary>
    public string? InvoiceNo { get; set; }
}


