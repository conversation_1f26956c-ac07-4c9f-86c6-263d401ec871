﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
using Admin.NET.Application.Enum;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("salesperformanceplan","")]
    [Tenant("1300000000001")]
    public class SalesperFormanceplan  : EntityBase
    {
        /// <summary>
        /// 时间
        /// </summary>
        [SugarColumn(ColumnDescription = "时间")]
        public DateTime? PlanTime { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "类型")]
        public SalesTypeEnmu Type { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "状态")]
        public SalesStatusEnmu Status { get; set; }
        /// <summary>
        /// 进度
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "进度")]
        public int Plan { get; set; }

        /// <summary>
        /// 销售单号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "销售单号")]
        public string SalesOrder { get; set; }
        /// <summary>
        /// 计划关联id
        /// </summary>
        [SugarColumn(ColumnDescription = "时间")]
        public long? PlanId { get; set; }
    }
}