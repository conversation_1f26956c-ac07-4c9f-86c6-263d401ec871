﻿<template>
	<div class="salesperFormanceplan-container">
		<!-- <el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }"> -->
		<!-- <el-form :model="queryParams" ref="queryForm" :inline="true"> -->
		<!--         <el-form-item label="时间">
          <el-date-picker placeholder="请选择时间" value-format="YYYY/MM/DD" type="daterange" v-model="queryParams.planTimeRange" />
          
        </el-form-item>

        <el-form-item>
          <el-button-group>
            <el-button type="primary"  icon="ele-Search" @click="handleQuery" v-auth="'salesperFormanceplan:page'"> 查询 </el-button>
            <el-button icon="ele-Refresh" @click="() => queryParams = {}"> 重置 </el-button>
            
          </el-button-group>
          
        </el-form-item> -->
		<!-- <el-form-item> -->
		<el-button type="primary" icon="ele-Plus" @click="openAddSalesperFormanceplan" v-auth="'salesperFormanceplan:add'" :disabled="props.contractStatus === 0" > 新增 </el-button>

		<!-- </el-form-item> -->

		<!-- </el-form> -->
		<!-- </el-card> -->
		<el-card class="full-table" shadow="hover" style="margin-top: 8px">
			<el-table :data="tableData" style="width: 100%" v-loading="loading" tooltip-effect="light" row-key="id" border="" :height="bomHeight">
				<el-table-column type="index" label="序号" width="55" align="center" fixed="" />
				<el-table-column prop="planTime" label="时间" fixed="" show-overflow-tooltip="">
					<template #default="scope">
						{{ scope.row.planTime.split(' ')[0] }}
					</template>
				</el-table-column>

				<el-table-column prop="type" label="类型" fixed="" show-overflow-tooltip="">
					<template #default="scope">
						<el-tag type="danger" v-if="scope.row.type === 0">发货</el-tag>
						<el-tag type="danger" v-if="scope.row.type === 1">收款</el-tag>
						<el-tag type="success" v-if="scope.row.type === 2">开票</el-tag>
					</template>
				</el-table-column>

				<el-table-column prop="status" label="状态" fixed="" show-overflow-tooltip="">
					<template #default="scope">
						<!-- <el-tag type="danger" v-if="scope.row.status === 0">确认</el-tag>
						<el-tag type="danger" v-if="scope.row.status === 1">未确认</el-tag> -->
						<el-switch v-model="scope.row.status" @change="openClosePlan(scope.row)"></el-switch>
					</template>
				</el-table-column>

				<el-table-column prop="plan" label="进度" fixed="" show-overflow-tooltip="">
					<template #default="scope">
						<!-- <el-tag type="danger" v-if="scope.row.plan === 0">完成</el-tag> -->
						<!-- <el-tag type="danger" v-if="scope.row.plan === 1">未完成</el-tag> -->
						<el-tag type="danger" v-if="scope.row.status === false">无</el-tag>
						<el-tag type="danger" v-if="scope.row.status === true && scope.row.type === 0 && scope.row.plan === 0">待提交</el-tag>
						<el-tag type="danger" v-if="scope.row.status === true && scope.row.type === 0 && scope.row.plan === 1">待出库</el-tag>
						<el-tag type="danger" v-if="scope.row.status === true && scope.row.type === 0 && scope.row.plan === 2">部分出库</el-tag>
						<el-tag type="danger" v-if="scope.row.status === true && scope.row.type === 0 && scope.row.plan === 3">已出库</el-tag>
						<el-tag type="danger" v-if="scope.row.status === true && scope.row.type === 0 && scope.row.plan === 4">已中止</el-tag>
						<el-tag type="danger" v-if="scope.row.status === true && scope.row.type === 1 && scope.row.plan === 0">待提交</el-tag>
						<el-tag type="danger" v-if="scope.row.status === true && scope.row.type === 1 && scope.row.plan === 1">待收款</el-tag>
						<el-tag type="danger" v-if="scope.row.status === true && scope.row.type === 1 && scope.row.plan === 2">部分收款</el-tag>
						<el-tag type="danger" v-if="scope.row.status === true && scope.row.type === 1 && scope.row.plan === 3">已收款</el-tag>
						<el-tag type="danger" v-if="scope.row.status === true && scope.row.type === 1 && scope.row.plan === 4">已中止</el-tag>
						<el-tag type="danger" v-if="scope.row.status === true && scope.row.type === 2 && scope.row.plan === 0">无票</el-tag>
						<el-tag type="danger" v-if="scope.row.status === true && scope.row.type === 2 && scope.row.plan === 1">待开</el-tag>
						<el-tag type="danger" v-if="scope.row.status === true && scope.row.type === 2 && scope.row.plan === 2">已开</el-tag>
						<el-tag type="danger" v-if="scope.row.status === true && scope.row.type === 2 && scope.row.plan === 3">申开</el-tag>
					</template>
				</el-table-column>

				<!--          <el-table-column prop="type" label="类型" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="status" label="状态" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="plan" label="进度" fixed="" show-overflow-tooltip="" /> -->
				<el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip="" v-if="auth('salesperFormanceplan:edit') || auth('salesperFormanceplan:delete')">
					<template #default="scope">
						<el-button
							icon="ele-Edit"
							size="small"
							text=""
							type="primary"
							@click="openEditSalesperFormanceplan(scope.row)"
							v-auth="'salesperFormanceplan:edit'"
							:disabled="scope.row.status === false ? false : true"
						>
							编辑
						</el-button>
						<el-button
							icon="ele-Delete"
							size="small"
							text=""
							type="primary"
							@click="delSalesperFormanceplan(scope.row)"
							v-auth="'salesperFormanceplan:delete'"
							:disabled="scope.row.status === false ? false : true"
						>
							删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="tableParams.page"
				v-model:page-size="tableParams.pageSize"
				:total="tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				small=""
				background=""
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
			<editDialog ref="editDialogRef" :title="editSalesperFormanceplanTitle" :saleId="props.outOrderId" @reloadTable="handleQuery" />
		</el-card>
	</div>
</template>

<script lang="ts" setup="" name="salesperFormanceplan">
import { ElMessageBox, ElMessage } from 'element-plus';
import { auth } from '/@/utils/authFunction';
//import { formatDate } from '/@/utils/formatTime';
import { ref, watch } from 'vue';
import editDialog from '/@/views/main/salesperFormanceplan/component/editDialog.vue';
import { pageSalesperFormanceplan, deleteSalesperFormanceplan, planSwitch } from '/@/api/main/salesperFormanceplan';
import { status } from 'nprogress';

const detail = ref<any>();
const editDialogRef = ref();
let code = '';
const loading = ref(false);
const tableData = ref<any>([]);
const props = defineProps<{
	orderDetail: String;
	outOrderId: string;
	bomHeight: String;
	outOrder: string;
	contractStatus: number; 
}>();
watch(
	() => props.orderDetail,
	(n) => {
		debugger;
		if (n) {
			handleQuery('');
		} else {
			tableData.value = [];
		}
	}
);
const queryParams = ref<any>({});
const tableParams = ref({
	page: 1,
	pageSize: 10,
	total: 0,
});
const editSalesperFormanceplanTitle = ref('');

// 打开履约计划
const openClosePlan = async (row: any) => {
	console.log(row.status);
	if (row.status == true) {
		row.status = 1;
	} else {
		row.status = 0;
	}
	const params = {
		...row,
		salesOrder: props.outOrder,
	};
	try {
		const res = await planSwitch(params);
		handleQuery(props.outOrderId);
	} catch (error) {
		handleQuery(props.outOrderId);
	}
};
// 查询操作
const handleQuery = async (Id: any) => {
	let params = {
		page: tableParams.value.page,
		pageSize: tableParams.value.pageSize,
		SalesOrder: Id,
	};
	loading.value = true;
	code = Id;
	// queryParams.value.salesOrder=Id;
	var res = await pageSalesperFormanceplan(params);
	tableData.value = res.data.result?.items ?? [];
	tableData.value.forEach((item) => {
		if (item.status == 0) {
			item.status = false;
		} else {
			item.status = true;
		}
	});
	detail.value = res.data.result?.items ?? [];
	tableParams.value.total = res.data.result?.total;
	loading.value = false;
};

// 修改 openAddSalesperFormanceplan 函数
const openAddSalesperFormanceplan = () => {
  editSalesperFormanceplanTitle.value = '添加履约计划';
  if (props.contractStatus === 0) {
    ElMessage.warning('洽谈状态下不能添加履约计划');
    return;
  }
  if (code == null || code == '') {
    ElMessage.warning('请选择合同');
    return;
  }
  editDialogRef.value.openDialog('', code);
};

// 打开编辑页面
const openEditSalesperFormanceplan = (row: any) => {
	editSalesperFormanceplanTitle.value = '编辑履约计划';
	editDialogRef.value.openDialog(row, '');
};

// 删除
const delSalesperFormanceplan = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await deleteSalesperFormanceplan(row);
			handleQuery(props.outOrderId);
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	tableParams.value.pageSize = val;
	handleQuery('');
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	tableParams.value.page = val;
	handleQuery('');
};

//handleQuery("");
defineExpose({ handleQuery, detail });
</script>


