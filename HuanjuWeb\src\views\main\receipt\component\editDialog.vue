﻿<template>
	<div class="receipt-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="700" draggable="" :close-on-click-modal="false">
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" :rules="rules">
				<!-- <el-row>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="摘要" prop="abstract">
							<el-input v-model="ruleForm.abstract" placeholder="请输入摘要信息" clearable />

						</el-form-item>

					</el-col>
				</el-row> -->
				<el-row :gutter="35">
					<!-- 					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="收款单号" prop="receiptNo">
							<el-input v-model="ruleForm.receiptNo" placeholder="请输入收款单号" clearable />
							
						</el-form-item>
						
					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="客户" prop="unitName">
							<div style="display: flex;">
								<el-select clearable filterable v-model="ruleForm.unitName" placeholder="">
									<el-option v-for="(item, index) in getPagePubcustomList" :key="index"
										:value="item.name" :label="item.name" />
								</el-select>
								<!-- <el-button class="supplier" icon="ele-Plus" text="" type="primary"
									@click="openAddPubSupplier">
							</el-button> -->
								<el-button class="supplier" type="primary" icon="el-icon-plus"
									@click="openAddPubCustom">
								</el-button>
							</div>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="摘要" prop="abstract">
							<el-input v-model="ruleForm.abstract" placeholder="请输入摘要信息" clearable />

						</el-form-item>

					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="交易账户">
							<el-select clearable v-model="ruleForm.trading" placeholder="请选择交易账户">
								<el-option v-for="(item, index) in tradingAccountList" :key="index" :value="item.value"
									:label="item.label"></el-option>
							</el-select>
						</el-form-item>

					</el-col>
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="合同编号" prop="contractNum">
							<el-input v-model="ruleForm.contractNum" placeholder="请输入合同编号" clearable />
							
						</el-form-item>
					</el-col> -->
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="销售单号" prop="salesOrder">
							<el-input v-model="ruleForm.salesOrder" placeholder="请输入销售单号" clearable />
							
						</el-form-item>
						
					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="单据金额" prop="documentAmount">
							<el-input v-model="ruleForm.documentAmount" placeholder="请输入单据金额" clearable />

						</el-form-item>

					</el-col>
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="已收金额" prop="amountReceived">
							<el-input v-model="ruleForm.amountReceived" placeholder="请输入已收金额" clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="已开票金额" prop="invoicedAmount">
							<el-input v-model="ruleForm.invoicedAmount" placeholder="请输入已开票金额" clearable
								:disabled="!ruleForm.invoiceStatus" />

						</el-form-item>

					</el-col> -->
					<!-- 					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="收入类型" prop="incomeType">
							<el-input-number v-model="ruleForm.incomeType" placeholder="请输入收入类型" clearable />
							
						</el-form-item>
						
					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="科目代码" prop="incomeCategory">
							<div style="display: flex;">
								<el-select clearable filterable v-model="ruleForm.incomeCategory" placeholder="">
									<el-option v-for="(item, index) in getSubjectCodeList" :key="index" :value="item.subjectCode"
									:label="item.subjectCode"></el-option>
								</el-select>
								
							</div>
						</el-form-item>
					</el-col>

					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="科目名称">
							<el-select clearable v-model="ruleForm.incomeType" placeholder="">
								<el-option v-for="(item, index) in getSubjectCodeList" :key="index" :value="item.subjectName"
									:label="item.subjectName"></el-option>

							</el-select>

						</el-form-item>

					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="科目名称" prop="incomeType">
							<el-input v-model="ruleForm.incomeType" placeholder="请输入科目名称" clearable readonly/>

						</el-form-item>

					</el-col>
					<!-- 					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="收入名目" prop="incomeCategory">
							<el-input-number v-model="ruleForm.incomeCategory" placeholder="请输入收入名目" clearable />
							
						</el-form-item>
						
					</el-col> -->
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="科目代码">
							<el-select clearable v-model="ruleForm.incomeCategory" placeholder="请选择科目代码">
								<el-option v-for="(item, index) in getSubjectCodeList" :key="index" :value="item.subjectCode"
									:label="item.subjectCode"></el-option>

							</el-select>

						</el-form-item>

					</el-col> -->

					<!-- 				<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="交易方式" prop="trading">
							<el-input-number v-model="ruleForm.trading" placeholder="请输入交易方式" clearable />
							
						</el-form-item>
						
					</el-col> -->
					
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="发票状态" prop="invoiceStatus">
							<el-switch v-model="ruleForm.invoiceStatus"></el-switch>
						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="备注" prop="notes">
							<el-input v-model="ruleForm.notes" placeholder="请输入备注" clearable />

						</el-form-item>

					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
		<PubcustomVue ref="editPubCustomRef" :title="editPubCustomTitle" @reloadTable="getPagePubcustomListJk">
		</PubcustomVue>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted,watch } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { addReceipt, updateReceipt } from "/@/api/main/receipt";
import { Pubcustom, SubjectList } from '/@/api/main/pubcustom';
import { tradingAccountDropdown } from '/@/api/main/tradingAccounts';
import PubcustomVue from '/@/views/main/pubcustom/component/editDialog.vue'
//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: "",
	},
});
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const loading = ref(false);
const editPubCustomTitle = ref('');
const editPubCustomRef = ref();
const ruleForm = ref<any>({});
//自行添加其他规则
const rules = ref<FormRules>({
});
// const inhouseTypeList = [//入库类型
// 	{ label: '主营业务收入', value: 0 },
// 	{ label: '其他业务收入', value: 1 },
// ]
// const inhouseCategory = [//入库类型
// 	{ label: '销售收入	', value: 0 },
// 	{ label: '非销售收入', value: 1 },
// ]
// 科目编码
const getSubjectCodeList = ref<any>([]);
const getSubjectCodeListJk = async () => {
	let list = await SubjectList();
	getSubjectCodeList.value = list.data.result ?? [];
	console.log('科目编码----', list)
};
getSubjectCodeListJk();

watch(() => ruleForm.value.incomeCategory, (newVal) => {
  if (newVal) {
    const subject = getSubjectCodeList.value.find((item: { subjectCode: any; }) => item.subjectCode === newVal);
    if (subject) {
      ruleForm.value.incomeType = subject.subjectName;
    }
  }
});
// const inhousetrading = [//入库类型
// 	{ label: '现金	', value: 0 },
// 	{ label: '对公', value: 1 },
// 	{ label: '支付宝', value: 2 },
// 	{ label: '微信', value: 3 },
// ]

// 交易账户
const tradingAccountList = ref<any>([]);
const getTradingAccountList = async () => {
	let list = await tradingAccountDropdown();
	tradingAccountList.value = list.data.result ?? [];
};
getTradingAccountList();

// 打开弹窗
const openDialog = (row: any) => {
	ruleForm.value = JSON.parse(JSON.stringify(row));
	if (ruleForm.value.invoiceStatus === 0) {
		ruleForm.value.invoiceStatus = false
	} else {
		ruleForm.value.invoiceStatus = true
	}
	isShowDialog.value = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	isShowDialog.value = false;
	setTimeout(() => {
		loading.value = false;
	}, 500)
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			loading.value = true;
			let values = ruleForm.value;
			//   if(ruleForm.value.invoiceStatus === false){
			//     ruleForm.value.invoiceStatus = 0
			//   } else{
			//     ruleForm.value.invoiceStatus = 1
			//   }
			if (ruleForm.value.id != undefined && ruleForm.value.id > 0) {
				await updateReceipt(values);
			} else {
				await addReceipt(values);
			}
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};
// 供应商
const getPagePubcustomList = ref<any>([]);
const getPagePubcustomListJk = async () => {
	let list = await Pubcustom();
	getPagePubcustomList.value = list.data.result ?? [];
	console.log('客户----', list)
};
getPagePubcustomListJk();
// 打开新增客户页面
const openAddPubCustom = () => {
	editPubCustomTitle.value = '添加客户';
	editPubCustomRef.value.openDialog({});
};


// 页面加载时
onMounted(async () => {
});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<style scoped>
.supplier {
	width: 30px;
	height: 30px;
	margin-left: 10px;
	padding-left: 20px;
}

.el-select {
	width: 210px;
}
</style>
