﻿using System;

namespace Admin.NET.Application;

    /// <summary>
    /// 收支明细输出参数
    /// </summary>
    public class warerevenueDto
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 收支时间
        /// </summary>
        public DateTime? revenueTime { get; set; }
        
        /// <summary>
        /// 来往单位
        /// </summary>
        public long? contactunits { get; set; }
        
        /// <summary>
        /// 关联单号
        /// </summary>
        public long? ordernumber { get; set; }
        
        /// <summary>
        /// 收支科目
        /// </summary>
        public string? subject { get; set; }
        
        /// <summary>
        /// 二级科目
        /// </summary>
        public string? levelsubject { get; set; }
        
        /// <summary>
        /// 收入金额
        /// </summary>
        public decimal? Incomeamount { get; set; }
        
        /// <summary>
        /// 支出金额
        /// </summary>
        public decimal? expenditureamount { get; set; }
        
        /// <summary>
        /// 经办人
        /// </summary>
        public long? handledby { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string? notes { get; set; }
        
    }
