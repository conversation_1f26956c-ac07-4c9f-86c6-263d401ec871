﻿using SqlSugar;

namespace Admin.NET.Application;

/// <summary>
/// 商品信息输出参数
/// </summary>
public class WarehousegoodsOutput
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 商品名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Specs { get; set; }

    /// <summary>
    /// 库存数量
    /// </summary>
    public int InventoryCount { get; set; }

    /// <summary>
    /// 告警数量（库存不足）
    /// </summary>
    public int AlarmMin { get; set; }

    /// <summary>
    /// 库存数量（库存堆积）
    /// </summary>
    public int AlarmMax { get; set; }

    /// <summary>
    /// 初始库存数量
    /// </summary>
    public int BaseInventoryCount { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public long? Unit { get; set; }

    /// <summary>
    /// 辅助单位
    /// </summary>
    public long? Auxiliaryunit { get; set; }

    /// <summary>
    /// 辅助单位
    /// </summary>
    public string? auxiliaryunitName { get; set; }



    /// <summary>
    /// 转换数量
    /// </summary>
    public int? convertCount { get; set; }


    /// <summary>
    /// 商品条码    
    /// </summary>
    public string? barcode { get; set; }

    /// <summary>
    /// 是否唯一码    
    /// </summary>
    public bool? isuniquecode { get; set; }

    /// <summary>
    /// 是否批次    
    /// </summary>
    public bool? isbatch { get; set; }

    /// <summary>
    /// 是否负库存    
    /// </summary>
    public bool? vacancy { get; set; }

    /// <summary>
    /// 单位    
    /// </summary>
    public string? unitName { get; set; }


    /// <summary>
    /// 是否删除    
    /// </summary>
    public bool? IsDelete { get; set; }

    /// <summary>
    /// 商户ID    
    /// </summary>
    public long? TenantId { get; set; }

    /// <summary>
    /// 保质期（天）
    /// </summary>
    public int? ExpirationDate { get; set; }

    /// <summary>
    /// 过期预警（天）
    /// </summary>
    public int? ExpiryReminder { get; set; }
}


