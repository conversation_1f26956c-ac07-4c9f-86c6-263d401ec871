﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 审批流程基础输入参数
/// </summary>
public class WorkflowOrderBaseInput
{
    /// <summary>
    /// 申请人ID
    /// </summary>
    public virtual long? UerId { get; set; }

    /// <summary>
    /// 审批单号
    /// </summary>
    public virtual string ApprovalNumber { get; set; }

    /// <summary>
    /// 流程ID
    /// </summary>
    public virtual long WorkflowId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public virtual ApproveStatusEnum Status { get; set; }

    /// <summary>
    /// 部门ID
    /// </summary>
    public virtual long OrgId { get; set; }

}

/// <summary>
/// 审批流程分页查询输入参数
/// </summary>
public class WorkflowOrderInput : BasePageInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 申请人ID
    /// </summary>
    public long? UerId { get; set; }

    /// <summary>
    /// 审批单号
    /// </summary>
    public string ApprovalNumber { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ApproveStatusEnum Status { get; set; }

    /// <summary>
    /// 部门ID
    /// </summary>
    public long OrgId { get; set; }

    public string Avatar { get; set; }

    /// <summary>
    /// 部门
    /// </summary>
    public string SysOrgName { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string SysPosName { get; set; }

    /// <summary>
    /// 申请人
    /// </summary>
    public string SysUserRealName { get; set; }
}

/// <summary>
/// 审批流程增加输入参数
/// </summary>
public class AddWorkflowOrderInput : WorkflowOrderBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    public DateTime CreateTime { get; set; }
}

/// <summary>
/// 审批流程删除输入参数
/// </summary>
public class DeleteWorkflowOrderInput : BaseIdInput
{
}

/// <summary>
/// 审批流程更新输入参数
/// </summary>
public class UpdateWorkflowOrderInput : WorkflowOrderBaseInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public long Id { get; set; }

}

/// <summary>
/// 审批流程主键查询输入参数
/// </summary>
public class QueryByIdWorkflowOrderInput : DeleteWorkflowOrderInput
{

}
