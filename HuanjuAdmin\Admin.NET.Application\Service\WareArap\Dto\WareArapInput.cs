﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 应收应付基础输入参数
    /// </summary>
    public class WareArapBaseInput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public virtual long Id { get; set; }
        
        /// <summary>
        /// 待收金额
        /// </summary>
        public virtual decimal? PendingAmount { get; set; }
        
        /// <summary>
        /// 代付金额
        /// </summary>
        public virtual decimal? AmountPaid { get; set; }
        
        /// <summary>
        /// 来往单位
        /// </summary>
        public virtual long Contactunits { get; set; }
        
        /// <summary>
        /// 类别
        /// </summary>
        public virtual int category { get; set; }
        
    }

    /// <summary>
    /// 应收应付分页查询输入参数
    /// </summary>
    public class WareArapInput : BasePageInput
    {
        /// <summary>
        /// 来往单位
        /// </summary>
        public string Contactunits { get; set; }
        
        /// <summary>
        /// 类别
        /// </summary>
        public int? Category { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
        
    }

    /// <summary>
    /// 应收应付增加输入参数
    /// </summary>
    public class AddWareArapInput : WareArapBaseInput
    {
    }

    /// <summary>
    /// 应收应付删除输入参数
    /// </summary>
    public class DeleteWareArapInput : BaseIdInput
    {
    }

    /// <summary>
    /// 应收应付更新输入参数
    /// </summary>
    public class UpdateWareArapInput : WareArapBaseInput
    {
    }

    /// <summary>
    /// 应收应付主键查询输入参数
    /// </summary>
    public class QueryByIdWareArapInput : DeleteWareArapInput
    {

    }
