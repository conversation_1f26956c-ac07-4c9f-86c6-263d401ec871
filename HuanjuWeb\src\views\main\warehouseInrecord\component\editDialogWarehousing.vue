﻿<template>
	<div class="warehouseconvertrecord-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="1100" draggable="" :close-on-click-modal="false">
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" :rules="rules">
				<el-table :data="tableDataMX" style="width: 100%;padding: 20px 0;" tooltip-effect="light">
					<!-- 	<el-row :gutter="35"> -->
					<el-table-column prop="warehousegoodsName" label="商品名称" show-overflow-tooltip="" width="109" />
					<el-table-column prop="brandName" label="品牌" show-overflow-tooltip="" width="80" />
					<el-table-column prop="specsName" label="规格" show-overflow-tooltip="" width="80" />
					<el-table-column prop="unitName" label="单位" show-overflow-tooltip="" width="109">
						<template #default="scope">
							<el-select clearable filterable v-model="scope.row.unitName" placeholder=" " disabled>
								<el-option v-for=" (item, index) in counterStore.unitList" :key="index"
									:value="item.value" :label="item.label" />
							</el-select>
						</template>
					</el-table-column>
					<el-table-column prop="isproduct" label="是否良品" show-overflow-tooltip="" width="109">
						<template #default="scope">
							<el-switch v-model="scope.row.isproduct" />
						</template>
					</el-table-column>
					<el-table-column prop="documentNum" label="单据数量" show-overflow-tooltip="" width="109">
						<!-- 				<template #default="scope">
								<el-input-number v-model="scope.row.documentNum"  clearable />
							</template> -->
					</el-table-column>
					<el-table-column prop="rcvQty" label="已入库数量" show-overflow-tooltip="" width="109">
						<!-- 						<template #default="scope">
								<el-input-number v-model="scope.row.RcvQty"  clearable />
							</template> -->
					</el-table-column>
					<el-table-column prop="quantity" label="本次入库数量" show-overflow-tooltip="" width="200">
						<template #default="scope">
							<el-input-number v-model="scope.row.quantity" clearable
								:disabled="scope.row.isbatch || scope.row.isuniquecode" />
						</template>
					</el-table-column>
					<el-table-column prop="" label="操作" show-overflow-tooltip="" width="150" fixed="right">
						<template #default="scope">
							<el-button size="small" text="" type="primary" v-if="scope.row.isbatch"
								@click="openIsBatchDialog(scope.row)">批次号</el-button>
							<el-button size="small" text="" type="primary" v-if="scope.row.isuniquecode">唯一码</el-button>
						</template>
					</el-table-column>
					<!-- </el-row> -->
				</el-table>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
		<editDialogisbatch ref="editDialogisbatchRef" :title="editDialogWare" @isBatchDataListCz="isBatchDataList" />
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { ElMessage, ElTable, ElButton } from "element-plus";
import type { FormRules } from "element-plus";
import { WarehouseStoreAdd } from '/@/api/main/warehouseInrecord';
import useCounter from '/@/stores/counter';
import editDialogisbatch from '/@/views/main/warehouseInrecord/component/editDialogisbatch.vue';
const editDialogWare = ref(''); //批次商品弹框
const editDialogisbatchRef = ref(); //批次商品弹框
const counterStore = useCounter();
var props = defineProps({
	title: {
		type: String,
		default: "",
	},
});
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const loading = ref(false);
const ruleForm = ref<any>({});
//自行添加其他规则
const rules = ref<FormRules>({
});

const getMx = ref([]);
let tableDataMX = ref<any>([]);

// 打开批次商品弹框
const openIsBatchDialog = (index: any) => {
	editDialogWare.value = '编辑批次商品明细';
	if (index.batchs == undefined) {
		const isBatchData = ref<any>([]);
		isBatchData.value = []
		debugger;
		isBatchData.value.push({
			id: index.id,
			warehousegoodsName: index.warehousegoodsName,
			quanProduceTimetity: null,
			warrantyTime: index.shelflife,
			expiryReminder: index.expiryReminder,
			warranty: index.warranty,
			expirationTime: null,
			batchnumber: null,
			quantity: null
		});

		editDialogisbatchRef.value.openDialog(isBatchData.value);
	} else
		editDialogisbatchRef.value.openDialog(index.batchs);
};

// 打开弹窗
const openDialog = (row: any) => {
	tableDataMX.value = row;
	getMx.value = row;
	isShowDialog.value = true;
};
// 接受批次商品弹窗传来的值
const isBatchDataList = (list: any) => {
	let num = 0
	list.forEach(item => {
		console.log(item)
		num += item.goodProductNum
	});

	tableDataMX.value.forEach((item) => {
		if (item.id == list[0].id) {
			item.quantity = num
			item.batchs = list
		}
	})
};
// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};

// 提交
const submit = async () => {
	try {
		const valid = await ruleFormRef.value.validate();
		if (!valid) {
			ElMessage.error('表单验证失败，请检查输入');
			return;
		}

		loading.value = true;
		
		// 检查 tableDataMX 是否为空
		if (!tableDataMX.value || tableDataMX.value.length === 0) {
			ElMessage.warning('没有可入库的数据');
			return;
		}

		const totalInboundCount = tableDataMX.value.reduce((sum: any, item: any) => {
			return sum + (item.quantity || 0);
		}, 0);

		if (totalInboundCount <= 0) {
			ElMessage.warning('入库总数量必须大于0');
			return;
		}

		let arr: any[] = [];
		
		for (const item of tableDataMX.value) {
			// 检查必要字段是否存在
			if (!item.id || !item.goodsId) {
				ElMessage.warning('商品数据不完整');
				return;
			}

			if (item.quantity > (item.documentNum - item.rcvQty)) {
				ElMessage.warning('填写的入库数量不能超出可入库数量');
				return;
			}

			if (item.quantity === null || item.quantity === 0) {
				continue;
			}

			if (item.quantity < 0) {
				ElMessage.warning('入库数量不得小于0');
				return;
			}

			const obj = {
				Id: item.id,
				inrecordId: item.inrecordId,
				warehouse: item.goodsId,
				goodsId: item.goodsId,
				quantity: item.quantity,
				puchQty: item.puchQty,
				tradeName: item.warehousegoodsName,
				brand: item.brand,
				specifications: item.specifications,
				unit: item.unit,
				isWarranty: true,
				supplier: item.supplierId,
				productCode: item.id,
				barCode: item.barcode,
				isUniqueCode: false,
				warranty: item.warranty,
				produceTime: item.productDate,
				notes: '',
				goodProduct: item.rating == 1 ? item.rating : 0,
				reject: item.rcvQty == 0 ? item.rcvQty : 0,
				safetyStockTallNum: 0,
				safetyStockLowNum: 0,
				Unitprice: item.unitprice,
				documentNum: item.documentNum,
				isproduct: item.isproduct,
				batchs: item.batchs || []
			};
			arr.push(obj);
		}

		if (arr.length > 0) {
			const res = await WarehouseStoreAdd(arr);
			if (!res?.data || res.data.code !== 200) {
				throw new Error(res?.data?.msg || '入库失败');
			}
			
			ElMessage.success('入库成功！');
			emit("reloadTable");
			isShowDialog.value = false;
		}
	} catch (error: any) {
		console.error('入库操作失败:', error);
		ElMessage.error(error.message || '入库失败，请重试');
	} finally {
		loading.value = false;
	}
};


// 页面加载时
onMounted(async () => {
});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
