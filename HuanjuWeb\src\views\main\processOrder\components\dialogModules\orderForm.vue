<template>
    <el-dialog v-model="dialogVisible" title="加工单配置" width="50%" :before-close="handleClose">
        <el-form :model="form" label-width="100px" :inline="true" :rules="rules" ref="formRef">
            <el-card style="margin-bottom: 8px;">
                <el-form-item label="加工单号" prop="orderNo">
                    <el-input v-model="form.orderNo" placeholder="请输入加工单号" style="width: 160px" disabled readonly />
                </el-form-item>
                <el-form-item label="方案名称" prop="schemeId">
                    <el-select v-model="form.schemeId" placeholder="请选择方案名称" style="width: 160px" filterable
                        @change="handleSchemeNameChange">
                        <el-option v-for="item in schemeList" :key="item.id" :label="item.schemeName" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="原料仓库" prop="materialWarehouseId">
                    <el-select v-model="form.materialWarehouseId" placeholder="请选择原料仓库" style="width: 160px" filterable>
                        <el-option v-for="item in warehouseList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="成品仓库" prop="produceWarehouseId">
                    <el-select v-model="form.produceWarehouseId" placeholder="请选择成品仓库" style="width: 160px" filterable>
                        <el-option v-for="item in warehouseList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="创建时间" prop="createTime">
                    <el-input v-model="form.createTime" placeholder="请输入创建时间" style="width: 160px" disabled readonly />
                </el-form-item>
            </el-card>
            <el-collapse v-model="activeItems">
                <material-table ref="materialTableRef" v-model:tableData="form.materialList"
                    :active-items="activeItems" :material-warehouse-id="form.materialWarehouseId" />
                <produce-table ref="produceTableRef" v-model:tableData="form.produceList" :active-items="activeItems" />
            </el-collapse>
        </el-form>
        <template #footer>
            <div style="display: flex; justify-content: flex-end; gap: 10px;">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="submitting">提交</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, provide, reactive, nextTick } from 'vue';
import materialTable from './materialTable.vue';
import produceTable from './produceTable.vue';
import { OrderTableData } from '../../types/type';
import { getOneProcessOrder, addProcessOrder, updateProcessOrder, getWarehouseList, getProcessOrderSchemeList, getProcessOrderSchemeDetail } from '/@/api/main/processOrder';
import { ElMessage } from 'element-plus';

const formRef = ref(null);
const materialTableRef = ref(null);
const produceTableRef = ref(null);
const submitting = ref(false);
const warehouseList = ref([{ id: '', name: '' }]);
const schemeList = ref([{ id: '', schemeName: '' }]);

const emit = defineEmits(['handleSubmit']);

const form = ref<OrderTableData>({
    id: 0,
    orderNo: '',
    schemeId: '',
    createTime: '',
    materialWarehouseId: '',
    produceWarehouseId: '',
    materialList: [],
    produceList: [],
});

// 表单验证规则
const rules = reactive({
    schemeId: [
        { required: true, message: '请选择方案名称', trigger: 'blur' },
    ],
    materialWarehouseId: [
        { required: true, message: '请选择原料仓库', trigger: 'blur' },
    ],
    produceWarehouseId: [
        { required: true, message: '请选择成品仓库', trigger: 'blur' },
    ],
});

const dialogVisible = ref(false);
const activeItems = ref(['materialList', 'outputList']);

// 获取原料仓库和成品仓库的选项
const getWarehouseOptions = async () => {
    getWarehouseList({}).then(res => {
        warehouseList.value = res.data.result;
    });
};

// 获取方案名称的选项
const getSchemeNameOptions = async () => {
    getProcessOrderSchemeList({}).then(res => {
        schemeList.value = res.data.result;
    });
};

// 获取方案详情
const getSchemeDetail = async (schemeId: number) => {
    // 设置material-table和produce-table的loading状态
    materialTableRef.value.loading = true;
    produceTableRef.value.loading = true;

    getProcessOrderSchemeDetail({ id: schemeId }).then(res => {
        // 设置material-table和produce-table的数据
        form.value = res.data.result;
    }).finally(() => {
        materialTableRef.value.loading = false;
        produceTableRef.value.loading = false;
    });
};

// 方案名称切换选择后的处理函数
const handleSchemeNameChange = (value: number) => {
    // 获取方案详情
    getSchemeDetail(value);
};

const handleClose = () => {
    dialogVisible.value = false;
};

// 提交表单
const handleSubmit = () => {
    if (formRef.value) {
        console.log(form.value);
        formRef.value.validate((valid: boolean) => {
            if (valid) {
                // 验证原料列表和产出列表是否为空
                if (form.value.materialList.length === 0) {
                    ElMessage.warning('原料列表不能为空');
                    return;
                }

                if (form.value.produceList.length === 0) {
                    ElMessage.warning('产出列表不能为空');
                    return;
                }

                // 验证表格是否有未完成的编辑
                if (!materialTableRef.value.valid()) {
                    return;
                }

                if (!produceTableRef.value.valid()) {
                    return;
                }

                // 提交表单
                submitting.value = true;
                (form.value.id ? updateProcessOrder(form.value) : addProcessOrder(form.value))
                    .then(res => {
                        ElMessage.success('提交成功');
                        emit('handleSubmit');
                        dialogVisible.value = false;
                    }).finally(() => {
                        submitting.value = false;
                    });
            } else {
                ElMessage.error('请完善表单信息');
                return false;
            }
        });
    }
};


const openDialog = async (id?: number) => {
    dialogVisible.value = true;
    provide('reset', dialogVisible);

    // 重置表单状态
    if (formRef.value) {
        formRef.value.resetFields();
    }

    form.value = {
        id: 0,
        orderNo: '',
        schemeId: '',
        createTime: '',
        materialWarehouseId: '',
        produceWarehouseId: '',
        materialList: [],
        produceList: [],
    };

    // 重置表格编辑状态
    nextTick(() => {
        if (materialTableRef.value) {
            materialTableRef.value.resetEditStatus();
        }

        if (produceTableRef.value) {
            produceTableRef.value.resetEditStatus();
        }

        getWarehouseOptions();
        getSchemeNameOptions();

        if (id) {
            if (materialTableRef.value) {
                console.log(materialTableRef.value.loading);
                materialTableRef.value.loading = true;
            }
            if (produceTableRef.value) {
                produceTableRef.value.loading = true;
            }
            getOneProcessOrder({ id: id }).then(res => {
                form.value = res.data.result;
                // 加载数据后再次确保重置编辑状态
                nextTick(() => {
                    if (materialTableRef.value) {
                        materialTableRef.value.resetEditStatus();
                        materialTableRef.value.loading = false;
                    }

                    if (produceTableRef.value) {
                        produceTableRef.value.resetEditStatus();
                        produceTableRef.value.loading = false;
                    }
                });
            });
        }
    });
}

defineExpose({
    openDialog
});
</script>

<style scoped lang="scss">
:deep(.el-card__body) {
    padding: 8px;
}

:deep(.el-dialog__footer) {
    padding-top: 10px;
    border-top: 1px solid var(--el-border-color-lighter);
}
</style>
