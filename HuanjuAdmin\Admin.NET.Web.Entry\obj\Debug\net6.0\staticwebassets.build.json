{"Version": 1, "Hash": "YTQBKS0PookcZ3FLaShV7V6Av9b3UzTGf78S3kqjE6U=", "Source": "Admin.NET.Web.Entry", "BasePath": "_content/Admin.NET.Web.Entry", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Admin.NET.Web.Entry\\wwwroot", "Source": "Admin.NET.Web.Entry", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "Pattern": "**"}], "Assets": [{"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\CodeGen\\ImportTemplate\\Web\\src\\api\\main\\importTemplate.ts", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "CodeGen/ImportTemplate/Web/src/api/main/importTemplate.ts", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\CodeGen\\ImportTemplate\\Web\\src\\api\\main\\importTemplate.ts"}, {"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\CodeGen\\ImportTemplate\\Web\\src\\views\\main\\importTemplate\\component\\editDialog.vue", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "CodeGen/ImportTemplate/Web/src/views/main/importTemplate/component/editDialog.vue", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\CodeGen\\ImportTemplate\\Web\\src\\views\\main\\importTemplate\\component\\editDialog.vue"}, {"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\CodeGen\\ImportTemplate\\Web\\src\\views\\main\\importTemplate\\index.vue", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "CodeGen/ImportTemplate/Web/src/views/main/importTemplate/index.vue", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\CodeGen\\ImportTemplate\\Web\\src\\views\\main\\importTemplate\\index.vue"}, {"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\images\\logo.png", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "images/logo.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\logo.png"}, {"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\data.data.ts.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "Template/data.data.ts.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Template\\data.data.ts.vm"}, {"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\dataModal.vue.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "Template/dataModal.vue.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Template\\dataModal.vue.vm"}, {"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\Dto.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "Template/Dto.cs.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Template\\Dto.cs.vm"}, {"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\editDialog.vue.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "Template/editDialog.vue.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Template\\editDialog.vue.vm"}, {"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\Entity.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "Template/Entity.cs.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Template\\Entity.cs.vm"}, {"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\index.vue.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "Template/index.vue.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Template\\index.vue.vm"}, {"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\Input.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "Template/Input.cs.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Template\\Input.cs.vm"}, {"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\Manage.js.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "Template/Manage.js.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Template\\Manage.js.vm"}, {"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\Output.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "Template/Output.cs.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Template\\Output.cs.vm"}, {"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\Service.cs.vm", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "Template/Service.cs.vm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Template\\Service.cs.vm"}, {"Identity": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\upload\\Address\\13750818896581.jpg", "SourceId": "Admin.NET.Web.Entry", "SourceType": "Discovered", "ContentRoot": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\", "BasePath": "_content/Admin.NET.Web.Entry", "RelativePath": "upload/Address/13750818896581.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\upload\\Address\\13750818896581.jpg"}]}