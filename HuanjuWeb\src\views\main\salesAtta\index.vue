﻿<template>
  <div class="salesAtta-container">
          <el-button type="primary" icon="ele-Plus" @click="openAddSalesAtta()" v-auth="'salesContract:add'"> 新增
     
          </el-button>
    <el-card class="full-table" shadow="hover" style="margin-top: 8px">
      <el-table
				:data="tableData"
				style="width: 100%"
				v-loading="loading"
				tooltip-effect="light"
				row-key="id"
				border=""
        :height="bomHeight">
        <el-table-column type="index" label="序号" width="55" align="center" fixed=""/>
        <el-table-column prop="address" label="附件地址" fixed="" show-overflow-tooltip="">
          <template #default="scope">
            <el-image
            style="width: 60px; height: 60px"
            :src="scope.row.address"
            :lazy="true"
            :hide-on-click-modal="true"
            :preview-src-list="[scope.row.address]"
            :initial-index="0"
            fit="scale-down"
            preview-teleported=""/>
            
          </template>
          
        </el-table-column>
        <el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip="" ><!-- v-if="auth('salesAtta:edit') || auth('salesAtta:delete')" -->
          <template #default="scope">
          <el-button icon="ele-Edit" size="small" text="" type="primary" @click="openEditSalesAtta(scope.row)" > 下载 </el-button> <!-- v-auth="'salesAtta:edit'" -->
            <el-button icon="ele-Delete" size="small" text="" type="primary" @click="delSalesAtta(scope.row)" > 删除 </el-button> <!-- v-auth="'salesAtta:delete'" -->
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
				v-model:currentPage="tableParams.page"
				v-model:page-size="tableParams.pageSize"
				:total="tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				small=""
				background=""
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
	/>
      <editDialog
			    ref="editDialogRef"
			    :title="editSalesAttaTitle"
			    @reloadTable="handleQuery"
      />
    </el-card>
  </div>
</template>

<script lang="ts" setup="" name="salesAtta">
  import { ref } from "vue";
  import { ElMessageBox, ElMessage } from "element-plus";
  import { auth } from '/@/utils/authFunction';
  //import { formatDate } from '/@/utils/formatTime';

  import editDialog from '/@/views/main/salesAtta/component/editDialog.vue'
  import { pageSalesAtta, deleteSalesAtta } from '/@/api/main/salesAtta';
	//父级传递来的参数
    const props = defineProps<{
      bomHeight: String,
    }>();
    const Ids=ref("");
    const editDialogRef = ref();
    const loading = ref(false);
    const tableData = ref<any>
      ([]);
      const queryParams = ref<any>
        ({});
        const tableParams = ref({
        page: 1,
        pageSize: 10,
        total: 0,
        });
        const editSalesAttaTitle = ref("");


        // 查询操作
        const handleQuery = async (Id:any) => {
          //debugger;
          Ids.value=Id;
          queryParams.value.salesID=Id;
        loading.value = true;
        var res = await pageSalesAtta(Object.assign(queryParams.value, tableParams.value));
        tableData.value = res.data.result?.items ?? [];
        tableParams.value.total = res.data.result?.total;
        loading.value = false;
        };

        // 打开新增页面
        const openAddSalesAtta = () => {
        editSalesAttaTitle.value = '添加合同附件';
        editDialogRef.value.openDialog({Ids});
        };

        // 打开编辑页面
        const openEditSalesAtta = (row: any) => {
        editSalesAttaTitle.value = '编辑合同附件';
        editDialogRef.value.openDialog(row);
        };

        // 删除
        const delSalesAtta = (row: any) => {
        ElMessageBox.confirm(`确定要删除吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        })
        .then(async () => {
        await deleteSalesAtta(row);
        //debugger;
        handleQuery(row.salesID);
        ElMessage.success("删除成功");
        })
        .catch(() => {});
        };

        // 改变页面容量
        const handleSizeChange = (val: number) => {
        tableParams.value.pageSize = val;
        handleQuery("");
        };

        // 改变页码序号
        const handleCurrentChange = (val: number) => {
        tableParams.value.page = val;
        handleQuery("");
        };


handleQuery("");
defineExpose({ handleQuery });
</script>


