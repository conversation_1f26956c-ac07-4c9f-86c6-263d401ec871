﻿namespace Admin.NET.Application;

    /// <summary>
    /// 出库记录输出参数
    /// </summary>
    public class OutboundRecordDto
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 出库明细Id
        /// </summary>
        public long? WarehouseOutMxId { get; set; }
        
        /// <summary>
        /// 出库数量
        /// </summary>
        public int? OutBoundCount { get; set; }
        
        /// <summary>
        /// 打印次数
        /// </summary>
        public int? PrintCount { get; set; }
        
        /// <summary>
        /// 批次Id
        /// </summary>
        public long? WarehouseBatchId { get; set; }
        
    }
