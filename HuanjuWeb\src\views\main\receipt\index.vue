﻿<template>
	<div class="receipt-container">
		<el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="queryParams" ref="queryForm" :inline="true">
				<el-form-item label="单位名称">
					<el-input v-model="queryParams.unitName" clearable="" placeholder="请输入单位名称" />
				</el-form-item>
				<el-form-item label="创建人">
					<el-input v-model="queryParams.createUserName" clearable="" placeholder="请输入创建人" />
				</el-form-item>
				<el-form-item label="收款状态">
					<el-select v-model="queryParams.paymentStatus" multiple placeholder="请选择收款状态">
						<el-option v-for="(item, index) in paymentStatus" :key="index" :value="item.value" :label="item.label"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="发票状态">
					<el-select v-model="queryParams.invoiceStatus" multiple placeholder="请选择发票状态">
						<el-option v-for="(item, index) in invoiceStatus" :key="index" :value="item.value" :label="item.label"></el-option>
					</el-select>
				</el-form-item>
			</el-form>
			<div style="margin-top: -10px; margin-bottom: 10px">
				<el-button-group>
					<el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'receipt:page'"> 查询 </el-button>
					<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
				</el-button-group>
				<el-button type="primary" icon="ele-Plus" @click="openAddReceipt" v-auth="'receipt:add'" style="margin-left: 10px"> 新增 </el-button>
				<el-button type="primary" icon="ele-Finished" @click="commitButtonClick"> 提交 </el-button>
				<el-button type="primary" icon="ele-Back" @click="withdraws"> 撤回 </el-button>
				<el-button type="primary" icon="ele-Finished" @click="suspend"> 中止 </el-button>
				<!-- <el-button type="primary" icon="ele-Plus" @click="MutlhoseInvoiceType"> 合并开票 </el-button> -->
			</div>
		</el-card>
		<el-card class="full-table" shadow="hover" style="margin-top: 8px">
			<el-table
				:data="tableData"
				style="width: 100%"
				:row-class-name="rowClassName"
				v-loading="loading"
				highlight-current-row
				tooltip-effect="light"
				:row-key="(row) => row.id"
				@row-click="handleRowClick"
				@selection-change="handleSelectionChange"
				border
			>
				<el-table-column type="selection" width="55" align="center" />
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="receiptNo" label="收款单号" width="100" show-overflow-tooltip="" />
				<el-table-column prop="unitName" label="单位名称" width="120" show-overflow-tooltip="" />
				<!-- <el-table-column prop="contractNum" label="合同编号" show-overflow-tooltip="" /> -->
				<el-table-column prop="superiorOrder" label="上级单号" width="100" show-overflow-tooltip="" />
				<el-table-column prop="abstract" label="摘要" show-overflow-tooltip="" />
				<el-table-column prop="documentAmount" label="单据金额" show-overflow-tooltip="" />
				<el-table-column prop="amountReceived" label="已收金额" show-overflow-tooltip="" />
				<el-table-column prop="pendingAmount" label="待收金额" show-overflow-tooltip="">
					<template #default="scope">
						<text>{{ scope.row.documentAmount - scope.row.amountReceived }}</text>
					</template>
				</el-table-column>
				<el-table-column prop="paymentStatus" label="收款状态" show-overflow-tooltip="">
					<template #default="scope">
						<el-tag type="danger" v-if="scope.row.paymentStatus === 0">待提交</el-tag>
						<el-tag type="danger" v-if="scope.row.paymentStatus === 1">待收款</el-tag>
						<el-tag type="danger" v-if="scope.row.paymentStatus === 2">部分收款</el-tag>
						<el-tag type="danger" v-if="scope.row.paymentStatus === 3">已收款</el-tag>
						<el-tag type="danger" v-if="scope.row.paymentStatus === 4">已中止</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="invoiceNo" label="发票号码" show-overflow-tooltip="" />
				<el-table-column prop="invoicedAmount" label="已开票金额" show-overflow-tooltip="" />
				<el-table-column prop="invoiceStatus" label="发票状态" show-overflow-tooltip="">
					<template #default="scope">
						<el-tag type="danger" v-if="scope.row.invoiceStatus === 0">无票</el-tag>
						<el-tag type="danger" v-if="scope.row.invoiceStatus === 1">待开</el-tag>
						<el-tag type="danger" v-if="scope.row.invoiceStatus === 2">已开</el-tag>
						<el-tag type="danger" v-if="scope.row.invoiceStatus === 3">申开</el-tag>
					</template>
				</el-table-column>
				<!--      <el-table-column prop="incomeType" label="收入类型" fixed="" show-overflow-tooltip="" /> -->
				<el-table-column prop="incomeType" label="科目名称" show-overflow-tooltip="">
					<!-- <template #default="scope">
						<el-tag type="danger" v-if="scope.row.incomeType === 0">主营业务收入</el-tag>
						<el-tag type="danger" v-if="scope.row.incomeType === 1">其他业务收入</el-tag>
					</template> -->
				</el-table-column>
				<!--          <el-table-column prop="incomeCategory" label="收入名目" fixed="" show-overflow-tooltip="" /> -->
				<el-table-column prop="incomeCategory" label="科目代码" show-overflow-tooltip="">
					<!-- <template #default="scope">
						<el-tag type="danger" v-if="scope.row.incomeCategory === 0">销售收入</el-tag>
						<el-tag type="danger" v-if="scope.row.incomeCategory === 1">非销售收入</el-tag>
					</template> -->
				</el-table-column>
				<!--         <el-table-column prop="paymentStatus" label="收款状态" fixed="" show-overflow-tooltip="" /> -->
				<!--          <el-table-column prop="trading" label="交易方式" fixed="" show-overflow-tooltip="" /> -->
				<el-table-column prop="tradingName" label="交易账户" show-overflow-tooltip="" />
				<!-- <template #default="scope">
            <el-tag type="danger" v-if="scope.row.trading === 0">现金</el-tag>
            <el-tag type="danger" v-if="scope.row.trading === 1">对公</el-tag>
            <el-tag type="danger" v-if="scope.row.trading === 2">支付宝</el-tag>
            <el-tag type="danger" v-if="scope.row.trading === 3">微信</el-tag>
          </template> 
        </el-table-column>-->
				<el-table-column prop="createUserName" label="创建人" show-overflow-tooltip=""></el-table-column>
				<el-table-column prop="createTime" label="创建时间" show-overflow-tooltip=""></el-table-column>
				<el-table-column prop="notes" label="备注" show-overflow-tooltip="" />
				<el-table-column label="操作" width="245" align="center" fixed="right" show-overflow-tooltip="" v-if="auth('receipt:edit') || auth('receipt:delete')">
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text="" type="primary" @click="openEditReceipt(scope.row)" v-auth="'receipt:edit'" :disabled="scope.row.paymentStatus === 0 ? false : true">
							编辑
						</el-button>
						<el-button icon="ele-Plus" size="small" text type="primary" @click="paymentOrder(scope.row)" :disabled="scope.row.paymentStatus !== 1 && scope.row.paymentStatus !== 2"> 收款 </el-button>
						<el-button icon="ele-Plus" size="small" text type="primary" @click="choseInvoiceType(scope.row)" :disabled="scope.row.paymentStatus !== 2 && scope.row.paymentStatus !== 3">
							开票
						</el-button>
						<el-button icon="ele-Delete" size="small" text="" type="primary" @click="delReceipt(scope.row)" v-auth="'receipt:delete'" :disabled="scope.row.paymentStatus === 0 ? false : true">
							删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="tableParams.page"
				v-model:page-size="tableParams.pageSize"
				:total="tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				small=""
				background=""
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
			/>
			<editDialog ref="editDialogRef" :title="editReceiptTitle" @reloadTable="handleQuery" />
			<PaidinAmount ref="PaidinAmountRef" :title="editPaymentOrderTitle" @reloadTable="handleQuery" />
			<TicketAmount ref="TicketAmountRef" :title="editTicketAmountTitle" @reloadTable="handleQuery" />
			<InvoiceType ref="InvoiceTypeRef" :title="editInvoiceTypeTitle" @reloadTable="handleQuery" />
		</el-card>
	</div>
</template>

<script lang="ts" setup="" name="receipt">
import { ref, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { auth } from '/@/utils/authFunction';
//import { formatDate } from '/@/utils/formatTime';
import { useRoute } from 'vue-router';

import editDialog from '/@/views/main/receipt/component/editDialog.vue';
import PaidinAmount from '/@/views/main/receipt/component/PaidinAmount.vue';
import TicketAmount from '/@/views/main/receipt/component/ticketAmount.vue';
import InvoiceType from '/@/views/main/receipt/component/InvoiceType.vue';
import { pageReceipt, deleteReceipt, Submit, Retract, Suspend } from '/@/api/main/receipt';

const editDialogRef = ref();
const tableRow = ref({});
const loading = ref(false);
const submitBtnStatus = ref(true); //提交按钮状态
const kpBtnStatus = ref(true);
const skBtnStatus = ref(true);
// const receiptButton = ref(true);
const invoiceButton = ref(true);
const editPaymentOrderTitle = ref('');
const editTicketAmountTitle = ref('');
const editInvoiceTypeTitle = ref('');
const PaidinAmountRef = ref();
const TicketAmountRef = ref();
const InvoiceTypeRef = ref();
const tableData = ref<any>([]);

const tableParams = ref({
	page: 1,
	pageSize: 20,
	total: 0,
});
const editReceiptTitle = ref('');
const paymentStatus = [
	{
		label: '待提交',
		value: 0,
	},
	{
		label: '待收款',
		value: 1,
	},
	{
		label: '部分收款',
		value: 2,
	},
	{
		label: '已收款',
		value: 3,
	},
	{
		label: '已中止',
		value: 4,
	},
]; //首款状态
const invoiceStatus = [
	{
		label: '无票',
		value: 0,
	},
	{
		label: '待开',
		value: 1,
	},
	{
		label: '已开',
		value: 2,
	},
	{
		label: '申开',
		value: 3,
	},
];
const selectedRows = ref<any>([]); // 保存选中的行数据
const rowClassName = (row: TableItem) => {
	return row.row.isSelected ? 'current-row' : '';
};
const handleSelectionChange = (selection: TableItem[]) => {
	selectedRows.value = selection;
	if (selection.length != 0) {
		selectedRows.value = selection;
		// // 待收款和部分收款可以收款
		// if ((selectedRows?.value[0].paymentStatus == 1 || selectedRows?.value[0].paymentStatus == 2) && selectedRows?.value.length == 1) {
		// 	receiptButton.value = false;
		// } else {
		// 	receiptButton.value = true;
		// }
		// 部分收款和已收款可以开票
		if ((selectedRows?.value[0].paymentStatus == 2 || selectedRows?.value[0].paymentStatus == 3) && selectedRows?.value.length == 1) {
			invoiceButton.value = false;
		} else {
			invoiceButton.value = true;
		}
	} else {
		// receiptButton.value = true;
		invoiceButton.value = true;
	}
	for (const row of tableData.value) {
		row.isSelected = false;
	}
	for (const selectedRow of selection) {
		const foundRow = tableData.value.find((row) => row.id === selectedRow.id);
		if (foundRow) {
			foundRow.isSelected = true;
		}
	}
};

// 查询操作
const handleQuery = async () => {
	try {
		loading.value = true;
		let paymentStatusParam = '';
		let invoiceStatusParam = '';
		
		if (queryParams.value && Array.isArray(queryParams.value.paymentStatus) && queryParams.value.paymentStatus.length > 0) {
			paymentStatusParam = queryParams.value.paymentStatus.join(',');
		}
		
		if (queryParams.value && Array.isArray(queryParams.value.invoiceStatus) && queryParams.value.invoiceStatus.length > 0) {
			invoiceStatusParam = queryParams.value.invoiceStatus.join(',');
		}

		const params = {
			...queryParams.value,
			paymentStatus: paymentStatusParam,
			invoiceStatus: invoiceStatusParam
		};
		
		var res = await pageReceipt(Object.assign(params, tableParams.value));
		tableData.value = res.data.result?.items ?? [];
		tableParams.value.total = res.data.result?.total;
	} catch (error) {
		console.error('查询失败:', error);
	} finally {
		loading.value = false;
	}
};

// 重置查询条件
const resetQuery = () => {
	queryParams.value = {};
	handleQuery();
};
// 打开收款页面
const paymentOrder = (row: any) => {
	editPaymentOrderTitle.value = '确认金额';
	PaidinAmountRef.value.openDialog({
		id: row.id,
	});
};
// 打开新增页面
const INVOICEOrder = () => {
	editTicketAmountTitle.value = '本次发票金额';
	TicketAmountRef.value.openDialog(tableRow.value);
};
// 打开新增页面
const openAddReceipt = () => {
	editReceiptTitle.value = '添加收款单';
	editDialogRef.value.openDialog({});
};

// 打开编辑页面
const openEditReceipt = (row: any) => {
	editReceiptTitle.value = '编辑收款单';
	editDialogRef.value.openDialog(row);
};

//合并开票（后续批量勾选是使用）
const MutlhoseInvoiceType = () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请先选中要开票的记录');
		return;
	}

	const listPurchaseIds = selectedRows.value.filter((item) => item.paymentStatus === 2 || item.paymentStatus === 3).map((item) => item.id);

	if (listPurchaseIds.length !== selectedRows.value.length) {
		ElMessage.warning('只有部分收款或已收款的记录可以开票');
		return;
	}

	editInvoiceTypeTitle.value = '选择发票类型';
	InvoiceTypeRef.value.openDialog(listPurchaseIds);
};

const choseInvoiceType = (row: any) => {
	var listPurchaseIds = ref<number[]>([]);
	// 将当前行的 id 添加到 listPurchaseIds
	listPurchaseIds.value.push(row.id);

	// 打开对话框
	editInvoiceTypeTitle.value = '选择发票类型';
	InvoiceTypeRef.value.openDialog(listPurchaseIds.value);
};

// 删除
const delReceipt = (row: any) => {
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await deleteReceipt(row);
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	tableParams.value.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	tableParams.value.page = val;
	handleQuery();
};
// handleQuery();
// 控制撤回按钮是否可用
const btnStatus = ref(true);
const handleRowClick = (row: any) => {
	tableRow.value = row;
	kpBtnStatus.value = false;
	skBtnStatus.value = false;
	if (tableRow.value.paymentStatus === 0) {
		submitBtnStatus.value = false;
	} else {
		submitBtnStatus.value = true;
	}
	if (tableRow.value.paymentStatus === 1) {
		btnStatus.value = false;
	} else {
		btnStatus.value = true;
	}
};
// 提交
const commitButtonClick = () => {
	// 待提交可以提交
	if (selectedRows.value.length == 0) {
		ElMessage.warning('请先选中要提交的记录');
		return;
	}
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		if (item.paymentStatus == 0 || item.paymentStatus == -1) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.receiptNo + ' 状态不正确，无法提交');
			return;
		}
	}
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要提交吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await Submit(listPurchaseIds.value);
			handleQuery();
			ElMessage.success('提交成功');
		})
		.catch(() => {});
	// ElMessageBox.confirm(`确定要提交吗?`, "提示", {
	//   confirmButtonText: "确定",
	//   cancelButtonText: "取消",
	//   type: "warning",
	// })
	//   .then(async () => {
	//     await Submit(tableRow.value);
	//     handleQuery();
	//     ElMessage.success("提交成功");
	//   })
	//   .catch(() => { });
};
// 撤回
const withdraws = () => {
	if (selectedRows.value.length == 0) {
		ElMessage.warning('请先选中要撤回的记录');
		return;
	}
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		if (item.paymentStatus == 1) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.receiptNo + ' 状态不正确，无法撤回');
			return;
		}
	}
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要撤回吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await Retract(listPurchaseIds.value);
			handleQuery();
			ElMessage.success('撤回成功');
		})
		.catch(() => {});
};
const suspend = () => {
	if (selectedRows.value.length == 0) {
		ElMessage.warning('请先选中要中止的记录');
		return;
	}
	var listPurchaseIds = ref<number[]>([]);
	for (let i = 0; i < selectedRows.value.length; i++) {
		const item = selectedRows.value[i];
		if (item.paymentStatus == 2) {
			listPurchaseIds.value.push(item.id);
		} else {
			ElMessage.warning(item.receiptNo + ' 状态不正确，无法中止');
			return;
		}
	}
	ElMessageBox.confirm(`共选中${listPurchaseIds.value.length}条记录，确定要中止吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await Suspend(listPurchaseIds.value);
			handleQuery();
			ElMessage.success('已中止');
		})
		.catch(() => {});
};

// 定义收款状态列表
const paymentStatusList = ref([
	{ label: '待收款', value: 0 },
	{ label: '部分收款', value: 1 },
	{ label: '已收款', value: 2 },
	{ label: '已作废', value: 3 },
]);

// 查询参数
const queryParams = ref<{
	paymentStatus: number[];
	invoiceStatus: number[];
	[key: string]: any;
}>({
	paymentStatus: [],
	invoiceStatus: []
});

const route = useRoute();
let initialized = false;

// 添加初始化方法
const initializeFilters = () => {
	if (initialized) return;
	
	const statusFromRoute = route.query.paymentStatus;
	const invoiceStatusFromRoute = route.query.invoiceStatus;
	
	if (statusFromRoute) {
		queryParams.value.paymentStatus = Array.isArray(statusFromRoute) 
			? statusFromRoute.map(Number)
			: [Number(statusFromRoute)];
	}
	
	if (invoiceStatusFromRoute) {
		queryParams.value.invoiceStatus = Array.isArray(invoiceStatusFromRoute)
			? invoiceStatusFromRoute.map(Number) 
			: [Number(invoiceStatusFromRoute)];
	}
	
	handleQuery();
	initialized = true;
};

// 在组件挂载时初始化
onMounted(() => {
	initializeFilters();
});
</script>
<style scoped>
</style>