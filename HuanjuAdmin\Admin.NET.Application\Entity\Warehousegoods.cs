﻿using System;
using SqlSugar;
using System.ComponentModel;
using Admin.NET.Core;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 商品信息表
    /// </summary>
    [SugarTable("warehousegoods", "商品信息表")]
    [Tenant("1300000000001")]
    public class Warehousegoods : EntityTenant
    {
        /// <summary>
        /// 商品名称
        /// </summary>
        [SugarColumn(ColumnDescription = "商品名称")]
        public string Name { get; set; }
        /// <summary>
        /// 品牌
        /// </summary>
        [SugarColumn(ColumnDescription = "品牌")]
        public string? Brand { get; set; }
        /// <summary>
        /// 编码
        /// </summary>
        [SugarColumn(ColumnDescription = "编码")]
        public string? Code { get; set; }
        /// <summary>
        /// 规格
        /// </summary>
        [SugarColumn(ColumnDescription = "规格")]
        public string? Specs { get; set; }

        /// <summary>
        /// 包装比例
        /// </summary>
        [SugarColumn(ColumnDescription = "包装比例")]
        public int? convertCount { get; set; }

        /// <summary>
        /// 商品条码
        /// </summary>
        [SugarColumn(ColumnDescription = "商品条码")]
        public string? barcode { get; set; }


        /// <summary>
        /// 是否负库存
        /// </summary>
        [SugarColumn(ColumnDescription = "是否负库存")]
        public bool? vacancy { get; set; }

        /// <summary>
        /// 是否唯一码
        /// </summary>
        [SugarColumn(ColumnDescription = "是否唯一码")]
        public bool? isuniquecode { get; set; }

        /// <summary>
        /// 是否批次
        /// </summary>
        [SugarColumn(ColumnDescription = "是否批次")]
        public bool isbatch { get; set; }
        /// <summary>
        /// 库存数量
        /// </summary>
        [SugarColumn(ColumnDescription = "库存数量")]
        public int InventoryCount { get; set; }
        /// <summary>
        /// 告警数量（库存不足）
        /// </summary>
        [SugarColumn(ColumnDescription = "告警数量(低)")]
        public int AlarmMin { get; set; }
        /// <summary>
        /// 库存数量（库存堆积）
        /// </summary>
        [SugarColumn(ColumnDescription = "告警数量(高)")]
        public int AlarmMax { get; set; }
        /// <summary>
        /// 初始库存数量
        /// </summary>
        [SugarColumn(ColumnDescription = "初始库存数量")]
        public int BaseInventoryCount { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注")]
        public string? Remark { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(ColumnDescription = "单位")]
        public long? Unit { get; set; }

        /// <summary>
        /// 辅助单位
        /// </summary>
        [SugarColumn(ColumnDescription = "辅助单位")]
        public long? Auxiliaryunit { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(Unit))]
        public WarehouseGoodsUnit WarehouseGoodsUnit { get; set; }

        /// <summary>
        /// 保质期（天）
        /// </summary>
        [SugarColumn(ColumnDescription = "保质期（天）")]
        public int? ExpirationDate { get; set; }

        /// <summary>
        /// 过期预警（天）
        /// </summary>
        [SugarColumn(ColumnDescription = "过期预警（天）")]
        public int? ExpiryReminder { get; set; }
    }
}