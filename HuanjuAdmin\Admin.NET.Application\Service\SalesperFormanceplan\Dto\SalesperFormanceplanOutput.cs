﻿using Admin.NET.Application.Enum;
using SqlSugar;
using System;

namespace Admin.NET.Application;

/// <summary>
/// 履约计划输出参数
/// </summary>
public class SalesperFormanceplanOutput
{
    public long Id { get; set; }
    /// <summary>
    /// 时间
    /// </summary>
    public DateTime? PlanTime { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public SalesTypeEnmu Type { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public SalesStatusEnmu Status { get; set; }

    /// <summary>
    /// 进度
    /// </summary>
    public int Plan { get; set; }
    /// <summary>
    /// 计划关联id
    /// </summary>
    [SugarColumn(ColumnDescription = "时间")]
    public long? PlanId { get; set; }
    public string SalesOrderName { get; set; }
}


