﻿using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Core;
using static SKIT.FlurlHttpClient.Wechat.Api.Models.ChannelsECWarehouseGetResponse.Types;
namespace Admin.NET.Application.Entity
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("warehouseconvertrecords","")]
    [Tenant("1300000000001")]
    public class Warehouseconvertrecord  : EntityTenant
    {
        /// <summary>
        /// 序号
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "序号")]
        public int Order { get; set; }
        /// <summary>
        /// 商品
        /// </summary>
        [SugarColumn(ColumnDescription = "商品", Length = 50)]
        public string? Commodity { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Required]
        [SugarColumn(ColumnDescription = "数量")]
        public int Num { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnDescription = "备注", Length = 0)]
        public string? Notes { get; set; }
        /// <summary>
        /// 转换类型
        /// </summary>
        [SugarColumn(ColumnDescription = "转换类型", Length = 50)]
        public int? Type { get; set; }
        /// <summary>
        /// 仓库
        /// </summary>
        [SugarColumn(ColumnDescription = "仓库", Length = 100)]
        public string? Warehouse { get; set; }
        /// <summary>
        /// 唯一码
        /// </summary>
        [SugarColumn(ColumnDescription = "唯一码", Length = 100)]
        public string? UniqueCode { get; set; }
        /// <summary>
        /// 批次号
        /// </summary>
        [SugarColumn(ColumnDescription = "批次号", Length = 100)]
        public string? BatchNumber { get; set; }
        /// <summary>
        /// 用户
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToOne, nameof(CreateUserId))]
        public SysUser Users { get; set; }
    }
}