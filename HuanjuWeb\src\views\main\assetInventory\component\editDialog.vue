﻿<template>
	<div class="assetInventory-container">
		<el-dialog v-model="isShowDialog" :title="props.title" :width="700" draggable=""  :close-on-click-modal="false">
			<el-form :model="ruleForm" ref="ruleFormRef" size="default" label-width="100px" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="ruleForm.id" />
					</el-form-item>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="名称" prop="name">
							<el-input v-model="ruleForm.name" placeholder="请输入名称" clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="品牌" prop="brand">
							<el-input v-model="ruleForm.brand" placeholder="请输入品牌" clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="编码" prop="code">
							<el-input v-model="ruleForm.code" placeholder="请输入编码" clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="规格" prop="specs">
							<el-input v-model="ruleForm.specs" placeholder="请输入规格" clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="总数量" prop="totalCount">
							<el-input-number v-model="ruleForm.totalCount" placeholder="请输入总数量" :disabled="ruleForm.id"
								clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="借用数量" prop="borrowCount">
							<el-input-number v-model="ruleForm.borrowCount" placeholder="请输入借用数量" :disabled="ruleForm.id"
								clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="库存数量" prop="inventoryCount">
							<el-input-number v-model="ruleForm.inventoryCount" placeholder="请输入库存数量" :disabled="ruleForm.id"
								clearable />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="需要归还" prop="isNdReturn">
							<el-switch v-model="ruleForm.isNdReturn" />

						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="ruleForm.remark" placeholder="请输入备注" clearable />

						</el-form-item>

					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button :loading="loading" type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { addAssetInventory, updateAssetInventory } from "/@/api/main/assetInventory";
//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: "",
	},
});
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const ruleForm = ref<any>({});
const loading = ref(false);
//自行添加其他规则
const rules = ref<FormRules>({
	name: [{ required: true, message: "请输入名称", trigger: "blur" }],
	// brand: [{ required: true, message: "请输入品牌", trigger: "blur" }],
	// code: [{ required: true, message: "请输入编码", trigger: "blur" }],
	// specs: [{ required: true, message: "请输入规格", trigger: "blur" }],
	totalCount: [{ required: true, message: "请输入总数量", trigger: "blur" }],
	borrowCount: [
		{ required: true, message: '请输入借用数量', trigger: 'blur' },
		{ type: 'number', message: '借用数量必须为数字值', trigger: 'blur' },
		{
			validator: (rule: any, value: any, callback: any) => {
				if (value < 0) {
					callback(new Error('借用数量不能小于0'));
				} else if (value != ruleForm.value.totalCount - ruleForm.value.inventoryCount) {
					callback(new Error('借用数量不等于总数量减库存数量'));
				} else {
					callback();
				}
			},
			trigger: 'blur',
		},
	],
	inventoryCount: [
		{ required: true, message: '请输入库存数量', trigger: 'blur' },
		{ type: 'number', message: '库存数量必须为数字值', trigger: 'blur' },
		{
			validator: (rule: any, value: any, callback: any) => {
				if (value < 0) {
					callback(new Error('库存数量不能小于0'));
				} else if (value != ruleForm.value.totalCount - ruleForm.value.borrowCount) {
					callback(new Error('库存数量不等于总数量减借用数量'));
				} else {
					callback();
				}
			},
			trigger: 'blur',
		},
	],
	remark: [{ max: 100, message: "备注不能超过100个字符", trigger: "blur" }],
});

// 打开弹窗
const openDialog = (row: any) => {
	ruleForm.value = JSON.parse(JSON.stringify(row));
	isShowDialog.value = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	isShowDialog.value = false;
	setTimeout(() => {
		loading.value = false;
	},500)
};

// 取消
const cancel = () => {
	isShowDialog.value = false;
	loading.value = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			loading.value = true;
			let values = ruleForm.value;
			if (ruleForm.value.id != undefined && ruleForm.value.id > 0) {
				await updateAssetInventory(values);
			} else {
				await addAssetInventory(values);
			}
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};





// 页面加载时
onMounted(async () => {
});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>




