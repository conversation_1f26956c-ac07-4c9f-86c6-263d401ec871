﻿using SqlSugar;
using System;

namespace Admin.NET.Application;

/// <summary>
/// 审批流程输出参数
/// </summary>
public class WorkflowOrderOutput
{
    /// <summary>
    /// Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 申请人ID
    /// </summary>
    public long? UerId { get; set; }

    /// <summary>
    /// 申请人ID
    /// </summary>
    public string SysUserRealName { get; set; }

    /// <summary>
    /// 审批单号
    /// </summary>
    public string ApprovalNumber { get; set; }

    /// <summary>
    /// 流程ID
    /// </summary>
    public long WorkflowId { get; set; }

    /// <summary>
    /// 流程名称
    /// </summary>
    public string ProcessName { get; set; }

    /// <summary>
    /// 流程地址
    /// </summary>
    public string ProcessAddress { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    /// <summary>
    /// 状态
    /// </summary>
    public ApproveStatusEnum Status { get; set; }

    /// <summary>
    /// 部门ID
    /// </summary>
    public long OrgId { get; set; }

    /// <summary>
    /// 部门ID
    /// </summary>
    public string SysOrgName { get; set; }

    public string Avatar { get; set; }

    public string SysPosName { get; set; }

}


