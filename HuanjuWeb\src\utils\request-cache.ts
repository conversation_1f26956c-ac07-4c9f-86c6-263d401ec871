/**
 * 请求缓存工具
 * 用于缓存API请求结果，避免重复请求
 */

interface CacheItem {
  data: any;
  timestamp: number;
  expiry: number;
}

class RequestCache {
  private cache = new Map<string, CacheItem>();
  private defaultExpiry = 5 * 60 * 1000; // 默认5分钟过期

  /**
   * 生成缓存键
   */
  private generateKey(url: string, params: any): string {
    return `${url}_${JSON.stringify(params)}`;
  }

  /**
   * 设置缓存
   */
  set(url: string, params: any, data: any, expiry?: number): void {
    const key = this.generateKey(url, params);
    const expiryTime = expiry || this.defaultExpiry;
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiry: expiryTime
    });
  }

  /**
   * 获取缓存
   */
  get(url: string, params: any): any | null {
    const key = this.generateKey(url, params);
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }
    
    // 检查是否过期
    if (Date.now() - item.timestamp > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }

  /**
   * 删除缓存
   */
  delete(url: string, params: any): void {
    const key = this.generateKey(url, params);
    this.cache.delete(key);
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.expiry) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// 创建全局缓存实例
export const requestCache = new RequestCache();

// 定期清理过期缓存
setInterval(() => {
  requestCache.cleanup();
}, 10 * 60 * 1000); // 每10分钟清理一次

/**
 * 带缓存的请求函数
 */
export async function cachedRequest<T>(
  url: string,
  params: any,
  requestFn: () => Promise<T>,
  expiry?: number
): Promise<T> {
  // 先尝试从缓存获取
  const cached = requestCache.get(url, params);
  if (cached) {
    console.log(`缓存命中: ${url}`, params);
    return cached;
  }

  // 缓存未命中，执行请求
  try {
    const result = await requestFn();
    // 将结果存入缓存
    requestCache.set(url, params, result, expiry);
    console.log(`缓存存储: ${url}`, params);
    return result;
  } catch (error) {
    console.error(`请求失败: ${url}`, error);
    throw error;
  }
}

export default requestCache; 