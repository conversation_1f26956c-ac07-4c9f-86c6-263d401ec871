﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Http;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.IO;
using System;

namespace Admin.NET.Application;
/// <summary>
/// 客户信息服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class PubcustomService : IDynamicApiController, ITransient
{
    private readonly UserManager _userManager;
    private readonly SqlSugarRepository<Pubcustom> _rep;
    private readonly SqlSugarRepository<ImportTemplate> _repImport;
    public PubcustomService(
        UserManager userManager,
        SqlSugarRepository<Pubcustom> rep,
        SqlSugarRepository<ImportTemplate> repImport)
    {
        _userManager = userManager;
        _rep = rep;
        _repImport = repImport;
    }

    /// <summary>
    /// 分页查询客户信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<PubcustomOutput>> Page(PubcustomInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(u => u.TenantId == _userManager.TenantId)
                    //管理员能看到全部，其它人可以看到公用和部门下的所有  
                    .WhereIF(!_userManager.SuperAdmin && !_userManager.Admin, u => u.IsCommunal || (!u.IsCommunal && u.OrgId == _userManager.OrgId))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Contacts), u => u.Contacts.Contains(input.Contacts.Trim()))
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Phone), u => u.Phone.Contains(input.Phone.Trim()))

                    .Select<PubcustomOutput>()
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加客户信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddPubcustomInput input)
    {
        var entity = input.Adapt<Pubcustom>();
        entity.OrgId = _userManager.OrgId;
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除客户信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeletePubcustomInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新客户信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdatePubcustomInput input)
    {
        var entity = input.Adapt<Pubcustom>();
        entity.OrgId = _userManager.OrgId;
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取客户信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<Pubcustom> Get([FromQuery] QueryByIdPubcustomInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取客户信息列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<PubcustomOutput>> List([FromQuery] PubcustomInput input)
    {
        return await _rep.AsQueryable().Select<PubcustomOutput>().ToListAsync();
    }



    /// <summary>
    /// 客户信息导入
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("import")]
    public async Task Import([FromForm] IFormFile file)
    {
        if (file == null || file.Length == 0)
            throw new Exception("文件不能为空");

        var importTemplate = await _repImport.GetFirstAsync(x => x.Name == "客户信息");
        if (importTemplate == null)
        {
            throw new Exception("客户信息模板不存在");
        }

        string currentRownum = "1";

        try
        {
            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                stream.Position = 0;

                IWorkbook workbook;
                if (file.FileName.EndsWith(".xlsx"))
                {
                    workbook = new XSSFWorkbook(stream);
                }
                else if (file.FileName.EndsWith(".xls"))
                {
                    workbook = new HSSFWorkbook(stream);
                }
                else
                {
                    throw new Exception("不支持的文件格式，请上传.xlsx或.xls文件");
                }

                ISheet sheet = workbook.GetSheetAt(0);

                for (int i = 0; i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row == null) continue;
                    if (i == 0)
                    {
                        if (GetCellValue(row.GetCell(0)).Trim() != importTemplate.Name) throw new Exception("模板名称不正确");
                        if (GetCellValue(row.GetCell(4)).Trim() != "v" + importTemplate.Version.ToString()) throw new Exception("模板版本不正确");
                        continue;
                    }
                    else if (i == 1) continue;

                    var custom = new Pubcustom();

                    var xuhao = GetCellValue(row.GetCell(0));
                    currentRownum = xuhao;

                    custom.Name = GetCellValue(row.GetCell(1));
                    if (custom.Name.IsNullOrEmpty()) throw new Exception($"序号：{currentRownum}客户名称为空");
                    custom.Type = GetCellValue(row.GetCell(2));
                    custom.Contacts = GetCellValue(row.GetCell(3));
                    if (custom.Contacts.IsNullOrEmpty()) throw new Exception($"序号：{currentRownum}联系人为空");
                    custom.Phone = GetCellValue(row.GetCell(4));
                    if (custom.Phone.IsNullOrEmpty()) throw new Exception($"序号：{currentRownum}联系方式为空");
                    custom.IsCommunal = true;
                    custom.TaxId = GetCellValue(row.GetCell(5));
                    custom.BankName = GetCellValue(row.GetCell(6));
                    custom.BankCode = GetCellValue(row.GetCell(7));
                    custom.Remark = GetCellValue(row.GetCell(8));
                    custom.Address = GetCellValue(row.GetCell(9));
                    custom.OrgId = _userManager.OrgId;

                    await _rep.InsertAsync(custom);
                }
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"导入失败：{ex.Message}\t 序号：{currentRownum}");
        }
    }

    private string GetCellValue(ICell cell)
    {
        if (cell == null)
            return string.Empty;

        switch (cell.CellType)
        {
            case CellType.Numeric:
                return cell.NumericCellValue.ToString();
            case CellType.String:
                return cell.StringCellValue;
            case CellType.Boolean:
                return cell.BooleanCellValue.ToString();
            case CellType.Formula:
                return cell.CellFormula;
            default:
                return string.Empty;
        }
    }

}

