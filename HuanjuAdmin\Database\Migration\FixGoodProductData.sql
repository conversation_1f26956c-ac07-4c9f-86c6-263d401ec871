-- 修复GoodProduct字段数据的脚本
-- 解决入库记录显示次品的问题

-- 1. 检查当前数据状态
SELECT '检查出库记录GoodProduct字段的数据分布:' as message;
SELECT 
    GoodProduct,
    COUNT(*) as count,
    CASE 
        WHEN GoodProduct = 1 THEN '良品'
        WHEN GoodProduct = 0 THEN '次品'
        ELSE 'NULL/未设置'
    END as description
FROM outboundrecord 
GROUP BY GoodProduct;

SELECT '检查入库记录GoodProduct字段的数据分布:' as message;
SELECT 
    GoodProduct,
    COUNT(*) as count,
    CASE 
        WHEN GoodProduct = 1 THEN '良品'
        WHEN GoodProduct = 0 THEN '次品'
        ELSE 'NULL/未设置'
    END as description
FROM inboundrecord 
GROUP BY GoodProduct;

-- 2. 更新出库记录的GoodProduct字段
-- 基于出库明细表的GoodProduct字段
UPDATE outboundrecord o
SET GoodProduct = (
    SELECT mx.GoodProduct 
    FROM warehouseoutmx mx 
    WHERE mx.Id = o.WarehouseOutMxId
    LIMIT 1
)
WHERE o.WarehouseOutMxId IS NOT NULL;

-- 3. 更新入库记录的GoodProduct字段
-- 基于入库明细表的GoodProduct字段
UPDATE inboundrecord i
SET GoodProduct = (
    SELECT mx.GoodProduct 
    FROM warehouseinrecordmx mx 
    WHERE mx.Id = i.WarehouseIncordMxId
    LIMIT 1
)
WHERE i.WarehouseIncordMxId IS NOT NULL;

-- 4. 对于没有关联明细的记录，设置默认值为良品(true)
UPDATE outboundrecord 
SET GoodProduct = 1 
WHERE GoodProduct IS NULL;

UPDATE inboundrecord 
SET GoodProduct = 1 
WHERE GoodProduct IS NULL;

-- 5. 检查更新后的数据状态
SELECT '更新后的出库记录GoodProduct字段分布:' as message;
SELECT 
    GoodProduct,
    COUNT(*) as count,
    CASE 
        WHEN GoodProduct = 1 THEN '良品'
        WHEN GoodProduct = 0 THEN '次品'
        ELSE 'NULL/未设置'
    END as description
FROM outboundrecord 
GROUP BY GoodProduct;

SELECT '更新后的入库记录GoodProduct字段分布:' as message;
SELECT 
    GoodProduct,
    COUNT(*) as count,
    CASE 
        WHEN GoodProduct = 1 THEN '良品'
        WHEN GoodProduct = 0 THEN '次品'
        ELSE 'NULL/未设置'
    END as description
FROM inboundrecord 
GROUP BY GoodProduct;

-- 6. 检查View_OutInBound视图中的最新数据
SELECT 'View_OutInBound视图中最新的10条记录:' as message;
SELECT 
    Id, 
    Type, 
    OrderNum, 
    GoodProduct,
    CASE 
        WHEN GoodProduct = 1 THEN '良品'
        WHEN GoodProduct = 0 THEN '次品'
        ELSE 'NULL/未设置'
    END as ProductType,
    CreateTime
FROM View_OutInBound 
ORDER BY CreateTime DESC 
LIMIT 10;

SELECT 'GoodProduct字段数据修复完成！' as message; 