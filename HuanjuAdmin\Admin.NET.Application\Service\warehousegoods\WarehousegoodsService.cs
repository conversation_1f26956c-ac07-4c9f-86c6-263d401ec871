﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Http;
using SqlSugar;
using System.IO;
using System;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Furion.DatabaseAccessor;
using Microsoft.AspNetCore.Mvc.Formatters;

namespace Admin.NET.Application;
/// <summary>
/// 商品信息服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class WarehousegoodsService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarClient _db;
    private readonly SqlSugarRepository<Warehousegoods> _rep;
    private readonly SqlSugarRepository<ImportTemplate> _repImport;
    private readonly SqlSugarRepository<WarehouseGoodsUnit> _repUnit;
    private readonly SqlSugarRepository<Warehouse> _repHouse;
    private readonly SqlSugarRepository<WarehouseStore> _repStore;
    UserManager _userManager;
    public WarehousegoodsService(SqlSugarRepository<Warehousegoods> rep, UserManager userManager, ISqlSugarClient db, SqlSugarRepository<ImportTemplate> repImport, SqlSugarRepository<WarehouseGoodsUnit> repUnit, SqlSugarRepository<WarehouseStore> repStore, SqlSugarRepository<Warehouse> repHouse)
    {
        _db = db;
        _rep = rep;
        _userManager = userManager;
        _repImport = repImport;
        _repUnit = repUnit;
        _repStore = repStore;
        _repHouse = repHouse;
    }

    /// <summary>
    /// 分页查询商品信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<WarehousegoodsOutput>> Page(WarehousegoodsInput input)
    {
        string sql = "SELECT s.*,w.Name unitName,wg.name auxiliaryunitName FROM warehousegoods s LEFT JOIN   warehousegoodsunit w ON s.unit=w.Id  LEFT JOIN   warehousegoodsunit wg ON s.auxiliaryunit=wg.Id";
        var query = _db.SqlQueryable<WarehousegoodsOutput>(sql)
            .Where(u => u.IsDelete == false && u.TenantId == _userManager.TenantId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name.Trim()))
             .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code.Trim()));

        //var query = _rep.AsQueryable()                      
        //            .Where(u => u.IsDelete == false && u.Tena.WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code.Trim()))ntId == _userManager.TenantId)
        //            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name.Trim()))
        //            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code.Trim()))

        //            .Select<WarehousegoodsOutput>();
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 查询所有商品信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetCommodity")]
    public async Task<List<WarehousegoodsOutput>> GetCommodity()
    {
        var query = await _rep.AsQueryable()
                    .Where(u => u.IsDelete == false && u.TenantId == _userManager.TenantId)
                    .Select<WarehousegoodsOutput>().ToListAsync();
        return query;
    }

    /// <summary>
    /// 获取商品ID列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "WarehousegoodsDropdown"), HttpGet]
    public async Task<dynamic> WarehousegoodsDropdown()
    {
        return await _rep.Context.Queryable<Warehousegoods>()
                .Where(x => x.IsDelete == false && x.TenantId == _userManager.TenantId)
                .Select(u => new
                {
                    Label = u.Name,
                    Value = u.Id
                }
                ).ToListAsync();
    }


    /// <summary>
    /// 通过ID查询商品信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "GetCommodityByID")]
    public async Task<WarehousegoodsOutput> GetCommodityByID(long Id)
    {
        var query = await _rep.AsQueryable()
                    .Where(u => u.IsDelete == false && u.TenantId == _userManager.TenantId)
                    .Where(u => u.Id == Id)
                    .Select<WarehousegoodsOutput>().FirstAsync();
        return query;
    }

    /// <summary>
    /// 增加商品信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddwarehousegoodsInput input)
    {
        if (!string.IsNullOrEmpty(input.Code))
        {
            var hasGoodsCode = _rep.AsQueryable().Any(x => x.Code == input.Code);
            if (hasGoodsCode) throw new Exception($"商品编码[{input.Code}]重复!");
        }
        input.BaseInventoryCount = input.InventoryCount;
        var entity = input.Adapt<Warehousegoods>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除商品信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeletewarehousegoodsInput input)
    {
        var entity = await _rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新商品信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdatewarehousegoodsInput input)
    {
        if (!string.IsNullOrEmpty(input.Code))
        {
            var hasGoodsCode = _rep.AsQueryable().Any(x => x.Code == input.Code && x.Id != input.Id);
            if (hasGoodsCode) throw new Exception("商品编码重复!");
        }
        var entity = input.Adapt<Warehousegoods>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取商品信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "Detail")]
    public async Task<Warehousegoods> Get([FromQuery] QueryByIdwarehousegoodsInput input)
    {
        return await _rep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取商品信息列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<WarehousegoodsOutput>> List([FromQuery] WarehousegoodsInput input)
    {
        return await _rep.AsQueryable().Select<WarehousegoodsOutput>().ToListAsync();
    }



    /// <summary>
    /// 商品信息导入
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("import")]
    [UnitOfWork]
    public async Task Import([FromForm] IFormFile file)
    {
        if (file == null || file.Length == 0)
            throw new Exception("文件不能为空");

        var importTemplate = await _repImport.GetFirstAsync(x => x.Name == "商品信息");
        if (importTemplate == null)
        {
            throw new Exception("商品信息模板不存在");
        }

        var listUnit = await _repUnit.GetListAsync(x => x.IsDelete == false);
        var listHouse = await _repHouse.GetListAsync(x => x.IsDelete == false);

        string currentRownum = "1";

        try
        {
            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                stream.Position = 0;

                IWorkbook workbook;
                if (file.FileName.EndsWith(".xlsx"))
                {
                    workbook = new XSSFWorkbook(stream);
                }
                else if (file.FileName.EndsWith(".xls"))
                {
                    workbook = new HSSFWorkbook(stream);
                }
                else
                {
                    throw Oops.Oh("不支持的文件格式，请上传.xlsx或.xls文件"); 
                }

                ISheet sheet = workbook.GetSheetAt(0);

                // 2025.4.2新增数据量上限，防止过大数据量导致导入数据时校验商品条码消耗过多性能
                if(sheet.LastRowNum > 1000)
                {
                    throw Oops.Oh("单次导入数据量过大，请控制在1000行以内");
                }
                Dictionary<string, int> map = new Dictionary<string, int>();
                // 防止有重复编码出现
                for (int i = 0; i <= sheet.LastRowNum; i++)
                {
                    
                    IRow row = sheet.GetRow(i);
                    if (row == null) continue;
                    if (i == 0)
                    {
                        if (GetCellValue(row.GetCell(0)).Trim() != importTemplate.Name) throw new Exception("模板名称不正确");
                        if (GetCellValue(row.GetCell(4)).Trim() != "v" + importTemplate.Version.ToString()) throw new Exception("模板版本不正确");
                        continue;
                    }
                    else if (i == 1) continue;
                    string? code = GetCellValue(row.GetCell(1));
                    if (string.IsNullOrWhiteSpace(code) )
                    {
                        continue;
                    }
                    if (map.ContainsKey(code))
                    {
                        throw Oops.Oh(string.Format(@"请保持商品编码唯一，编码为{0}的商品重复！", code));
                    }
                    else
                    {
                        map.Add(code, 1);
                    }
                }

                for (int i = 0; i <= sheet.LastRowNum; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row == null) continue;
                    if (i == 0)
                    {
                        if (GetCellValue(row.GetCell(0)).Trim() != importTemplate.Name) throw new Exception("模板名称不正确");
                        if (GetCellValue(row.GetCell(4)).Trim() != "v" + importTemplate.Version.ToString()) throw new Exception("模板版本不正确");
                        continue;
                    }
                    else if (i == 1) continue;

                    var wGoods = new Warehousegoods();

                    var xuhao = GetCellValue(row.GetCell(0));
                    currentRownum = xuhao;

                    wGoods.Code = GetCellValue(row.GetCell(1));
                    bool rs = _rep.AsQueryable().Any(x => x.Code == wGoods.Code);


                    if (rs) throw Oops.Oh($"商品编码 {wGoods.Code} 已存在");

                    wGoods.Name = GetCellValue(row.GetCell(2));
                    if (wGoods.Name.IsNullOrEmpty()) throw new Exception($"序号：{currentRownum}商品名称为空");
                    var danWei = GetCellValue(row.GetCell(5));
                    if (danWei.IsNullOrEmpty()) throw new Exception($"序号：{currentRownum}单位为空");

                    var goodsUnit = listUnit.Find(x => x.Name == danWei);
                    if (goodsUnit != null) wGoods.Unit = goodsUnit.Id;
                    else
                    {
                        //添加单位
                        var waGoodsUnit = new WarehouseGoodsUnit();
                        waGoodsUnit.Name = danWei;
                        waGoodsUnit.Status = true;
                        waGoodsUnit.Remark = "导入";
                        await _repUnit.InsertReturnBigIdentityAsync(waGoodsUnit);
                        wGoods.Unit = waGoodsUnit.Id;
                        listUnit.Add(waGoodsUnit);
                    }

                    var fuZhuDW = GetCellValue(row.GetCell(6));
                    if (!fuZhuDW.IsNullOrEmpty())
                    {
                        var goodsUnitFZ = listUnit.Find(x => x.Name == fuZhuDW);
                        if (goodsUnitFZ != null) wGoods.Auxiliaryunit = goodsUnitFZ.Id;
                        else
                        {
                            //添加单位
                            var waGoodsUnitFZ = new WarehouseGoodsUnit();
                            waGoodsUnitFZ.Name = fuZhuDW;
                            waGoodsUnitFZ.Status = true;
                            waGoodsUnitFZ.Remark = "导入";
                            await _repUnit.InsertReturnBigIdentityAsync(waGoodsUnitFZ);
                            wGoods.Auxiliaryunit = waGoodsUnitFZ.Id;
                            listUnit.Add(waGoodsUnitFZ);
                        }
                    }

                    wGoods.Brand = GetCellValue(row.GetCell(3));
                    wGoods.Specs = GetCellValue(row.GetCell(4));
                    wGoods.convertCount = GetCellValue(row.GetCell(7)).ToInt(0);
                    var isVacancy = GetCellValue(row.GetCell(8));
                    wGoods.vacancy = isVacancy == "是" ? true : false;
                    wGoods.barcode = GetCellValue(row.GetCell(9));
                    var isUniquecode = GetCellValue(row.GetCell(10));
                    wGoods.isuniquecode = isUniquecode == "是" ? true : false;
                    var isBatch = GetCellValue(row.GetCell(11));
                    wGoods.isbatch = isBatch == "是" ? true : false;
                    wGoods.Remark = "导入";

                    //_db.AsTenant().BeginTran();

                    await _rep.InsertReturnBigIdentityAsync(wGoods);

                    var warehouseName = GetCellValue(row.GetCell(12));
                    var goodProduct = GetCellValue(row.GetCell(13));
                    var rejectProduct = GetCellValue(row.GetCell(14));
                    if (!warehouseName.IsNullOrEmpty())
                    {
                        var warehouse = listHouse.Find(x => x.Name == warehouseName);
                        if (warehouse == null) throw new Exception($"序号：{currentRownum}指定的仓库{warehouseName}不存在");

                        var warehouseStore = new WarehouseStore();
                        warehouseStore.TradeID = wGoods.Id;
                        warehouseStore.WarehouseId = warehouse.Id;
                        warehouseStore.GoodProduct = goodProduct.ToInt(0);
                        warehouseStore.Reject = rejectProduct.ToInt(0);
                        warehouseStore.Compatible = warehouseStore.GoodProduct;
                        warehouseStore.Notes = "导入";

                        await _repStore.InsertAsync(warehouseStore);
                    }

                    //_db.AsTenant().CommitTran();
                }
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"导入失败：{ex.Message}\t 序号：{currentRownum}");
        }
    }

    private string GetCellValue(ICell cell)
    {
        if (cell == null)
            return string.Empty;

        switch (cell.CellType)
        {
            case CellType.Numeric:
                return cell.NumericCellValue.ToString();
            case CellType.String:
                return cell.StringCellValue;
            case CellType.Boolean:
                return cell.BooleanCellValue.ToString();
            case CellType.Formula:
                return cell.CellFormula;
            default:
                return string.Empty;
        }
    }
}