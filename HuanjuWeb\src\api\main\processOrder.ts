/*
 * @Author: WDD
 * @Date: 2025-04-13 11:28:02
 * @LastEditors: WDD
 * @LastEditTime: 2025-04-27 16:01:48
 * @Description: file content
 */
import request from '/@/utils/request';
enum Api {
	PageProcessOrder = '/api/processOrder/page',
	ListProcessOrder = '/api/processOrder/list',
	GetOneProcessOrder = '/api/processOrder/detail',
	AddProcessOrder = '/api/processOrder/add',
	UpdateProcessOrder = '/api/processOrder/update',
	DeleteProcessOrder = '/api/processOrder/delete',
	GetProcessOrderSchemeList = '/api/processOrder/getProcessOrderSchemeList',
	GetProcessOrderSchemeDetail = '/api/processOrder/getProcessOrderSchemeDetail',
	GetWarehouseList = '/api/processOrder/getWarehouseList',
	GetWarehouseStockList = '/api/processOrder/getWarehouseStockList'
}

// 分页查询出入库记录
export const pageProcessOrder = (params?: any) =>
	request({
		url: Api.PageProcessOrder,
		method: 'post',
		data: params,
	});

// 获取单个打印模板
export const getOneProcessOrder = (params?: any) =>
	request({
		url: Api.GetOneProcessOrder,
		method: 'post',
		data: params,
	});

export function addProcessOrder(records: any) {
	return request({
		url: Api.AddProcessOrder,
		method: 'post',
		data: records
	});
}

export function updateProcessOrder(records: any) {
	return request({
		url: Api.UpdateProcessOrder,
		method: 'post',
		data: records
	});
}

export function deleteProcessOrder(records: any) {
	return request({
		url: Api.DeleteProcessOrder,
		method: 'post',
		data: records
	});
}

// 获取加工单方案列表
export const getProcessOrderSchemeList = (params?: any) =>
	request({
		url: Api.GetProcessOrderSchemeList,
		method: 'post',
		data: params,
	});

// 获取加工单方案详情
export const getProcessOrderSchemeDetail = (params?: any) =>
	request({
		url: Api.GetProcessOrderSchemeDetail,
		method: 'post',
		data: params,
	});

// 获取仓库列表
export const getWarehouseList = (params?: any) =>
	request({
		url: Api.GetWarehouseList,
		method: 'post',
		data: params,
	});

// 获取仓库库存列表
export const getWarehouseStockList = (params?: any) =>
	request({
		url: Api.GetWarehouseStockList,
		method: 'post',
		data: params,
	});
	