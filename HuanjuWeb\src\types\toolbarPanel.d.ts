// 定义表单项类型
declare interface FormItem {
    label: string;
    prop: string;
    type: 'input' | 'select' | 'daterange' | string;
    placeholder?: string;
    clearable?: boolean;
    className?: string;
    style?: string;
    multiple?: boolean;
    options?: Array<{label: string, value: any}>;
  }
  
  // 定义按钮类型
  declare interface OperationButton {
    text: string;
    icon?: string;
    type?: string;
    handler: () => void;
    auth?: string;
  }
  