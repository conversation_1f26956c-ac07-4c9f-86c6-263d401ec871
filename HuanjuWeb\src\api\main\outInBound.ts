﻿import request from '/@/utils/request';
enum Api {
	PageOutboundRecord = '/api/outboundRecord/page',
	ListOutboundRecord = '/api/outboundRecord/list',
	GetOnePrint = '/api/sysPrint/getOnePrint',
	updatePrintCount = '/api/outboundRecord/updatePrintCount',
	RedInkOutboundRecord = '/api/outboundRecord/redInkOutboundRecord',
	RedInkInboundRecord = '/api/outboundRecord/redInkInboundRecord'
}

// 分页查询出入库记录
export const pageOutboundRecord = (params?: any) =>
	request({
		url: Api.PageOutboundRecord,
		method: 'post',
		data: params,
	});

// 查询出入库记录
export const listOutboundRecord = (params?: any) =>
	request({
		url: Api.ListOutboundRecord,
		method: 'post',
		data: params,
	});


// 获取单个打印模板
export const getOnePrint = (params?: any) =>
	request({
		url: Api.GetOnePrint,
		method: 'post',
		data: params,
	});

	export function updatePrintCount(records: { id: number, type: string }[]) {
		return request({
			url: Api.updatePrintCount,
			method: 'post',
			data: records  // 直接发送数组，不要包装在对象中
		});
	}

// 出库红冲
export const redInkOutboundRecord = (outboundRecordId: number) =>
	request({
		url: `${Api.RedInkOutboundRecord}?outboundRecordId=${outboundRecordId}`,
		method: 'post',
	});

// 入库红冲
export const redInkInboundRecord = (inboundRecordId: number) =>
	request({
		url: `${Api.RedInkInboundRecord}?inboundRecordId=${inboundRecordId}`,
		method: 'post',
	});