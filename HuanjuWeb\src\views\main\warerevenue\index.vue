<template>
  <div class="warerevenue-container">
    <el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="收支时间">
          <el-date-picker placeholder="请选择收支时间" value-format="YYYY/MM/DD" type="daterange" v-model="queryParams.revenueTimeRange" />
          
        </el-form-item>
        <el-form-item label="来往单位">
          <el-input v-model="queryParams.contactunits" clearable="" placeholder="请输入来往单位"/>
          
        </el-form-item>
        <el-form-item label="经办人">
          <el-input v-model="queryParams.handledbyName" clearable="" placeholder="请输入经办人"/>
        </el-form-item>
             
						<el-form-item label="类型">
							<el-select clearable v-model="queryParams.revenueType" placeholder="请选择类型">
								<el-option v-for="(item, index) in  revenueType" :key="index" :value="item.value"
									:label="item.label"></el-option>

							</el-select>

						</el-form-item>
        <el-form-item>
          <el-button-group>
            <el-button type="primary"  icon="ele-Search" @click="handleQuery" v-auth="'warerevenue:page'"> 查询 </el-button>
            <el-button icon="ele-Refresh" @click="() => queryParams = {}"> 重置 </el-button>
            
          </el-button-group>
          
        </el-form-item>
        <el-form-item>
          <!-- <el-button type="primary" icon="ele-Plus" @click="openAddwarerevenue" v-auth="'warerevenue:add'"> 新增 </el-button> -->
          <el-button type="primary" icon="ele-Plus">导出</el-button>
        </el-form-item>
        
      </el-form>
    </el-card>
    <el-card class="full-table" shadow="hover" style="margin-top: 8px">
      <el-table
				:data="tableData"
				style="width: 100%"
				v-loading="loading"
				tooltip-effect="light"
				row-key="id"
				border="">
        <el-table-column type="index" label="序号" width="55" align="center" fixed=""/>
         <!-- <el-table-column prop="id" label="主键Id" fixed="" show-overflow-tooltip="" /> -->
         <el-table-column prop="revenueType" label="类型" fixed="" show-overflow-tooltip="">
          <template #default="scope">
            <el-tag  v-if="scope.row.revenueType === 1">收入</el-tag>
            <el-tag  v-if="scope.row.revenueType === 2">支出</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="contactunits" label="来往单位" fixed="" show-overflow-tooltip="" />
        <el-table-column prop="ordernumber" label="关联单号" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="subject" label="科目名称" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="levelsubject" label="科目代码" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="incomeamount" label="收入金额" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="expenditureamount" label="支出金额" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="handledbyName" label="经办人" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="revenueTime" label="收支时间" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="TradingName" label="收支帐户" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="notes" label="备注" fixed="" show-overflow-tooltip="" />
          <!-- <el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip="" v-if="auth('warerevenue:edit') || auth('warerevenue:delete')">
           <template #default="scope">
           <el-button icon="ele-Edit" size="small" text="" type="primary" @click="openEditwarerevenue(scope.row)" v-auth="'warerevenue:edit'"> 编辑 </el-button>
            <el-button icon="ele-Delete" size="small" text="" type="primary" @click="delwarerevenue(scope.row)" v-auth="'warerevenue:delete'"> 删除 </el-button>
          </template>
        </el-table-column> -->
      </el-table>
      <el-pagination
				v-model:currentPage="tableParams.page"
				v-model:page-size="tableParams.pageSize"
				:total="tableParams.total"
				:page-sizes="[10, 20, 50, 100]"
				small=""
				background=""
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				layout="total, sizes, prev, pager, next, jumper"
	/>
      <editDialog
			    ref="editDialogRef"
			    :title="editwarerevenueTitle"
			    @reloadTable="handleQuery"
      />
    </el-card>
  </div>
</template>

<script lang="ts" setup="" name="warerevenue">
  import { ref } from "vue";
  import { ElMessageBox, ElMessage } from "element-plus";
  import { auth } from '/@/utils/authFunction';
  //import { formatDate } from '/@/utils/formatTime';

  import editDialog from '/@/views/main/warerevenue/component/editDialog.vue'
  import { pagewarerevenue, deletewarerevenue } from '/@/api/main/warerevenue';


    const editDialogRef = ref();
    const loading = ref(false);
    const tableData = ref<any>
      ([]);
      const queryParams = ref<any>
        ({});
        const tableParams = ref({
        page: 1,
        pageSize: 10,
        total: 0,
        });
        const editwarerevenueTitle = ref("");

        const revenueType = [//付款类型
	{ label: '收入', value: 1 },
	{ label: '支出', value: 2 },
]

        // 查询操作
        const handleQuery = async () => {
        loading.value = true;
        var res = await pagewarerevenue(Object.assign(queryParams.value, tableParams.value));
        tableData.value = res.data.result?.items ?? [];
        tableParams.value.total = res.data.result?.total;
        loading.value = false;
        };

        // 打开新增页面
        const openAddwarerevenue = () => {
        editwarerevenueTitle.value = '添加收支明细';
        editDialogRef.value.openDialog({});
        };

        // 打开编辑页面
        const openEditwarerevenue = (row: any) => {
        editwarerevenueTitle.value = '编辑收支明细';
        editDialogRef.value.openDialog(row);
        };

        // 删除
        const delwarerevenue = (row: any) => {
        ElMessageBox.confirm(`确定要删除吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        })
        .then(async () => {
        await deletewarerevenue(row);
        handleQuery();
        ElMessage.success("删除成功");
        })
        .catch(() => {});
        };

        // 改变页面容量
        const handleSizeChange = (val: number) => {
        tableParams.value.pageSize = val;
        handleQuery();
        };

        // 改变页码序号
        const handleCurrentChange = (val: number) => {
        tableParams.value.page = val;
        handleQuery();
        };


handleQuery();
</script>


