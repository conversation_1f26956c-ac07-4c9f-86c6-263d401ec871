﻿using Admin.NET.Application.Const;
using Admin.NET.Application.Entity;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using Furion.FriendlyException;
using Microsoft.AspNetCore.Hosting;
using System;
using COSXML.Network;
using NPOI.HPSF;
using System.Text;

namespace Admin.NET.Application;
/// <summary>
/// 导入模板服务
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public class ImportTemplateService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<ImportTemplate> _rep;
    public ImportTemplateService(SqlSugarRepository<ImportTemplate> rep)
    {
        _rep = rep;
    }

    /// <summary>
    /// 分页查询导入模板
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<ImportTemplateOutput>> Page(ImportTemplateInput input)
    {
        var query = _rep.AsQueryable()
                    .Where(u => u.IsDelete == false)
                    .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name.Trim()))
                    .Select<ImportTemplateOutput>()
;
        query = query.OrderBuilder(input);
        return await query.ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加导入模板
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddImportTemplateInput input)
    {
        var entity = input.Adapt<ImportTemplate>();
        await _rep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除导入模板
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeleteImportTemplateInput input)
    {
        var entity = input.Adapt<ImportTemplate>();
        await _rep.FakeDeleteAsync(entity);   //假删除
    }

    /// <summary>
    /// 更新导入模板
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdateImportTemplateInput input)
    {
        var entity = input.Adapt<ImportTemplate>();
        await _rep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取导入模板列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    public async Task<List<ImportTemplateOutput>> List([FromQuery] ImportTemplateInput input)
    {
        return await _rep.AsQueryable().Select<ImportTemplateOutput>().ToListAsync();
    }

    /// <summary>
    /// 下载导入模板
    /// </summary>
    /// <param name="id">模板ID</param>
    /// <returns>文件流</returns>
    [HttpGet("download/{id}")]
    [ApiDescriptionSettings(Name = "Download")]
    public async Task<IActionResult> DownloadTemplate(long id)
    {
        var template = await _rep.GetFirstAsync(t => t.Id == id && !t.IsDelete);
        if (template == null)
            throw Oops.Oh(ErrorCodeEnum.D1002);

        var fileName = template.FilePath;
        var fullPath = Path.Combine(AppContext.BaseDirectory, "ImportTemplate", fileName);
        if (!File.Exists(fullPath))
            throw Oops.Oh(ErrorCodeEnum.D1002);

        var fileBytes = await File.ReadAllBytesAsync(fullPath);

        // 使用 FileContentResult 来设置正确的 Content-Disposition 头
        return new FileContentResult(fileBytes, "application/octet-stream")
        {
            FileDownloadName = $"=?UTF-8?B?{Convert.ToBase64String(Encoding.UTF8.GetBytes(fileName))}?="
        };
    }

}

