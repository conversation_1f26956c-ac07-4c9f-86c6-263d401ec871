﻿<template>
	<!-- ---------------------入库单下-------------------------- -->
	<div class="warehouseInrecordMX-container">
		<el-card class="bomCard" shadow="hover">
			<el-form>
				<el-form-item class="bomForm">
					<span class="text-lg font-bold">入库单号：{{ props.orderNumber }}</span>
					<el-button class="addBtn" type="primary" icon="ele-Plus" @click="Warehousing" :disabled="btnStatus"> 入库 </el-button>
					<el-button class="addBtn" type="primary" icon="ele-List" @click="InBoundRecord" :disabled="btnRecord"> 入库记录 </el-button>
				</el-form-item>
			</el-form>
			<el-table :data="props.detailsData || []" :key="props.detailsData.length" style="width: 100%" v-loading="loading" tooltip-effect="light" row-key="id" border="" class="bomTable" :height="bomHeight">
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="incordNumber" label="入库单号" width="120" show-overflow-tooltip="" />
				<el-table-column prop="goodsId" label="商品" show-overflow-tooltip="">
					<template #default="scope">
						<span>{{ scope.row.warehousegoodsName }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="barcode" label="商品条码" width="90" show-overflow-tooltip="" />
				<el-table-column prop="productCode" label="商品编码" width="90" show-overflow-tooltip="" />
				<el-table-column prop="brandName" label="品牌" width="90" show-overflow-tooltip="" />
				<el-table-column prop="specsName" label="规格" width="90" show-overflow-tooltip="" />
				<el-table-column prop="unitName" label="单位" width="60" show-overflow-tooltip="" />
				<el-table-column prop="unitprice" label="单价" width="70" show-overflow-tooltip="" />
				<el-table-column prop="totalAmt" label="合计" width="90" show-overflow-tooltip="" />
				<el-table-column prop="documentNum" label="单据数量" width="65" show-overflow-tooltip="" />
				<el-table-column prop="rcvQty" label="入库数量" width="65" show-overflow-tooltip="" />
				<!-- <el-table-column prop="supplierId" label="供应商" show-overflow-tooltip="">
					<template #default="scope">
						<span>{{ scope.row.pubSupplierName }}</span>
					</template>
				</el-table-column> -->
				<!-- <el-table-column label="入库记录" show-overflow-tooltip>
          <template #default="scope">
            <ModifyRecord v-if="scope.row.listOutInBound" :data="scope.row.listOutInBound" />
          </template>
        </el-table-column> -->
			</el-table>
			<!-- <el-pagination v-model:currentPage="tableParams.page" v-model:page-size="tableParams.pageSize"
        :total="tableParams.total" :page-sizes="[10, 20, 50, 100]" small="" background=""
        @size-change="handleSizeChange" @current-change="handleCurrentChange"
        layout="total, sizes, prev, pager, next, jumper" /> -->
			<editDialogWarehousing ref="editDialogWarehousingRef" :title="editDialogWare" @reloadTable="sxHandleQuery" />
			<outInBoundRecord ref="inBoundRecordRef" :title="inBoundRecordDialog" @reloadTable="sxHandleQuery" />
		</el-card>
	</div>
</template>

<script lang="ts" setup="" name="warehouseInrecordMX">
import { ref, watch } from 'vue';
import editDialogWarehousing from '/@/views/main/warehouseInrecord/component/editDialogWarehousing.vue';
import { getIncordMxList } from '/@/api/main/warehouseInrecordMX';
import outInBoundRecord from '/@/views/main/warehouseInrecordMX/component/OutInBoundRecord.vue';
import { listOutboundRecord } from '/@/api/main/outInBound';
// import ModifyRecord from './component/modifyRecord.vue'
import { PubSupplierDropdown } from '/@/api/main/pubSupplier';
import { debug } from 'console';
const emit = defineEmits(['reloadTable']);

const editDialogWarehousingRef = ref();
const inBoundRecordRef = ref();
const loading = ref(false);
const tableData = ref<any>([]);
const tableParams = ref({
	page: 1,
	pageSize: 10,
	total: 0,
});

const editDialogWare = ref('');
const inBoundRecordDialog = ref('');
const props = defineProps<{
	orderDetail: String;
	orderNumber: string;
	orderInhouseStatus: Number;
	bomHeight: String;
	detailsData: any[];
}>();
const btnStatus = ref(false); //入库按钮的禁用
const btnRecord = ref(false); //入库记录按钮的禁用

watch(
	() => props.orderDetail,
	(n) => {
		if (n) {
			handleQuery(true);
		} else {
			tableData.value = [];
		}
	}
);

// 判断入库按钮是否可以使用
const determineBtnStatus = () => {
	if (props.orderDetail) {
		if (props.orderInhouseStatus === 0 || props.orderInhouseStatus === 1) btnRecord.value = true;
		else btnRecord.value = false;
		if (props.orderInhouseStatus === 1 || props.orderInhouseStatus === 2) {
			btnStatus.value = false;
		} else {
			btnStatus.value = true;
		}
	} else {
		btnStatus.value = true;
		btnRecord.value = true;
	}
};
determineBtnStatus();
const Warehousing = async () => {
	editDialogWare.value = '编辑入库单明细';
	debugger;
	editDialogWarehousingRef.value.openDialog(tableData.value);
};

const InBoundRecord = async () => {
	if (!props.orderDetail) {
		return;
	}

	if (props.orderDetail) {
		let params = {
			type: '1',
			orderNumber: props.orderNumber,
		};
		var res = await listOutboundRecord(params);
		if (res.data.result) {
			inBoundRecordDialog.value = '入库记录';
			inBoundRecordRef.value.openDialog(res.data.result);
		}
	}
};

// 查询操作
const handleQuery = async (isNewQuery = false) => {
	loading.value = true;
	let params = {
		page: tableParams.value.page,
		pageSize: tableParams.value.pageSize,
		total: 0,
		inrecordId: props.orderDetail,
	};
	var res = await getIncordMxList(params);
	
	// 如果是新查询，则替换数据；否则保留现有数据（用于追加模式）
	if (isNewQuery) {
		tableData.value = res.data.result ?? [];
	}
	
	loading.value = false;
	determineBtnStatus();
};
// 刷新数据
const sxHandleQuery = () => {
	// 先通知父组件刷新数据
	emit('reloadTable');
	
	// 然后本地查询数据
	handleQuery();
};

// // 改变页面容量
// const handleSizeChange = (val: number) => {
//   if (!tableData.length) return;
//   tableParams.value.pageSize = val;
//   handleQuery();
// };

// // 改变页码序号
// const handleCurrentChange = (val: number) => {
//   if (!tableData.length) return;
//   tableParams.value.page = val;
//   handleQuery();
// };

const pubSupplierDropdownList = ref<any>([]);
const getPubSupplierDropdownList = async () => {
	let list = await PubSupplierDropdown();
	pubSupplierDropdownList.value = list.data.result ?? [];
};
getPubSupplierDropdownList();

// 添加一个新方法来清空所有明细数据
const clearAllDetails = () => {
	console.log('清空所有明细数据');
	tableData.value = [];
};

// 将明细数据追加到表格 - 更简单直接的实现
const appendDetails = (details) => {
	if (!details || details.length === 0) {
		console.log('追加操作：没有明细数据可追加');
		return;
	}
	
	console.log(`追加 ${details.length} 条明细数据`);
	
	// 直接添加明细数据到表格，不需要过滤等逻辑
	tableData.value = [...tableData.value, ...details];
	
	console.log('追加后明细数据总数:', tableData.value.length);
};

// 通过入库单ID移除明细
const removeDetailsByOrderId = (inrecordId) => {
	if (!inrecordId) {
		console.log('移除操作：没有提供入库单ID');
		return;
	}
	
	console.log('移除入库单ID:', inrecordId);
	console.log('移除前明细数:', tableData.value.length);
	
	// 打印几条数据的 inrecordId 属性用于调试
	if (tableData.value.length > 0) {
		console.log('明细数据的inrecordId示例:', 
			tableData.value.slice(0, 3).map(item => item.inrecordId)
		);
	}
	
	const beforeLength = tableData.value.length;
	tableData.value = tableData.value.filter(item => item.inrecordId !== inrecordId);
	console.log('移除后明细数:', tableData.value.length, '移除:', beforeLength - tableData.value.length);
};

// 暴露这些方法给父组件
defineExpose({
	appendDetails,
	removeDetailsByOrderId,
	clearAllDetails,
	tableData
});

// 简化明细表数据处理
watch(
	() => tableData.value,
	(newVal) => {
		console.log(`明细表数据变化，当前有 ${newVal.length} 条数据`);
		if (newVal.length > 0) {
			// 检查第一条数据是否有入库单号
			console.log("明细表第一条数据:", {
				id: newVal[0].id,
				incordNumber: newVal[0].incordNumber,
				orderNumber: newVal[0].orderNumber
			});
		}
	},
	{ deep: true }
);

// 添加监听父组件传入的明细数据
watch(
	() => props.detailsData,
	(newData) => {
		if (newData) {
			console.log(`接收到新的明细数据，共 ${newData.length} 条`);
			// 不需要设置tableData了，因为我们直接用props.detailsData渲染
		}
	},
	{ deep: true }
);
</script>

<style lang="scss" scoped>
.warehouseInrecordMX-container {
	height: 100%;
}

.bomCard {
	height: 100%;

	.bomForm {
		display: flex;
		margin-bottom: 6px;

		.el-input {
			width: 150px;
		}

		.addBtn {
			margin-left: 10px;
		}
	}

	.bomTable {
		margin-top: 6px;
	}
}

.text-lg {
	font-size: 1.125rem;
}

.font-bold {
	font-weight: 700;
}
</style>
