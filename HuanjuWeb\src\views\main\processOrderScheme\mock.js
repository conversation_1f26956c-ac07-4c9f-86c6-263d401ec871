// 模拟根据查询条件、分页参数查询数据

export const getOrderList = (queryParams, pageParams) => {
    return new Promise((resolve, reject) => {
        setTimeout(() => {

            const orderList = Array.from({ length: 100 }, (_, index) => ({
                id: index + 1,
                schemeNo: `方案编号${index + 1}`,
                schemeName: `方案名称${index + 1}`,
                createTime: `2${index + 1}-01-01 12:00:00`,
                updateTime: `2${index + 1}-01-01 12:00:00`,  
                status: index%2 === 0 ? 1 : 0,
            }));

           const filterOrderList = orderList.filter(item => {
                if (queryParams.schemeName) {
                    return item.schemeName.includes(queryParams.schemeName);
                }
                return true;
            });

            const total = filterOrderList.length;

            resolve({
                data: filterOrderList.slice((pageParams.page - 1) * pageParams.pageSize, pageParams.page * pageParams.pageSize),
                total: total,
            });
        }, 1000);
    });
};  


// 模拟点击了订单行，返回原料列表和产出列表
export const getOrderInfo = (id) => {
    return new Promise((resolve, reject) => {
        setTimeout(async () => {
            const orderInfo = {
                id: id,
                schemeNo: `方案编号${id}`,
                schemeName: `方案名称${id}`,
                createTime: `2${id}-01-01 12:00:00`,
                updateTime: `2${id}-01-01 12:00:00`,  
                status: id%2 === 0 ? 1 : 0,
                materialList: await getMaterialList(id),
                outputList: await getOutputList(id),
            };
            resolve(orderInfo);
        }, 1000);
    });
};

export const getMaterialList = (id) => {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            const materialList = Array.from({ length: 15 }, (_, index) => ({
                id: index + 1,
                materialCode: `M00${index + 1}`,
                materialName: `原料${index + 1}`,
                specification: `规格${index + 1}`,
                unit: '个',
                quantity: 10,
            }));
            resolve(materialList);
        }, 1000);
    });
};  

export const getOutputList = (id) => {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            const outputList = Array.from({ length: 12 }, (_, index) => ({
                id: index + 1,
                productCode: `P00${index + 1}`,
                productName: `产品${index + 1}`,
                specification: `规格${index + 1}`,
                unit: '个',
                quantity: 100,
            }));
            resolve(outputList);
        }, 1000);
    });
};  
