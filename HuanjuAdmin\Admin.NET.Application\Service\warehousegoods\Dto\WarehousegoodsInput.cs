﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

/// <summary>
/// 商品信息基础输入参数
/// </summary>
public class WarehousegoodsBaseInput
{
    /// <summary>
    /// 商品名称
    /// </summary>
    public virtual string Name { get; set; }

    /// <summary>
    /// 品牌
    /// </summary>
    public virtual string? Brand { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    public virtual string? Code { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public virtual string? Specs { get; set; }

    /// <summary>
    /// 库存数量
    /// </summary>
    public virtual int InventoryCount { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public virtual string? unit { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public virtual string? auxiliaryunit { get; set; }

    /// <summary>
    /// 是否负库存
    /// </summary>
    public virtual bool? vacancy { get; set; }

    /// <summary>
    /// 包装比例
    /// </summary>
    public virtual int? convertCount { get; set; }

    /// <summary>
    /// 商品条码
    /// </summary>
    public virtual string? barcode { get; set; }

    /// <summary>
    /// 是否唯一码
    /// </summary>
    public virtual long? isuniquecode { get; set; }

    /// <summary>
    /// 是否批次
    /// </summary>
    public virtual long? isbatch { get; set; }


    /// <summary>
    /// 告警数量（库存不足）
    /// </summary>
    public int AlarmMin { get; set; }

    /// <summary>
    /// 库存数量（库存堆积）
    /// </summary>
    public int AlarmMax { get; set; }

    /// <summary>
    /// 初始库存数量
    /// </summary>
    public virtual int BaseInventoryCount { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 保质期（天）
    /// </summary>
    public int? ExpirationDate { get; set; }

    /// <summary>
    /// 过期预警（天）
    /// </summary>
    public int? ExpiryReminder { get; set; }

}

/// <summary>
/// 商品信息分页查询输入参数
/// </summary>
public class WarehousegoodsInput : BasePageInput
{
    /// <summary>
    /// 商品名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    public string? Code { get; set; }

}

/// <summary>
/// 商品信息增加输入参数
/// </summary>
public class AddwarehousegoodsInput : WarehousegoodsBaseInput
{
}

/// <summary>
/// 商品信息删除输入参数
/// </summary>
public class DeletewarehousegoodsInput : BaseIdInput
{
}

/// <summary>
/// 商品信息更新输入参数
/// </summary>
public class UpdatewarehousegoodsInput : WarehousegoodsBaseInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public long Id { get; set; }

}

/// <summary>
/// 商品信息主键查询输入参数
/// </summary>
public class QueryByIdwarehousegoodsInput : DeletewarehousegoodsInput
{

}
