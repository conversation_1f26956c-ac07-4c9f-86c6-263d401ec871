﻿<template>
  <div class="warehousebatch-container">
    <el-dialog v-model="isShowDialog" :title="props.title" :width="1200" draggable="" :close-on-click-modal="false">
      <el-form>
        <el-table :data="tableDataMX" style="width: 100%" tooltip-effect="light">
          <el-table-column type="index" label="序号" width="55" align="center" fixed="" />
          <el-table-column prop="tradeName" label="商品名称" fixed="" show-overflow-tooltip="" />
          <el-table-column prop="batchnumber" label="批次号" fixed="" show-overflow-tooltip="" />
          <el-table-column prop="goodProductNum" label="良品数量" fixed="" show-overflow-tooltip="" />
          <el-table-column prop="rejectNum" label="次品数量" fixed="" show-overflow-tooltip="" />
          <el-table-column prop="produceTime" label="生产日期" fixed="" show-overflow-tooltip="">
            <template #default="scope">
              {{ formatDate(scope.row.produceTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="warrantyTime" label="保质期" fixed="" show-overflow-tooltip="" />
          <el-table-column prop="expiryReminder" label="提醒天数" fixed="" show-overflow-tooltip="" />
          <el-table-column prop="shelflifeStatus" label="临期提醒" fixed="" show-overflow-tooltip="">
            <template #default="scope">
              <el-tag type="success" v-if="scope.row.shelflifeStatus == -1">未设置</el-tag>
              <el-tag type="success" v-if="scope.row.shelflifeStatus == 0">正常</el-tag>
              <el-tag type="danger" v-else-if="scope.row.shelflifeStatus == 1">临期</el-tag>
              <el-tag type="danger" v-else-if="scope.row.shelflifeStatus == 2">过期</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="保质期(天)" fixed="" show-overflow-tooltip="">
            <template #default="scope">
              {{ calculateRemainingDays(scope.row.expirationTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="expirationTime" label="到期时间" fixed="" show-overflow-tooltip="">
            <template #default="scope">
              {{ formatDate(scope.row.expirationTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="cost" label="批次成本" fixed="" show-overflow-tooltip="" />
        </el-table>
      </el-form>
      <!-- 			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" size="default">取 消</el-button>
					<el-button type="primary" @click="submit" size="default">确 定</el-button>
				</span>
			</template> -->
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { addwarehousebatch, updatewarehousebatch } from "/@/api/main/warehousebatch";
import { pagewarehousebatch, deletewarehousebatch } from '/@/api/main/warehousebatch';
//父级传递来的参数
var props = defineProps({
  title: {
    type: String,
    default: "",
  },
});
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const ruleFormRef = ref();
const isShowDialog = ref(false);
const ruleForm = ref<any>({});
let tableDataMX = ref<any>([]);
//自行添加其他规则
const rules = ref<FormRules>({
});

// 打开弹窗
const openDialog = async (row: any) => {
  console.log(row, '-----------')
  ruleForm.value = JSON.parse(JSON.stringify(row));
  queryParams.value = ruleForm.value;
  /*  var res =  pagewarehousebatch(row);
   tableDataMX.value = res.result?.items ?? [];  */
  handleQuery();


};
const queryParams = ref<any>
  ({});
const tableParams = ref({
  page: 1,
  pageSize: 10,
  total: 0,
});
// 查询操作
const handleQuery = async () => {
  var res = await pagewarehousebatch(Object.assign(queryParams.value, tableParams.value));
  tableDataMX.value = res.data.result?.items ?? [];
  tableDataMX.value.forEach(item => {
    item.tradeName = queryParams.value.tradeName
  })
  isShowDialog.value = true;
};

// 关闭弹窗
const closeDialog = () => {
  emit("reloadTable");
  isShowDialog.value = false;
};

// 取消
const cancel = () => {
  isShowDialog.value = false;
};

// 提交
const submit = async () => {
  ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
    if (isValid) {
      let values = ruleForm.value;
      if (ruleForm.value.id != undefined && ruleForm.value.id > 0) {
        await updatewarehousebatch(values);
      } else {
        await addwarehousebatch(values);
      }
      closeDialog();
    } else {
      ElMessage({
        message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
        type: "error",
      });
    }
  });
};

// 计算剩余天数
const calculateRemainingDays = (expirationTime: string) => {
  if (!expirationTime) return 0;
  const today = new Date();
  const expDate = new Date(expirationTime);
  const diffTime = expDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

// 在 script 部分添加日期格式化函数
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  return dateString.split(' ')[0];
};

// 页面加载时
onMounted(async () => {
  //	handleQuery();
});

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>




