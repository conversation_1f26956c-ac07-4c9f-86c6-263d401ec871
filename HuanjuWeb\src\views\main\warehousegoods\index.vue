﻿<template>
  <div class="warehousegoods-container">
    <el-card class="query-form" shadow="hover" :body-style="{ paddingBottom: '0' }">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="商品名称">
          <el-input v-model="queryParams.name" clearable="" placeholder="请输入商品名称" />

        </el-form-item>
        <el-form-item label="编码">
          <el-input v-model="queryParams.code" clearable="" placeholder="请输入编码" />

        </el-form-item>
        <el-form-item>
          <el-button-group>
            <el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'warehousegoods:page'"> 查询
            </el-button>
            <el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>

          </el-button-group>

        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="ele-Plus" @click="openAddwarehousegoods" v-auth="'warehousegoods:add'"> 新增
          </el-button>
          <el-button type="primary" icon="ele-Upload" v-auth="'warehousegoods:import'" @Click="handleImport"> 导入
          </el-button>
          <input type="file" ref="fileInput" style="display: none;" @change="onFileSelected" accept=".xls,.xlsx">
        </el-form-item>

      </el-form>
    </el-card>
    <el-card class="full-table" shadow="hover" style="margin-top: 8px">
      <el-table :data="tableData" style="width: 100%" v-loading="loading" tooltip-effect="light" row-key="id" border="">
        <el-table-column type="index" label="序号" width="55" align="center" fixed="" />
        <el-table-column prop="name" label="商品名称" fixed="" show-overflow-tooltip="" />
        <el-table-column prop="brand" label="品牌" fixed="" show-overflow-tooltip="" />
        <el-table-column prop="code" label="编码" fixed="" show-overflow-tooltip="" />
        <el-table-column prop="specs" label="规格" fixed="" show-overflow-tooltip="" />

        <el-table-column prop="unitName" label="单位" fixed="" show-overflow-tooltip="" />
        <el-table-column prop="barcode" label="商品条码" fixed="" show-overflow-tooltip="" />
        <!--         <el-table-column prop="isbatch" label="是否批次 " fixed="" show-overflow-tooltip="" /> -->

        <el-table-column prop="isbatch" label="是否批次" fixed="" show-overflow-tooltip="">
          <template #default="scope">
            <el-tag v-if="scope.row.isbatch"> 是 </el-tag>
            <el-tag type="danger" v-else> 否 </el-tag>

          </template>
        </el-table-column>

        <el-table-column min-width="90" prop="expirationDate" label="保质期（天）" fixed="" show-overflow-tooltip="" />
        <el-table-column min-width="90" prop="expiryReminder" label="过期提醒(天)" fixed="" show-overflow-tooltip="" />

        <el-table-column prop="isuniquecode" label="是否唯一码" fixed="" show-overflow-tooltip="">
          <template #default="scope">
            <el-tag v-if="scope.row.isuniquecode"> 是 </el-tag>
            <el-tag type="danger" v-else> 否 </el-tag>

          </template>
        </el-table-column>

        <el-table-column prop="vacancy" label="是否负库存" fixed="" show-overflow-tooltip="">
          <template #default="scope">
            <el-tag v-if="scope.row.vacancy"> 是 </el-tag>
            <el-tag type="danger" v-else> 否 </el-tag>

          </template>
        </el-table-column>

        <el-table-column prop="auxiliaryunitName" label="辅助单位 " fixed="" show-overflow-tooltip="" />
        <el-table-column prop="convertCount" label="包装比例" fixed="" show-overflow-tooltip="" />
        <!--      <el-table-column prop="inventoryCount" label="库存数量" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="alarmMin" label="库存告警(低)" fixed="" show-overflow-tooltip="" />
         <el-table-column prop="alarmMax" label="库存告警(高)" fixed="" show-overflow-tooltip="" /> -->
        <el-table-column prop="remark" label="备注" fixed="" show-overflow-tooltip="" />
        <el-table-column label="操作" width="140" align="center" fixed="right" show-overflow-tooltip=""
          v-if="auth('warehousegoods:edit') || auth('warehousegoods:delete')">
          <template #default="scope">
            <el-button icon="ele-Edit" size="small" text="" type="primary" @click="openEditwarehousegoods(scope.row)"
              v-auth="'warehousegoods:edit'"> 编辑 </el-button>
            <el-button icon="ele-Delete" size="small" text="" type="primary" @click="delwarehousegoods(scope.row)"
              v-auth="'warehousegoods:delete'"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-model:currentPage="tableParams.page" v-model:page-size="tableParams.pageSize"
        :total="tableParams.total" :page-sizes="[10, 20, 50, 100]" small="" background=""
        @size-change="handleSizeChange" @current-change="handleCurrentChange"
        layout="total, sizes, prev, pager, next, jumper" />
      <editDialog ref="editDialogRef" :title="editwarehousegoodsTitle" @reloadTable="handleQuery" />
    </el-card>
  </div>
</template>

<script lang="ts" setup="" name="warehousegoods">
import { ref } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { auth } from '/@/utils/authFunction';
//import { formatDate } from '/@/utils/formatTime';

import editDialog from '/@/views/main/warehousegoods/component/editDialog.vue'
import { pagewarehousegoods, deletewarehousegoods, ImportWarehouseGoods } from '/@/api/main/warehousegoods';
import { useGoodsStore } from '/@/stores/goods';


const editDialogRef = ref();
const loading = ref(false);
const tableData = ref<any>
  ([]);
const queryParams = ref<any>
  ({});
const tableParams = ref({
  page: 1,
  pageSize: 10,
  total: 0,
});
const editwarehousegoodsTitle = ref("");
const fileInput = ref<HTMLInputElement | null>(null);
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB limit

const goodsStore = useGoodsStore();

const handleImport = () => {
  fileInput.value?.click();
};

const onFileSelected = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0];
  if (!file) return;

  if (file.size > MAX_FILE_SIZE) {
    ElMessage.error(`文件大小不能超过 ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
    if (fileInput.value) {
      fileInput.value.value = ''; // Reset file input
    }
    return;
  }

  const formData = new FormData();
  formData.append('file', file);

  try {
    loading.value = true;
    ElMessage.info("正在导入数据，请稍候...");
    await ImportWarehouseGoods(formData);
    ElMessage.success("导入成功");
    handleQuery();
  } catch (error) {
    //ElMessage.error("导入失败，请检查文件格式是否正确");
  } finally {
    loading.value = false;
    if (fileInput.value) {
      fileInput.value.value = ''; // Reset file input
    }
  }
};

// 查询操作
const handleQuery = async () => {
  loading.value = true;
  var res = await pagewarehousegoods(Object.assign(queryParams.value, tableParams.value));
  tableData.value = res.data.result?.items ?? [];
  tableParams.value.total = res.data.result?.total;
  loading.value = false;
};
// 重置查询条件
const resetQuery = () => {
  queryParams.value = {};
  handleQuery();
};
// 打开新增页面
const openAddwarehousegoods = () => {
  editwarehousegoodsTitle.value = '添加商品信息';
  editDialogRef.value.openDialog({});
};

// 打开编辑页面
const openEditwarehousegoods = (row: any) => {
  editwarehousegoodsTitle.value = '编辑商品信息';
  editDialogRef.value.openDialog(row);
};

// 删除
const delwarehousegoods = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      await deletewarehousegoods(row);
      await goodsStore.refreshGoodsList();
      handleQuery();
      ElMessage.success("删除成功");
    })
    .catch(() => { });
};

// 改变页面容量
const handleSizeChange = (val: number) => {
  tableParams.value.pageSize = val;
  handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
  tableParams.value.page = val;
  handleQuery();
};

// 新增商品后
const handleAddSuccess = async () => {
  await goodsStore.refreshGoodsList();
  handleQuery();
};

// 编辑商品后
const handleEditSuccess = async () => {
  await goodsStore.refreshGoodsList();
  handleQuery();
};

handleQuery();
</script>
