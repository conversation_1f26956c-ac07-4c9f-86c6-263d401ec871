<template>
    <div class="editable-table-container">
        <el-table ref="tableRef" :data="paginatedData" :height="tableHeight" :row-class-name="rowClassName"
            v-loading="loading" tooltip-effect="light" row-key="id" border stripe highlight-current-row
            @row-click="handleRowClick" @selection-change="handleSelectionChange">
            <!-- 动态渲染表格列 -->
            <template v-for="(column, index) in columns" :key="column.prop || index">
                <!-- 选择列 -->
                <el-table-column v-if="column.type === 'selection'" type="selection" :width="column.width || 40" />

                <!-- 索引列 -->
                <el-table-column v-else-if="column.type === 'index'" type="index" :label="column.label || '序号'"
                    :width="column.width || 55" :align="column.align || 'center'" />

                <!-- 自定义插槽列 -->
                <el-table-column v-else-if="column.slot" :prop="column.prop" :label="column.label" :width="column.width"
                    :align="column.align" :fixed="column.fixed"
                    :show-overflow-tooltip="column.showOverflowTooltip !== false">
                    <template #default="scope">
                        <div v-if="editingIndex === scope.$index && column.editable">
                            <slot :name="`${column.slot}-edit`" :row="scope.row" :index="scope.$index" :editing="true">
                                <el-input v-model="scope.row[column.prop]" size="small" />
                            </slot>
                        </div>
                        <slot v-else :name="column.slot" :row="scope.row" :index="scope.$index" :editing="false">
                            {{ scope.row[column.prop] }}
                        </slot>
                    </template>
                </el-table-column>

                <!-- 操作列 -->
                <el-table-column v-else-if="column.type === 'operation'" :label="column.label || '操作'"
                    :width="column.width || 140" :align="column.align || 'center'" :fixed="column.fixed || 'right'"
                    :show-overflow-tooltip="column.showOverflowTooltip !== false">
                    <template #default="scope">
                        <template v-if="editingIndex === scope.$index">
                            <el-button icon="ele-Check" size="small" text="" type="success"
                                @click="saveEdit(scope.$index)">
                                保存
                            </el-button>
                            <el-button icon="ele-Close" size="small" text="" type="danger" @click="cancelEdit">
                                取消
                            </el-button>
                        </template>
                        <template v-else>
                            <slot name="operation" :row="scope.row" :index="scope.$index">
                                <el-button icon="ele-Edit" size="small" text="" type="primary"
                                    @click="editItem(scope.$index, scope.row)">
                                    编辑
                                </el-button>
                                <el-button icon="ele-Delete" size="small" text="" type="primary"
                                    @click="deleteItem(scope.$index)">
                                    删除
                                </el-button>
                            </slot>
                        </template>
                    </template>
                </el-table-column>

                <!-- 普通列 -->
                <el-table-column v-else :prop="column.prop" :label="column.label" :width="column.width"
                    :align="column.align" :fixed="column.fixed" :sortable="column.sortable"
                    :show-overflow-tooltip="column.showOverflowTooltip !== false" />
            </template>
        </el-table>

        <!-- 分页组件 -->
        <el-pagination v-if="showPagination" v-model:currentPage="localPageParams.page"
            v-model:page-size="localPageParams.pageSize" :total="localPageParams.total" :page-sizes="[10, 20, 50, 100]"
            small background layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, nextTick, PropType } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
    tableData: {
        type: Array,
        required: true,
        default: () => []
    },
    columns: {
        type: Array as PropType<EditableColumnType[]>,
        required: true
    },
    loading: {
        type: Boolean,
        default: false
    },
    tableHeight: {
        type: [String, Number],
        default: '100%'
    },
    showPagination: {
        type: Boolean,
        default: true
    },
    // 验证规则，用于检查必填字段
    requiredFields: {
        type: Array,
        default: () => []
    },
    // 验证失败消息
    validationMessage: {
        type: String,
        default: '必填字段不能为空'
    }
});

const emit = defineEmits([
    'update:tableData',
    'row-click',
    'selection-change',
    'size-change',
    'current-change'
]);

const tableRef = ref();
const editingIndex = ref(-1); // 当前正在编辑的行索引，-1表示没有行在编辑
const editingRow = ref(null); // 保存正在编辑行的原始数据副本
const isNewRow = ref(false); // 新增标记：当前编辑的是否是新增行

// 本地分页参数
const localPageParams = reactive({
    page: 1,
    pageSize: 10,
    total: 0
});

watch(() => props.tableData, () => {
    localPageParams.total = props.tableData.length;
}, { immediate: true });

// 分页数据
const paginatedData = computed(() => {
    if (!props.showPagination) {
        return props.tableData;
    }
    const start = (localPageParams.page - 1) * localPageParams.pageSize;
    const end = start + localPageParams.pageSize;
    return props.tableData.slice(start, end);
});

// 行类名
const rowClassName = ({ row, rowIndex }: any) => {
    return editingIndex.value === rowIndex ? 'editing-row' : '';
};

// 事件处理
const handleRowClick = (row: any, column: any, event: any) => {
    emit('row-click', row, column, event);
};

const handleSelectionChange = (selection: any) => {
    emit('selection-change', selection);
};

const handleSizeChange = (val: number) => {
    localPageParams.pageSize = val;
    localPageParams.page = 1;
    emit('size-change', val);
};

const handleCurrentChange = (val: number) => {
    localPageParams.page = val;
    emit('current-change', val);
};

// 滚动到新行
const scrollToNewRow = (row: any) => {
    nextTick(() => {
        if (tableRef.value) {

            const tr = tableRef.value.$refs.tableBody.querySelector('tbody').firstElementChild;

            if (tr) {
                const rowHeight = tr.offsetHeight || 40;
                const index = paginatedData.value.indexOf(row);
                if (index !== -1) {
                    const targetPosition = index * rowHeight;
                    tableRef.value.setScrollTop(targetPosition);
                }
            }
        }
    });
};

// 检查编辑状态
const checkEditStatus = () => {
    if (editingIndex.value !== -1) {
        ElMessage.warning('请先完成当前编辑');
        return false;
    }
    return true;
};

// 添加新行
const addItem = (newItemTemplate = {}) => {
    // 检查编辑状态
    if (!checkEditStatus()) {
        return false;
    }

    // 创建新行并添加到数据中
    const newItem = {
        id: Date.now(), // 临时ID
        ...newItemTemplate
    };
    const index = props.tableData.length;
    // 保存原始数据副本
    editingRow.value = null; // 新行不需要保存原始数据
    editingIndex.value = index;
    isNewRow.value = true; // 标记为新行
    // 使用新数组以避免直接修改props
    const updatedData = [...props.tableData, newItem];
    emit('update:tableData', updatedData, index);

    // 设置新行为编辑状态，并标记为新行
    nextTick(() => {
        // 如果添加的行不在当前页，则跳转到最后一页
        if (props.showPagination) {
            if (localPageParams.pageSize > props.tableData.length) {
                localPageParams.page = 1;
            } else {
                const lastPage = Math.ceil(props.tableData.length / localPageParams.pageSize);
                if (localPageParams.page !== lastPage) {
                    localPageParams.page = lastPage;
                }
            }
        }

    }).then(() => {
        nextTick(() => {
            // 滚动到新行位置
            scrollToNewRow(newItem);
        });
    });

    return true;
};

// 编辑行
const editItem = (index: number, row: any) => {
    // 检查编辑状态
    if (!checkEditStatus()) {
        return;
    }

    // 保存原始数据副本
    editingRow.value = JSON.parse(JSON.stringify(row));
    editingIndex.value = index;
    isNewRow.value = false; // 标记为非新行
};

// 保存编辑
const saveEdit = (index: number) => {
    // 验证数据有效性
    const currentRow = paginatedData.value[index];

    // 检查必填字段
    for (const field of props.requiredFields) {
        if (!currentRow[field]) {
            ElMessage.warning(props.validationMessage);
            return;
        }
    }

    // 计算在原始数据中的索引
    const originalIndex = (localPageParams.page - 1) * localPageParams.pageSize + index;

    // 更新原始数据
    const updatedData = [...props.tableData];
    updatedData[originalIndex] = currentRow;
    emit('update:tableData', updatedData, originalIndex);

    // 重置编辑状态
    editingIndex.value = -1;
    editingRow.value = null;
    isNewRow.value = false;
};

// 取消编辑
const cancelEdit = () => {
    if (isNewRow.value) {
        // 如果是新增行，则直接移除
        const index = editingIndex.value;
        const updatedData = [...props.tableData];
        updatedData.splice(index, 1);
        emit('update:tableData', updatedData, index);
    } else if (editingRow.value) {
        // 如果是编辑现有行，还原数据
        const index = editingIndex.value;
        const updatedData = [...props.tableData];
        updatedData[index] = editingRow.value;
        emit('update:tableData', updatedData, index);
    }

    // 重置编辑状态
    editingIndex.value = -1;
    editingRow.value = null;
    isNewRow.value = false;
};

// 删除行
const deleteItem = (index: number) => {
    ElMessageBox.confirm('确认删除该记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        // 计算在原始数据中的索引
        const originalIndex = (localPageParams.page - 1) * localPageParams.pageSize + index;

        // 更新原始数据
        const updatedData = [...props.tableData];
        updatedData.splice(originalIndex, 1);
        emit('update:tableData', updatedData, index);

        ElMessage.success('删除成功');
    }).catch(() => {
        // 用户取消删除
    });
};

// 重置编辑状态
const resetEditStatus = () => {
    editingIndex.value = -1;
    editingRow.value = null;
    isNewRow.value = false;
};

// 检查表格是否有编辑中的行以及数据有效性
const valid = () => {
    if (editingIndex.value !== -1) {
        ElMessage.warning('请先保存或取消当前编辑');
        return false;
    }

    // 检查数据有效性
    for (const item of props.tableData) {
        for (const field of props.requiredFields) {
            if (!item[field]) {
                ElMessage.warning(props.validationMessage);
                return false;
            }
        }
    }

    return true;
};

// 暴露方法
defineExpose({
    tableRef,
    addItem,
    editItem,
    deleteItem,
    resetEditStatus,
    valid
});
</script>

<style scoped>
.editable-table-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.el-pagination {
    margin-top: 12px;
    justify-content: flex-end;
}

:deep(.editing-row) {
    background-color: var(--el-fill-color-light) !important;
}
</style>