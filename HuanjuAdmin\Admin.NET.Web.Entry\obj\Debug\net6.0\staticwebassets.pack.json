{"Files": [{"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\CodeGen\\ImportTemplate\\Web\\src\\api\\main\\importTemplate.ts", "PackagePath": "staticwebassets\\CodeGen\\ImportTemplate\\Web\\src\\api\\main\\importTemplate.ts"}, {"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\CodeGen\\ImportTemplate\\Web\\src\\views\\main\\importTemplate\\component\\editDialog.vue", "PackagePath": "staticwebassets\\CodeGen\\ImportTemplate\\Web\\src\\views\\main\\importTemplate\\component\\editDialog.vue"}, {"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\CodeGen\\ImportTemplate\\Web\\src\\views\\main\\importTemplate\\index.vue", "PackagePath": "staticwebassets\\CodeGen\\ImportTemplate\\Web\\src\\views\\main\\importTemplate\\index.vue"}, {"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\Dto.cs.vm", "PackagePath": "staticwebassets\\Template\\Dto.cs.vm"}, {"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\Entity.cs.vm", "PackagePath": "staticwebassets\\Template\\Entity.cs.vm"}, {"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\Input.cs.vm", "PackagePath": "staticwebassets\\Template\\Input.cs.vm"}, {"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\Manage.js.vm", "PackagePath": "staticwebassets\\Template\\Manage.js.vm"}, {"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\Output.cs.vm", "PackagePath": "staticwebassets\\Template\\Output.cs.vm"}, {"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\Service.cs.vm", "PackagePath": "staticwebassets\\Template\\Service.cs.vm"}, {"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\data.data.ts.vm", "PackagePath": "staticwebassets\\Template\\data.data.ts.vm"}, {"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\dataModal.vue.vm", "PackagePath": "staticwebassets\\Template\\dataModal.vue.vm"}, {"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\editDialog.vue.vm", "PackagePath": "staticwebassets\\Template\\editDialog.vue.vm"}, {"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\Template\\index.vue.vm", "PackagePath": "staticwebassets\\Template\\index.vue.vm"}, {"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\images\\logo.png", "PackagePath": "staticwebassets\\images\\logo.png"}, {"Id": "D:\\hjcode\\HuanjuCode\\HuanjuAdmin\\Admin.NET.Web.Entry\\wwwroot\\upload\\Address\\13750818896581.jpg", "PackagePath": "staticwebassets\\upload\\Address\\13750818896581.jpg"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.Admin.NET.Web.Entry.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.build.Admin.NET.Web.Entry.props", "PackagePath": "build\\Admin.NET.Web.Entry.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildMultiTargeting.Admin.NET.Web.Entry.props", "PackagePath": "buildMultiTargeting\\Admin.NET.Web.Entry.props"}, {"Id": "obj\\Debug\\net6.0\\staticwebassets\\msbuild.buildTransitive.Admin.NET.Web.Entry.props", "PackagePath": "buildTransitive\\Admin.NET.Web.Entry.props"}], "ElementsToRemove": []}