/*
 * @Author: WDD
 * @Date: 2025-04-13 11:20:59
 * @LastEditors: WDD
 * @LastEditTime: 2025-04-27 17:18:55
 * @Description: file content
 */
export interface OrderTableData {
    id: number;
    orderNo: string;
    schemeId: string;
    createTime: string;
    materialWarehouseId: string;
    produceWarehouseId: string;
    materialList: MaterialTableData[];
    produceList: ProduceTableData[];
}

export interface MaterialTableData {
    id: number;
    materialCode: string;
    materialName: string;
    specification: string;
    unit: string;
    baseQuantity: number;
    actQuantity: number;
}

export interface ProduceTableData {
    id: number;
    productCode: string;
    productName: string;
    specification: string;
    unit: string;
    baseQuantity: number;
    actQuantity: number;
    theoreticalQuantity: number;
    productivity: string;
}