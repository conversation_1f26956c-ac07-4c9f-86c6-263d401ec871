﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 个人审批明细基础输入参数
    /// </summary>
    public class WorkflowRecordBaseInput
    {
        /// <summary>
        /// 审批单ID
        /// </summary>
        public virtual long OrderId { get; set; }
        
        /// <summary>
        /// 步骤编号
        /// </summary>
        public virtual int StepNumber { get; set; }
        
        /// <summary>
        /// 审批人
        /// </summary>
        public virtual long Approver { get; set; }
        
        /// <summary>
        /// 审批状态
        /// </summary>
        public virtual int Status { get; set; }
        
        /// <summary>
        /// 审批意见
        /// </summary>
        public virtual string? Remark { get; set; }
        
    }

    /// <summary>
    /// 个人审批明细分页查询输入参数
    /// </summary>
    public class WorkflowRecordInput : BasePageInput
    {
        /// <summary>
        /// 审批单ID
        /// </summary>
        public long OrderId { get; set; }
        
        /// <summary>
        /// 步骤编号
        /// </summary>
        public int StepNumber { get; set; }
        
        /// <summary>
        /// 审批人
        /// </summary>
        public long Approver { get; set; }
        
        /// <summary>
        /// 审批状态
        /// </summary>
        public ApproveStatusEnum Status { get; set; }
        
        /// <summary>
        /// 审批意见
        /// </summary>
        public string? Remark { get; set; }
        
    }

    /// <summary>
    /// 个人审批明细增加输入参数
    /// </summary>
    public class AddWorkflowRecordInput : WorkflowRecordBaseInput
    {
    }

    /// <summary>
    /// 个人审批明细删除输入参数
    /// </summary>
    public class DeleteWorkflowRecordInput : BaseIdInput
    {
    }

    /// <summary>
    /// 个人审批明细更新输入参数
    /// </summary>
    public class UpdateWorkflowRecordInput : WorkflowRecordBaseInput
    {
        /// <summary>
        /// Id
        /// </summary>
        [Required(ErrorMessage = "Id不能为空")]
        public long Id { get; set; }
        
    }

    /// <summary>
    /// 个人审批明细主键查询输入参数
    /// </summary>
    public class QueryByIdWorkflowRecordInput : DeleteWorkflowRecordInput
    {

    }
