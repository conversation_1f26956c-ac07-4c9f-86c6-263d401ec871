<template>
    <el-card class="top-Card" shadow="hover" :body-style="{ height: '100%' }">
					<el-table
						ref="taskTableRef"
						:data="tableData"
						class="top-table"
						:row-class-name="rowClassName"
						v-loading="loading"
						tooltip-effect="light"
						row-key="id"
						border=""
						@row-click="handleRowClick"
						@row-dblclick="handleRowDblclick"
						highlight-current-row
						v-model:selection="selectedRows"
						@selection-change="changeSelect"
						stripe
						:height="tableHeight"
						@expand-change="handleExpandChange"
					>
						<!-- 动态渲染表格列 -->
						<template v-for="(column, index) in columns">
							<!-- 选择列 -->
							<el-table-column v-if="column.type === 'selection'" :key="'selection-'+index" type="selection" :width="column.width || 40" />
							
							<!-- 索引列 -->
							<el-table-column v-else-if="column.type === 'index'" :key="'index-'+index" type="index" :label="column.label || '序号'" :width="column.width || 55" :align="column.align || 'center'" />
							
							<!-- 展开列 -->
							<el-table-column v-else-if="column.type === 'expand'" :key="'expand-'+index" type="expand" :width="column.width || 40" >
								<template #default="scope">
									<slot :name="column.slot" :row="scope.row" :index="scope.$index"></slot>
								</template>
							</el-table-column>

							<!-- 带自定义插槽的列 -->
							<el-table-column
								v-else-if="column.slot"
								:key="'slot-'+column.prop+'-'+index"
								:prop="column.prop"
								:label="column.label"
								:width="column.width"
								:align="column.align"
								:fixed="column.fixed"
								:show-overflow-tooltip="column.showOverflowTooltip !== false"
							>
								<template #default="scope">
									<slot :name="column.slot" :row="scope.row" :index="scope.$index"></slot>
								</template>
							</el-table-column>
							
							<!-- 操作列 -->
							<el-table-column
								v-else-if="column.type === 'operation'"
								:key="'operation-'+index"
								:label="column.label || '操作'"
								:width="column.width || 140"
								:align="column.align || 'center'"
								:fixed="column.fixed || 'right'"
								:show-overflow-tooltip="column.showOverflowTooltip !== false"
							>
								<template #default="scope">
									<slot name="operation" :row="scope.row" :index="scope.$index"></slot>
								</template>
							</el-table-column>
							
							<!-- 普通列 -->
							<el-table-column
								v-else
								:key="column.prop+'-'+index"
								:prop="column.prop"
								:label="column.label"
								:width="column.width"
								:align="column.align"
								:fixed="column.fixed"
								:sortable="column.sortable"
								:show-overflow-tooltip="column.showOverflowTooltip !== false"
							/>
						</template>
					</el-table>
					<el-pagination
						v-model:currentPage="localTableParams.page"
						v-model:page-size="localTableParams.pageSize"
						:total="localTableParams.total"
						:page-sizes="[10, 20, 50, 100]"
						small=""
						background=""
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
						layout="total, sizes, prev, pager, next, jumper"
					/>
				</el-card>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, reactive, watch } from 'vue';

// 定义props
const props = defineProps({
	// 表格数据
	tableData: {
		type: Array,
		default: () => []
	},
	// 表格列配置
	columns: {
		type: Array as () => TableColumn[],
		required: true
	},
	// 表格高度
	tableHeight: {
		type: [String, Number],
		default: 'calc(100% - 24px - var(--el-card-padding))'
	},
	// 加载状态
	loading: {
		type: Boolean,
		default: false
	},
	// 表格参数
	tableParams: {
		type: Object,
		default: () => ({
			page: 1,
			pageSize: 10,
			total: 0
		})
	}
});

// 选中行
const selectedRows = ref([]);

// 定义事件
const emit = defineEmits([
	'update:selection', 
	'row-click', 
	'selection-change', 
	'size-change', 
	'current-change',
	'update:tableParams',
	'row-dblclick',
	'expand-change'
]);

// 创建本地表格参数副本
const localTableParams = reactive({...props.tableParams});

// 监听props变化更新本地数据
watch(() => props.tableParams, (newVal) => {
  Object.assign(localTableParams, newVal);
}, { deep: true });

// 行类名
// eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
const rowClassName = ({ row, rowIndex }:any) => {
	return '';
};

// 处理行点击
const handleRowClick = (row:any, column:any, event:any) => {
	emit('row-click', row, column, event);
};

// 处理行双击
const handleRowDblclick = (row:any, column:any, event:any) => {
	emit('row-dblclick', row, column, event);
};

// 处理选择变化
const changeSelect = (selection:any) => {
	emit('selection-change', selection);
	emit('update:selection', selection);
};

// 处理每页条数变化
const handleSizeChange = (val:any) => {
	localTableParams.pageSize = val;
	emit('update:tableParams', localTableParams);
	emit('size-change', val);
};

// 处理页码变化
const handleCurrentChange = (val:any) => {
	localTableParams.page = val;
	emit('update:tableParams', localTableParams);
	emit('current-change', val);
};

// 暴露表格引用和展开行方法
const taskTableRef = ref();

// 展开/折叠行
const toggleRowExpansion = (row:any, expanded:any) => {
    if (taskTableRef.value) {
        taskTableRef.value.toggleRowExpansion(row, expanded);
    }
};

const handleExpandChange = (row:any, expanded:any) => {
	emit('expand-change', row, expanded);
};

defineExpose({
    toggleRowExpansion
});

</script>

<style scoped>
.top-Card {
	margin-bottom: 8px;
}
</style>