-- 调试GoodProduct字段数据的查询脚本

-- 1. 检查View_OutInBound视图中的GoodProduct字段
SELECT 'View_OutInBound视图数据:' as title;
SELECT 
    Id,
    Type,
    OrderNum,
    GoodProduct,
    HEX(GoodProduct) as GoodProduct_HEX,
    CAST(GoodProduct AS UNSIGNED) as GoodProduct_INT,
    CASE 
        WHEN GoodProduct = 1 THEN '良品'
        WHEN GoodProduct = 0 THEN '次品'
        WHEN GoodProduct IS NULL THEN 'NULL'
        ELSE CONCAT('未知值:', GoodProduct)
    END as ProductType,
    CreateTime
FROM View_OutInBound 
ORDER BY CreateTime DESC 
LIMIT 10;

-- 2. 检查outboundrecord表的原始数据
SELECT 'outboundrecord表数据:' as title;
SELECT 
    Id,
    OrderNum,
    GoodProduct,
    HEX(GoodProduct) as GoodProduct_HEX,
    CAST(GoodProduct AS UNSIGNED) as GoodProduct_INT,
    CASE 
        WHEN GoodProduct = 1 THEN '良品'
        WHEN GoodProduct = 0 THEN '次品'
        WHEN GoodProduct IS NULL THEN 'NULL'
        ELSE CONCAT('未知值:', GoodProduct)
    END as ProductType,
    CreateTime
FROM outboundrecord 
ORDER BY CreateTime DESC 
LIMIT 5;

-- 3. 检查inboundrecord表的原始数据
SELECT 'inboundrecord表数据:' as title;
SELECT 
    Id,
    OrderNum,
    GoodProduct,
    HEX(GoodProduct) as GoodProduct_HEX,
    CAST(GoodProduct AS UNSIGNED) as GoodProduct_INT,
    CASE 
        WHEN GoodProduct = 1 THEN '良品'
        WHEN GoodProduct = 0 THEN '次品'
        WHEN GoodProduct IS NULL THEN 'NULL'
        ELSE CONCAT('未知值:', GoodProduct)
    END as ProductType,
    CreateTime
FROM inboundrecord 
ORDER BY CreateTime DESC 
LIMIT 5;

-- 4. 检查GoodProduct字段的数据类型
SELECT 'GoodProduct字段类型信息:' as title;
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_TYPE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND COLUMN_NAME = 'GoodProduct'
AND TABLE_NAME IN ('outboundrecord', 'inboundrecord', 'View_OutInBound');

-- 5. 特别检查最新的入库记录
SELECT '最新入库记录详细信息:' as title;
SELECT 
    i.*,
    CASE 
        WHEN i.GoodProduct = 1 THEN '良品'
        WHEN i.GoodProduct = 0 THEN '次品'
        WHEN i.GoodProduct IS NULL THEN 'NULL'
        ELSE CONCAT('未知值:', i.GoodProduct)
    END as ProductType
FROM inboundrecord i
WHERE i.CreateTime >= CURDATE() - INTERVAL 7 DAY
ORDER BY i.CreateTime DESC
LIMIT 3; 