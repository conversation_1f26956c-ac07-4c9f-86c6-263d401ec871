﻿import request from '/@/utils/request';
enum Api {
	AddTradingAccounts = '/api/tradingAccounts/add',
	DeleteTradingAccounts = '/api/tradingAccounts/delete',
	UpdateTradingAccounts = '/api/tradingAccounts/update',
	PageTradingAccounts = '/api/tradingAccounts/page',
	TradingAccountDropdown = '/api/tradingAccounts/tradingAccountDropdown',
	SubjectList = '/api/Subject/List',
}

// 增加交易账户
export const addTradingAccounts = (params?: any) =>
	request({
		url: Api.AddTradingAccounts,
		method: 'post',
		data: params,
	});

// 删除交易账户
export const deleteTradingAccounts = (params?: any) =>
	request({
		url: Api.DeleteTradingAccounts,
		method: 'post',
		data: params,
	});

// 编辑交易账户
export const updateTradingAccounts = (params?: any) =>
	request({
		url: Api.UpdateTradingAccounts,
		method: 'post',
		data: params,
	});

// 分页查询交易账户
export const pageTradingAccounts = (params?: any) =>
	request({
		url: Api.PageTradingAccounts,
		method: 'post',
		data: params,
	});


// 交易账户下拉框
export const tradingAccountDropdown = (params?: any) =>
	request({
		url: Api.TradingAccountDropdown,
		method: 'get',
		data: params,
	});

// 分页查询科目信息
export const SubjectList = (params?: any) =>
	request({
		url: Api.SubjectList,
		method: 'get',
		data: params,
	});
