﻿using System;

namespace Admin.NET.Application;

/// <summary>
/// 销售合约输出参数
/// </summary>
public class SalesContractDto
{
    /// <summary>
    /// 销售人
    /// </summary>
    public string Salesperson { get; set; }

    /// <summary>
    /// 销售单号
    /// </summary>
    public string SalesOrder { get; set; }

    /// <summary>
    /// 合同编码
    /// </summary>
    public string ContractCode { get; set; }

    /// <summary>
    /// 合同状态
    /// </summary>
    public YesNoEnum ContractStatus { get; set; }

    /// <summary>
    /// 总金额
    /// </summary>
    public decimal TotalAmt { get; set; }

    /// <summary>
    /// 优惠金额
    /// </summary>
    public decimal DiscountAmt { get; set; }

    /// <summary>
    /// 合同金额
    /// </summary>
    public decimal ContractAmount { get; set; }

    /// <summary>
    /// 签订时间
    /// </summary>
    public DateTime? SigningTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 客户名称
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    public string? Contacts { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public string? Tel { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// 已收金额
    /// </summary>
    public decimal? AmountReceived { get; set; }

    /// <summary>
    /// 已开票金额
    /// </summary>
    public decimal? InvoicedAmount { get; set; }

    /// <summary>
    /// 是否缺货
    /// </summary>
    public bool? OutOfStock { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// 附件
    /// </summary>
    public string? Annex { get; set; }

}
