﻿using System.ComponentModel.DataAnnotations;

namespace Admin.NET.Application;

    /// <summary>
    /// 供应商管理基础输入参数
    /// </summary>
    public class PubSupplierBaseInput
    {
        /// <summary>
        /// 供应商名称
        /// </summary>
        public virtual string Name { get; set; }
        
        /// <summary>
        /// 供应商类型
        /// </summary>
        public virtual string? Type { get; set; }
        
        /// <summary>
        /// 联系人
        /// </summary>
        public virtual string Contacts { get; set; }
        
        /// <summary>
        /// 电话
        /// </summary>
        public virtual string Phone { get; set; }
        
        /// <summary>
        /// 公用标志
        /// </summary>
        public virtual bool IsCommunal { get; set; }
        
        /// <summary>
        /// 开户行
        /// </summary>
        public virtual string? BankName { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public virtual string? Remark { get; set; }
        
        /// <summary>
        /// 银行账号
        /// </summary>
        public virtual string? BankCode { get; set; }
        
        /// <summary>
        /// 税务登记号
        /// </summary>
        public virtual string? TaxId { get; set; }
        
        /// <summary>
        /// 地址
        /// </summary>
        public virtual string? Address { get; set; }
        
    }

    /// <summary>
    /// 供应商管理分页查询输入参数
    /// </summary>
    public class PubSupplierInput : BasePageInput
    {
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 供应商类型
        /// </summary>
        public string? Type { get; set; }
        
        /// <summary>
        /// 联系人
        /// </summary>
        public string Contacts { get; set; }
        
        /// <summary>
        /// 电话
        /// </summary>
        public string Phone { get; set; }
        
        /// <summary>
        /// 公用标志
        /// </summary>
        public bool IsCommunal { get; set; }
        
        /// <summary>
        /// 税务登记号
        /// </summary>
        public string? TaxId { get; set; }
        
    }

    /// <summary>
    /// 供应商管理增加输入参数
    /// </summary>
    public class AddPubSupplierInput : PubSupplierBaseInput
    {
    }

    /// <summary>
    /// 供应商管理删除输入参数
    /// </summary>
    public class DeletePubSupplierInput : BaseIdInput
    {
    }

    /// <summary>
    /// 供应商管理更新输入参数
    /// </summary>
    public class UpdatePubSupplierInput : PubSupplierBaseInput
    {
        /// <summary>
        /// Id
        /// </summary>
        [Required(ErrorMessage = "Id不能为空")]
        public long Id { get; set; }
        
    }

    /// <summary>
    /// 供应商管理主键查询输入参数
    /// </summary>
    public class QueryByIdPubSupplierInput : DeletePubSupplierInput
    {

    }
